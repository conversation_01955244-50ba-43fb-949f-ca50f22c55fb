import { inject } from '@angular/core';

import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';

import { Observable } from 'rxjs';
import { SchoolEventManagerService } from '../../sharedServices';

export const EventViewResolver: ResolveFn<any> = (route: ActivatedRouteSnapshot): Observable<any> => {
  const eventService = inject(SchoolEventManagerService);
  const id = route.params['id'];

  return id ? eventService.GetEventView(id) : null;
};
