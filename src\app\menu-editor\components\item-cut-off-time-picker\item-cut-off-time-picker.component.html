<form *ngIf="form" [formGroup]="form">
  <div class="col-12">
    <h3 class="subTitle" [ngClass]="{'newColor': newTheme}">Specific cut off time</h3>

    <input-checkbox
      formControlName="hasCutOffTime"
      placeholder="This item has a specific cut-off time"
    ></input-checkbox>

    <div *ngIf="hasCutOffTime.value" class="mt-3">
      <mat-form-field class="timeInput" appearance="outline">
        <mat-select formControlName="cutOffTimeType">
          <mat-option [value]="ItemCutOffTimeTypes.Early">{{ ItemCutOffTimeTypes.Early }}</mat-option>
          <mat-option [value]="ItemCutOffTimeTypes.Late">{{ ItemCutOffTimeTypes.Late }}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field class="timeInput" appearance="outline">
        <mat-select formControlName="cutOffTime">
          <mat-option *ngFor="let val of CUT_OFF_TIME_LIST" [value]="val">{{
            val | cutOffTimeFormat
          }}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>
</form>
