<div class="row">
  <div class="col-12">
    <h3>
      Errors
      <span *ngIf="errors">( {{ errors.length }} )</span>
    </h3>
  </div>
</div>

<div *ngFor="let err of errors" class="row errorRow">
  <div class="col-12">
    <h4>
      {{ err.errorMessage }} <span>(order id: {{ err.orderId }})</span>
    </h4>
    <p class="subTitle">{{ err.parentName }} - {{ err.studentName }}</p>
    <p class="subTitle">Logic app: {{ err.logicAppIdentifier }}</p>
  </div>
</div>
