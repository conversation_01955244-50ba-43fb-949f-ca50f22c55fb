import { KeyValue } from '@angular/common';
import { Component, Input, OnInit, Output, SimpleChanges, EventEmitter } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

//Models
import { Days, getSelectedWeekdaysFromForm, School } from 'src/app/sharedModels';
import { getWeekDayKeyValue } from 'src/app/sharedModels/base/KeyValueConversion';

@Component({
  selector: 'opening-days-form',
  templateUrl: './opening-days-form.component.html',
  styleUrls: ['./opening-days-form.component.scss'],
})
export class OpeningDaysFormComponent implements OnInit {
  @Input() school: School;
  @Output() updateSchool: EventEmitter<School> = new EventEmitter();
  form: FormGroup;
  daysHelper: Days;
  checkBoxValues: KeyValue<string, string>[] = [];

  constructor() {}

  ngOnInit(): void {
    this.checkBoxValues = getWeekDayKeyValue();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.school?.currentValue) {
      if (this.school.Name) {
        this._createForm();
      }
    }
  }

  private _createForm() {
    this.daysHelper = new Days(this.school.OpeningDays);

    this.form = new FormGroup({
      Monday: new FormControl(this.daysHelper.IsAvailableMonday()),
      Tuesday: new FormControl(this.daysHelper.IsAvailableTuesday()),
      Wednesday: new FormControl(this.daysHelper.IsAvailableWednesday()),
      Thursday: new FormControl(this.daysHelper.IsAvailableThursday()),
      Friday: new FormControl(this.daysHelper.IsAvailableFriday()),
    });
  }

  submitForm() {
    let dayString = getSelectedWeekdaysFromForm(this.form);

    this.school.OpeningDays = dayString.length > 0 ? dayString.substring(0, dayString.length - 1) : '';
    this.updateSchool.emit(this.school);
  }
}
