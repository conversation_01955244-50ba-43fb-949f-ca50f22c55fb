import { ChangeDetectionStrategy, Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedToolsModule } from 'src/app/shared-tools/shared-tools.module';
import { BaseComponent, Canteen, MerchantTypeEnum, SchoolEvent } from 'src/app/sharedModels';
import { EventRowComponent } from '../event-row/event-row.component';
import { SchoolsButtonModule } from 'src/app/schools-button/schools-button.module';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { SharedModule } from 'src/app/shared/shared.module';
import { EventListComponent } from '../event-list/event-list.component';
import { DebounceService, SchoolEventManagerService } from 'src/app/sharedServices';

@Component({
  selector: 'event-list-preview-page',
  standalone: true,
  imports: [
    CommonModule,
    SharedToolsModule,
    EventRowComponent,
    SchoolsButtonModule,
    SharedModule,
    EventListComponent,
  ],
  templateUrl: './event-list-preview-page.component.html',
  styleUrls: ['./event-list-preview-page.component.scss'],
  // changeDetection: ChangeDetectionStrategy.OnPush, //TODO: bring this back
})
export class EventListPreviewPageComponent extends BaseComponent {
  eventList: SchoolEvent[];
  schoolId: number;
  merchantId: number;
  loading: boolean;
  merchantFilter: MerchantTypeEnum[] = [MerchantTypeEnum.Canteen, MerchantTypeEnum.Event];
  subscriptionManageEventState$: Subscription;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private debounceService: DebounceService,
    private schoolEventManagerService: SchoolEventManagerService
  ) {
    super();
  }

  onMerchantChange(event: Canteen): void {
    this.merchantId = event.CanteenId;
    this.getEventDataDebounce();
  }

  onSchoolSelect(event: number): void {
    this.schoolId = event;
    this.getEventDataDebounce();
  }

  getEventDataDebounce(): void {
    this.loading = true;
    this.eventDebounce();
  }

  /**
   * wait until both merchant and schoolId have loaded after a school/merchant change
   */
  eventDebounce = this.debounceService.callDebounce(this.getEventList, 200, false, true);

  getEventList(): void {
    this.schoolEventManagerService.GetEventsByMerchantAndSchool(this.merchantId, this.schoolId).subscribe({
      next: (res: SchoolEvent[]) => {
        this.loading = false;
        this.eventList = res;
      },
      error: error => {
        this.handleErrorFromService(error);
      },
    });
  }

  navToNewEvent(): void {
    this.router.navigate([`./create`], { relativeTo: this.route, queryParams: { schoolId: this.schoolId } });
  }

  navToSelectedEvent(eventId: number): void {
    this.router.navigate([`./view/${eventId}`], { relativeTo: this.route });
  }
}
