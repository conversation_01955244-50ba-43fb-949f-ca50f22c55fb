<mat-dialog-content>
  <modal-header [title]="displayNotice.Title" (close)="closeModal()"></modal-header>

  <p>Notice Id: {{ displayNotice.NoticeId }}</p>
  <p>Description: {{ displayNotice.Description }}</p>
  <p>Start Date: {{ displayNotice.StartDate | date : 'y/MMM/d' }}</p>
  <p>End Date: {{ displayNotice.EndDate | date : 'y/MMM/d' }}</p>
  <p>School Name: {{ displayNotice.SchoolName }}</p>

  <form [formGroup]="noticeDialogForm">
    <mat-form-field appearance="outline">
      <mat-label>Decline Reason</mat-label>
      <textarea matInput formControlName="declineReason"></textarea>
    </mat-form-field>
  </form>

  <div mat-dialog-actions>
    <button mat-button style="color: blue" (click)="submitForm(noticeStatusEnum.Validated)">Approve</button>

    <button mat-button (click)="submitForm(noticeStatusEnum.Refused)" [disabled]="!declineReason.value">
      Decline
    </button>
  </div>
</mat-dialog-content>
