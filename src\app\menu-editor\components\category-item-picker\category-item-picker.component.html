<div *ngIf="listCategories">
  <form *ngIf="form" [formGroup]="form">
    <mat-form-field appearance="outline">
      <mat-label>Category</mat-label>
      <mat-select formControlName="category" required>
        <mat-option *ngFor="let category of listCategories" [value]="category.MenuCategoryId">{{
          category.CategoryName
        }}</mat-option>
      </mat-select>
    </mat-form-field>
  </form>
</div>

<category-item-table
  [tableData]="menuItemsToDisplay"
  [selectedCategory]="selectedCategory"
  [selectedItems]="linkedItems"
  (addItem)="addItem($event)"
></category-item-table>
