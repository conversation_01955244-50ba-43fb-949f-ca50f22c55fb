import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { DialogTransferMoneyComponent } from './dialog-transfer-money.component';

describe('DialogTransferMoneyComponent', () => {
  let component: DialogTransferMoneyComponent;
  let fixture: ComponentFixture<DialogTransferMoneyComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [DialogTransferMoneyComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogTransferMoneyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
