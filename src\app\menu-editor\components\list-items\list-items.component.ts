import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { FormControl, FormGroup,} from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog } from '@angular/material/dialog';

// Models
import {
  Canteen,
  MenuItem,
  BaseComponent,
  ItemCategory,
  CanteenCategoriesMenus,
  ItemMenus,
} from 'src/app/sharedModels';

// Services
import { MenuService, MenuEditorService, MenuEditorApiService, SpinnerService } from 'src/app/sharedServices';

// Ngrx
import { Subscription } from 'rxjs';
import { Store, select } from '@ngrx/store';
import { LoadMenuCategories } from 'src/app/states/canteen/canteen.actions';
import { CanteenState } from 'src/app/states';
import { canteenStateSelector } from 'src/app/states/canteen/canteen.selectors';

@Component({
  selector: 'list-items',
  templateUrl: './list-items.component.html',
  styleUrls: ['../editor-table-page.scss', './list-items.component.scss'],
})
export class ListItemsComponent extends BaseComponent implements OnInit {
  listCategories: ItemCategory[] = [];
  listMenus: string[] = [];
  listItems: MenuItem[] = [];
  selectedCanteen: Canteen;
  form: FormGroup;
  listCanteens: Canteen[] = [];
  private _SelectedCanteen: Canteen;
  private subscription: Subscription;
  private allCategories = new ItemCategory();

  // table
  displayedColumns: string[] = ['image', 'name', 'price', 'IsActive', 'Menus', 'actions'];
  dataSource = new MatTableDataSource<MenuItem>();

  defaultValueSchool: number;

  constructor(
    private activatedRoute: ActivatedRoute,
    private menuService: MenuService,
    private menuEditorService: MenuEditorService,
    private menuEditorApiService: MenuEditorApiService,
    private spinnerService: SpinnerService,
    public dialog: MatDialog,
    private store: Store<{ canteen: CanteenState }>
  ) {
    super();
  }

  ngOnInit() {
    this.store.dispatch(LoadMenuCategories());

    this.allCategories.CategoryName = 'All Categories';
    this.allCategories.MenuCategoryId = 0;
    this.subscription = this.store.pipe(select(canteenStateSelector)).subscribe((state: CanteenState) => {
      this.listCanteens = state.list;

      if (state.selected) {
        this._SelectedCanteen = state.selected;
      }
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  get category() {
    return this.form.get('category');
  }

  get menu() {
    return this.form.get('menu');
  }

  /** Create the filter form */
  private _createForm() {
    // Manage category
    let categoryId = this.menuEditorService.GetSelectedCategoryId();
    let index = this.listCategories.findIndex(i => i.MenuCategoryId == categoryId);

    if (index == -1) {
      categoryId = 0;
    }

    this.form = new FormGroup({
      category: new FormControl(categoryId),
      menu: new FormControl('All Menus'),
    });

    this.FilterMenuList();

    this.category.valueChanges.subscribe(val => {
      this.menuEditorService.SetSelectedCategoryId(val);
      this.FilterMenuList();
    });

    this.menu.valueChanges.subscribe(val => {
      this.menuEditorService.SetSelectedMenu(val);
      this.FilterMenuList();
    });
  }

  private FilterMenuList() {
    const isMenuValue = this.menu.value == 'All Menus' ? null : this.menu.value;
    let filteredData = [];
    if (this.category.value && isMenuValue) {
      filteredData = this.listItems.filter(menuItem => {
        return (
          menuItem.CategoryId == this.category.value &&
          menuItem.Menus &&
          menuItem.Menus.find(menu => menu.MenuName == this.menu.value)
        );
      });
      this.dataSource.data = filteredData;
      return;
    }
    if (this.category.value && !isMenuValue) {
      filteredData = this.listItems.filter(menuItem => {
        return menuItem.CategoryId == this.category.value;
      });
      this.dataSource.data = filteredData;
      return;
    }
    if (!this.category.value && isMenuValue) {
      filteredData = this.listItems.filter(menuItem => {
        return menuItem.Menus && menuItem.Menus.find(menu => menu.MenuName == this.menu.value);
      });
      this.dataSource.data = filteredData;
      return;
    } else {
      this.dataSource.data = this.listItems;
    }
  }

  private _GetListItemsByCanteenId() {
    if (this.selectedCanteen && this.selectedCanteen.CanteenId > 0) {
      this.spinnerService.start();
      this.menuEditorApiService.GetItemsByCanteenAPI(this.selectedCanteen.CanteenId).subscribe(
        (response: MenuItem[]) => {
          if (response != null) {
            this.RefreshTable(response);
            const menusList = [];
            response.map(el => {
              if (!el.Menus) {
                return;
              }
              el.Menus.map(menu => {
                if (menu.MenuName && !menusList.includes(menu.MenuName)) {
                  menusList.push(menu.MenuName);
                }
              });
            });
            this.listMenus = ['All Menus', ...menusList];
          } else {
            this.RefreshTable(null);
            this.listMenus = ['All Menus'];
          }

          this.menuEditorApiService
            .GetCategoriesMenusByCanteenIdAPI(this.selectedCanteen.CanteenId)
            .subscribe(
              (response: CanteenCategoriesMenus) => {
                this.listCategories = [this.allCategories, ...response.ItemCategories];
                this._createForm();
              },
              error => {
                this.RefreshTable(null);
                this.spinnerService.stop();
                this.handleErrorFromService(error);
              }
            );

          this.spinnerService.stop();
        },
        error => {
          this.RefreshTable(null);
          this.spinnerService.stop();
          this.handleErrorFromService(error);
          this.listCategories = [this.allCategories];
          this.listMenus = ['All Menus'];
        }
      );
    } else {
      this.RefreshTable(null);
    }
  }

  private RefreshTable(listItem: MenuItem[]) {
    this.listItems = listItem;
    this.dataSource.data = this.listItems;
  }

  CanteenChanged(canteen: Canteen) {
    this.selectedCanteen = canteen;
    this._GetListItemsByCanteenId();
  }

  GetMenusToDisplay(item: MenuItem) {
    let menus: ItemMenus[] = [];
    if (item.Menus && item.Menus.length > 0) {
      item.Menus.forEach(m => {
        let index = this._SelectedCanteen.Schools.findIndex(i => i.SchoolId == m.SchoolId);
        if (index > -1) {
          menus.push(m);
        }
      });
    }
    return {
      length: menus.length,
      menusDisplay: menus
        ? menus
            .sort((a, b) => {
              if (a.SchoolName < b.SchoolName) return -1;
              if (a.SchoolName > b.SchoolName) return 1;
              else return 0;
            })
            .splice(0, 5)
        : [],
    };
  }
}
