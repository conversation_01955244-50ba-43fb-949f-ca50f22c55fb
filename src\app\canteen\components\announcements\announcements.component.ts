import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { FormGroup, FormControl, Validators } from '@angular/forms';

//Models
import { NoticeBoardComponent, Notice, NoticeType } from 'src/app/sharedModels';

// Services
import { SpinnerService, NoticeService, UserService } from 'src/app/sharedServices';

// Dialog
import { MatDialog } from '@angular/material/dialog';
import { ConvertToUniversalDateFormat } from 'src/app/utility';

@Component({
  selector: 'app-announcements',
  templateUrl: './announcements.component.html',
  styleUrls: ['./announcements.component.scss'],
})
export class AnnouncementsComponent extends NoticeBoardComponent implements OnInit {
  date = new Date();

  constructor(
    private _location: Location,
    public spinnerService: SpinnerService,
    protected noticeService: NoticeService,
    protected userService: UserService,
    public dialog: MatDialog
  ) {
    super(noticeService, spinnerService, userService, dialog);
  }

  ngOnInit() {
    this.currentNoticeType = NoticeType.Announcement;
    this.CreateForm(new Notice());
  }

  GoBackClick() {
    this._location.back();
  }

  openEditForm(announcement: Notice) {
    this.selectedNotice = announcement;
    this.CreateForm(announcement);
    this.showNoticeForm = true;
  }

  CreateForm(announcement: Notice) {
    const filteredAnnouncements =
      (this.noticeData && this.noticeData.filter(announcement => announcement.IsActive == true)) || [];
    const formDate = announcement.EndDate ? ConvertToUniversalDateFormat(announcement.EndDate) : null;

    this.form = new FormGroup({
      title: new FormControl(announcement.Title || '', [Validators.required, Validators.minLength(3)]),
      description: new FormControl(announcement.Description || ''),
      endDate: new FormControl(formDate),
      isActive: new FormControl(this.isAnnouncementActive(announcement)),
    });
  }

  isAnnouncementActive(announcement: Notice): boolean {
    const newAnnouncment = !announcement.NoticeId;
    return announcement?.IsActive || newAnnouncment;
  }

  AddNotice(): void {
    this.showNoticeForm = true;
    this.selectedNotice = null;
    this.CreateForm(new Notice());
  }

  IsSubmitButtonActive(): boolean {
    return !this.form.valid;
  }
}
