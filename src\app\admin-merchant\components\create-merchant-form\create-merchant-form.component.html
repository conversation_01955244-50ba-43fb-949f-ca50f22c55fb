<div class="relative-section">
  <nav-back-button
    smallText="true"
    (navBack)="GoBackClick()"
    text="Go Back"
    class="backButton"
    smallFont="true"
    noPadding="true"
  ></nav-back-button>
</div>

<div class="container-fluid">
  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <div class="header relative-section">
      <h3>Create Merchant</h3>

      <button class="merchant-btn" type="submit">
        <img src="assets/icons/white-plus.svg" alt="plus symbol" />
        Create
      </button>
    </div>

    <div class="col-12 user-card">
      <div class="row">
        <!-- Left Column -->
        <div class="col-sm-12 col-md-6">
          <!-- First Name -->
          <mat-form-field appearance="outline">
            <mat-label>First name</mat-label>
            <input matInput placeholder="First name" formControlName="firstName" type="text" required />
            <mat-error *ngIf="merchantName.invalid">You must enter a value</mat-error>
          </mat-form-field>

          <!-- Last name -->
          <mat-form-field appearance="outline">
            <mat-label>Last name</mat-label>
            <input matInput placeholder="Last name" formControlName="lastName" type="text" required />
            <mat-error *ngIf="merchantName.invalid">You must enter a value</mat-error>
          </mat-form-field>

          <!-- Canteen Type -->
          <label id="type-label">Type</label>
          <mat-radio-group aria-labelledby="type-label" formControlName="merchantType" class="radioButtons">
            <mat-radio-button style="padding-right: 15px" [value]="merchantTypeEnum.Canteen">{{
              merchantTypeEnum.Canteen
            }}</mat-radio-button>
            <mat-radio-button [value]="merchantTypeEnum.Event">{{ merchantTypeEnum.Event }}</mat-radio-button>
            <mat-radio-button [value]="merchantTypeEnum.Uniform">{{
              merchantTypeEnum.Uniform
            }}</mat-radio-button>
          </mat-radio-group>
          <hr class="details-divider" />
        </div>

        <!-- Right Column -->
        <div class="col-sm-12 col-md-6">
          <!-- merchant name -->
          <mat-form-field appearance="outline">
            <mat-label>Merchant name</mat-label>
            <input matInput placeholder="Merchant Name" formControlName="merchantName" type="text" required />
            <mat-error *ngIf="merchantName.invalid">You must enter a value</mat-error>
          </mat-form-field>

          <!-- mobile -->
          <div class="read-only-wrapper">
            <mat-form-field appearance="outline">
              <mat-label>Mobile</mat-label>
              <input matInput placeholder="Mobile" formControlName="mobile" type="text" readonly />
            </mat-form-field>
          </div>

          <!-- email -->
          <div class="read-only-wrapper" style="padding-top: 10px">
            <mat-form-field appearance="outline">
              <mat-label>Email</mat-label>
              <input matInput placeholder="Email" formControlName="email" type="text" readonly />
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
