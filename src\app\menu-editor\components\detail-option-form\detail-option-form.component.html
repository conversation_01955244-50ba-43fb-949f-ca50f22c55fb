<h2>Option</h2>

<div class="cardDefaultCanteen">
  <form (ngSubmit)="SubmitForm()" #optionForm="ngForm">
    <div>
      <mat-form-field appearance="outline">
        <mat-label>Option name</mat-label>
        <input
          matInput
          [(ngModel)]="option.Name"
          name="Name"
          placeholder="Name"
          type="text"
          maxlength="100"
          required
        />
      </mat-form-field>
    </div>

    <div>
      <mat-checkbox [(ngModel)]="option.IsActive" name="IsActive">Is Active</mat-checkbox>
    </div>

    <div>
      <h3 class="title">Selection rules</h3>
      <div *ngFor="let rule of selectionRules; let i = index">
        <div class="checkBoxWrapper">
          <mat-checkbox [checked]="selectedRuleIndex === i" (change)="onRuleChange(i)">
            <div class="textWrapper">
              <p class="ruleTitle">{{ rule.title }}</p>
              <p class="ruleDescription">{{ rule.description }}</p>
            </div>
          </mat-checkbox>
        </div>
      </div>
    </div>

    <div class="divButton">
      <button
        class="PrimaryButton submitButton"
        type="submit"
        [disabled]="!optionForm.form.valid || selectedRuleIndex == null"
      >
        {{ GetTextButton() }}
      </button>
      <button
        *ngIf="CanDeleteItem()"
        class="PrimaryButton deleteButton"
        type="button"
        (click)="ArchiveMenuItem($event)"
      >
        Delete Option
      </button>
    </div>
  </form>
</div>
