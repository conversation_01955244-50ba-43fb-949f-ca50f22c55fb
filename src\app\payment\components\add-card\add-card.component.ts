import { Component, OnInit, Input, Output, EventEmitter, AfterViewChecked } from '@angular/core';
import { MatRadioChange } from '@angular/material/radio';
import * as braintree from 'braintree-web';

// models
import { BraintreeErrorsEnum, AddCardForm } from 'src/app/sharedModels';

// service
import { CashlessAppInsightsService } from 'src/app/sharedServices';

@Component({
  selector: 'payment-add-card',
  templateUrl: './add-card.component.html',
  styleUrls: ['./add-card.component.scss'],
})
export class AddCardComponent implements OnInit, AfterViewChecked {
  @Input() client: any;
  @Input() textSubmitButton: string;
  //@Input() errorAddCard: string = null;
  @Output() cancel: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() confirm: EventEmitter<AddCardForm> = new EventEmitter<AddCardForm>();
  errorAddCard: string = null;
  hostedFieldsInstance: braintree.HostedFields;
  cardholdersName: string;
  bRemember: boolean = true;
  selectedPaymentChoice: number = 1;
  private needToRefreshForm: boolean = false;

  constructor(private appInsightsService: CashlessAppInsightsService) {}

  ngOnInit() {
    if (this.client) {
      this._InitHostedFields();
    }
  }

  // if(change.value == 1 || change.value == 2){
  //   this._InitHostedFields();
  // }

  ngAfterViewChecked(): void {
    //Called after ngAfterContentInit when the component's view has been initialized. Applies to components only.
    //Add 'implements AfterViewInit' to the class.
    if (this.needToRefreshForm) {
      this._InitHostedFields();
    }
  }

  CancelClick() {
    this.cancel.emit(true);
  }

  ConfirmClick() {
    this.errorAddCard = null;
    this.tokenizeUserDetails();
  }

  SelectedChoiceChanged(change: MatRadioChange) {
    if (
      (change.value == 1 || change.value == 2) &&
      this.selectedPaymentChoice != 1 &&
      this.selectedPaymentChoice != 2
    ) {
      this.needToRefreshForm = true;
    }
    this.selectedPaymentChoice = change.value;
  }

  private _InitHostedFields() {
    this.needToRefreshForm = false;

    braintree.hostedFields
      .create({
        client: this.client,
        styles: {
          // Override styles for the hosted fields
          input: {
            'font-size': '16px',
          },
        },

        // The hosted fields that we will be using
        // NOTE : cardholder's name field is not available in the field options
        // and a separate input field has to be used incase you need it
        fields: {
          number: {
            selector: '#card-number',
            placeholder: '1111 1111 1111 1111',
          },
          cvv: {
            selector: '#cvv',
            placeholder: '111',
          },
          expirationDate: {
            selector: '#expiration-date',
            placeholder: 'MM/YY',
          },
        },
      })
      .then(hostedFieldsInstance => {
        this.hostedFieldsInstance = hostedFieldsInstance;

        hostedFieldsInstance.on('focus', event => {
          const field = event.fields[event.emittedBy];
          const label = this.findLabel(field);
          label.classList.remove('filled'); // added and removed css classes
          // can add custom code for custom validations here
        });

        hostedFieldsInstance.on('blur', event => {
          const field = event.fields[event.emittedBy];
          const label = this.findLabel(field); // fetched label to apply custom validations
          // can add custom code for custom validations here
        });

        hostedFieldsInstance.on('empty', event => {
          const field = event.fields[event.emittedBy];
          // can add custom code for custom validations here
        });

        hostedFieldsInstance.on('validityChange', event => {
          const field = event.fields[event.emittedBy];
          const label = this.findLabel(field);
          if (field.isPotentiallyValid) {
            // applying custom css and validations
            label.classList.remove('invalid');
          } else {
            label.classList.add('invalid');
          }
          // can add custom code for custom validations here
        });
      });
  }

  // Tokenize the collected details so that they can be sent to your server
  // called from the html when the 'Pay' button is clicked
  private tokenizeUserDetails() {
    this.hostedFieldsInstance
      .tokenize({ cardholderName: this.cardholdersName })
      .then(payload => {
        // submit payload.nonce to the server from here
        let request: AddCardForm = {
          remember: this.bRemember,
          nonce: payload.nonce,
        };
        this.confirm.emit(request);
      })
      .catch(error => {
        switch (error.code) {
          case BraintreeErrorsEnum.AllFieldsEmpty:
            this.errorAddCard = null;
            break;

          case BraintreeErrorsEnum.OneOrMoreFieldsEmpty:
            this.errorAddCard = 'All fields are required';
            break;

          case BraintreeErrorsEnum.Duplicate:
            this.errorAddCard = 'You already have save this credit card';
            break;

          case BraintreeErrorsEnum.Tokenize:
            this.errorAddCard = 'Please check the card details';
            break;

          default:
            break;
        }

        // track event
        this.appInsightsService.TrackEvent('AddCardFormValidation', { Error: JSON.stringify(error) });
      });
  }

  // Fetches the label element for the corresponding field
  private findLabel(field: braintree.HostedFieldsHostedFieldsFieldData) {
    return document.querySelector('.hosted-field--label[for="' + field.container.id + '"]');
  }
}
