@import '../../../../styles/cashless-theme.scss';

.backButton {
  color: orange;
  font-size: 14px;
}

.titleWrapper {
  display: flex;
  flex-direction: row;
  margin-bottom: 13px;
  align-items: center;
}

.title {
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 22px;
  margin: 0;
  margin-left: 8px;
}

.schoolSelection {
  background-color: #ffffff;
  border-radius: 12px;
  padding-top: 10px;
  padding-left: 20px;
  padding-right: 20px;
}

.titleDescription {
  margin: 0;
  font-size: 20px;
}

.titleDescriptionWrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
}

.titleDescriptionButton {
  display: flex;
  flex-direction: row;
  cursor: pointer;
}

.titleDescriptionButtonText {
  color: $orange-3;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: right;
  margin: 0;
  margin-right: 9px;
}

.cardWrapper {
  background-color: #ffffff;
  border-radius: 12px;
  padding-top: 20px;
  padding-bottom: 23px;
  padding-right: 15px;
  padding-left: 20px;
}

.crossIconWrapper {
  display: flex;
  justify-content: flex-end;
  padding-right: 2px;
}

.invisible {
  display: none;
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
.mat-checkbox-background {
  background-color: yellowgreen !important;
}
