import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubOption } from 'src/app/sharedModels';

@Component({
  selector: 'option-chip-list',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './option-chip-list.component.html',
  styleUrls: ['./option-chip-list.component.scss'],
})
export class OptionChipListComponent {
  @Input() optionList: SubOption[];
}
