import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

// components
import {
  AdminDetailSchoolComponent,
  AdminSchoolClassesComponent,
  SchoolNavBarComponent,
  AdminListSchoolsComponent,
  AdminEventManagementComponent,
  AdminSchoolFeaturesComponent,
} from './components';

// resolvers
import {
  ListCanteensForAdminResolver,
  ListClassesResolver,
  ListSchoolsAdminResolver,
  ListStatesResolver,
  SchoolResolver,
} from '../sharedServices';
import { SchoolMerchantsResolver } from './resolvers/school-merchants.resolver';
import { ListFeaturesResolver } from './resolvers/list-features.resolver';

// routes
const routes: Routes = [
  {
    path: '',
    component: AdminListSchoolsComponent,
    resolve: { schools: ListSchoolsAdminResolver },
  },
  {
    path: 'add',
    component: AdminDetailSchoolComponent,
    resolve: {
      canteens: ListCanteensForAdminResolver,
      states: ListStatesResolver,
    },
  },
  {
    path: ':id',
    component: SchoolNavBarComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'details',
      },
      {
        path: 'details',
        component: AdminDetailSchoolComponent,
        resolve: {
          school: SchoolResolver,
          states: ListStatesResolver,
        },
      },
      {
        path: 'classes',
        component: AdminSchoolClassesComponent,
        resolve: {
          classes: ListClassesResolver,
        },
      },
      {
        path: 'features',
        component: AdminSchoolFeaturesComponent,
        resolve: {
          features: ListFeaturesResolver,
        },
      },
      {
        path: 'events',
        component: AdminEventManagementComponent,
        resolve: {
          merchants: SchoolMerchantsResolver,
        },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminSchoolsRoutingModule {}
