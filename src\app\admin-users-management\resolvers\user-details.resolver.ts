import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';

// state
import { Store } from '@ngrx/store';
import { UserManagementState } from 'src/app/states/user-management/user-management-state.interface';
import { SelectUser } from 'src/app/states/user-management/user-management.actions';
import { Observable } from 'rxjs';

export const UserDetailsResolver: ResolveFn<any> = (route: ActivatedRouteSnapshot): Observable<any> => {
  const store = inject(Store<{ userManagement: UserManagementState }>);
  let id = route.params['id'];

  if (id == null || id == undefined) {
    id = route.parent.params['id'];
  }
  store.dispatch(SelectUser({ userId: +id }));

  return null;
};
