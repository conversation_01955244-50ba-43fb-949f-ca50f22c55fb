@import '../../../../styles/cashless-theme.scss';
@import 'src/styles/schools-theme';

.button-container {
  display: flex;
  justify-content: flex-end;
}

.form {
  max-width: 900px;
  display: flex;
  flex-direction: column;
}

.time-date-container {
  display: flex;
  justify-content: space-between;
}

.warning {
  margin-top: 5px;
  color: red;
}

mat-form-field {
  width: 100%;
}

.info-container {
  background-color: $error-background;
  padding: 8px;
  border-radius: 8px;
  gap: 8px;
  display: flex;
  align-items: center;
  overflow: visible;
  margin-bottom: 16px;

  .info-icon {
    vertical-align: middle;
    line-height: 1;
    font-size: 24px;
  }

  p {
    margin: 0;
    width: 100%;
  }
}
