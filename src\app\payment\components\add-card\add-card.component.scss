@import '../../../../styles/cashless-breakpoints.scss';
@import '../../../../styles/cashless-theme.scss';

/*--------------------
SHARED VARIABLES
--------------------*/
$red: #e91e63;
$blue: #3f51b5;
$grey: #eae8e9;
$grey2: #f3f3f3;
$white: #fff;

h1 {
  font-size: 1.5em;
  font-weight: 100;
}

#cardForm {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/*--------------------
  PANEL FORM
  --------------------*/

.panel {
  background: $white;
  width: 80%;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.16), 0 0 2px 0 rgba(0, 0, 0, 0.12);
}

.panel__header {
  background: $blue;
  color: $white;
}

.panel__header,
.panel__footer {
  padding: 1em 2em;
}

.panel__footer {
  background: $grey2;
}

.panel__content {
  padding: 1em 2em;
  overflow: hidden;
}

.textfield--float-label {
  width: 100%;
  float: left;
  display: inline-block;
  padding-right: 5px;

  &.cvvField {
    margin-bottom: 15px;
  }
}

.hosted-field--label {
  transform: translateY(0.4em);
  font-size: 1.125em;
  line-height: 16px;
  transition: all 0.15s ease-out;
  display: block;
  width: 100%;
  font-weight: 400;
  overflow: hidden;
  margin-bottom: 0.5em;
  &.label-float {
    text-overflow: ellipsis;
    color: #2196f3;
    transition: all 0.15s ease-out;
  }
  &.filled {
    @extend .label-float;
    color: rgba(0, 0, 0, 0.54);
  }
  &.invalid {
    @extend .label-float;
    color: #f44336;
  }
}

span.icon {
  position: relative;
  top: 0.2em;
  margin-right: 0.2em;
}

svg {
  fill: #333;
}

.hosted-field {
  height: 32px;
  margin-bottom: 1em;
  display: block;
  background-color: transparent;
  color: rgba(0, 0, 0, 0.87);
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.26);
  outline: 0;
  width: 100%;
  font-size: 16px;
  padding: 0;
  box-shadow: none;
  border-radius: 0;
  position: relative;
}

/*--------------------
  BT HOSTED FIELDS SPECIFIC 
  --------------------*/

.braintree-hosted-fields-focused {
  border-bottom: 2px solid $blue;
  transition: all 200ms ease;
}

.braintree-hosted-fields-invalid {
  border-bottom: 2px solid $red;
  transition: all 200ms ease;
}

/*---------------------
  Media Queries
  ----------------------*/

@media (max-width: 600px) {
  html {
    overflow: auto;
  }
  #cardForm {
    height: auto;
    margin: 2em;
    font-size: 13px;
  }
  .panel {
    width: 100%;
  }
  .textfield--float-label {
    width: 100%;
    float: none;
    display: inline-block;
  }
}

.cashlessForm {
  padding-bottom: 20px;
}

.rememberCheck {
  padding-left: 25px;
}

.rowButton {
  margin-top: 10px;
  margin-bottom: 15px;

  padding-left: 0;
  padding-right: 30px;

  @media (max-width: $breakpoint-md) {
    padding: 0;
  }
}

// Select list Type card
.card-radio-group {
  display: flex;
  flex-direction: column;
  margin: 15px 0;
}

.card-radio-button {
  margin: 5px 0 5px 0;
}

.informationsPayment {
  color: $orange-3;
}
