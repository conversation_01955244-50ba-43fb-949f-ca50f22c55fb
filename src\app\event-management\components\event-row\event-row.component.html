<div class="row-container" (click)="pressEvent()">
  <event-image-placeholder *ngIf="!schoolEvent.ImageUrl" [small]="true" />
  <img *ngIf="schoolEvent.ImageUrl" [src]="schoolEvent.ImageUrl" alt="Event image" />

  <div class="content-container">
    <h3>{{ schoolEvent.Name }}</h3>

    <div class="date-container">
      <p class="pr-3">
        Event date: {{ schoolEvent.EventDate | date : 'mediumDate'
        }}{{ schoolEvent.EventDate | eventTimeFormat }}
      </p>
      <p>
        Cut off date: {{ schoolEvent.CutOffDate | date : 'mediumDate'
        }}{{ schoolEvent.CutOffDate | eventTimeFormat }}
      </p>
    </div>
  </div>
  <div class="indicator-container">
    <active-indicator [isActive]="schoolEvent.IsActive"></active-indicator>
  </div>
</div>
