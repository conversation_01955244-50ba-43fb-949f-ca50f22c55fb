import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Location } from '@angular/common';

// Service
import { MenuEditorApiService } from 'src/app/sharedServices';

// Models
import { BaseComponent, Option, Canteen } from 'src/app/sharedModels';

// ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { CanteenState } from '../../../states';
import { selectedCanteen } from '../../../states/canteen/canteen.selectors';

@Component({
  selector: 'editor-detail-option',
  templateUrl: './detail-option.component.html',
  styleUrls: ['./detail-option.component.scss'],
})
export class DetailOptionComponent extends BaseComponent implements OnInit, OnDestroy {
  option: Option;
  private canteenSubscription: Subscription;
  selectedCanteen: Canteen;

  constructor(
    private activatedRoute: ActivatedRoute,
    private _location: Location,
    private store: Store<{ canteen: CanteenState }>,
    private menuEditorApiService: MenuEditorApiService
  ) {
    super();
  }

  ngOnInit() {
    this.canteenSubscription = this.store
      .pipe(select(selectedCanteen))
      .subscribe((selectedCanteen: Canteen) => {
        this.selectedCanteen = selectedCanteen;
        this.option = this.activatedRoute.snapshot.data['option'];

        if (!this.option && selectedCanteen) {
          this.option = new Option();
          this.option.IsActive = true;
          this.option.CanteenId = selectedCanteen.CanteenId;
          this.option.SubOptions = [];
        }
      });
  }

  reloadOptionData(): void {
    this.menuEditorApiService.GetOptionByIdAPI(this.option.MenuItemOptionsCategoryId).subscribe({
      next: (res: Option) => {
        this.option = res;
      },
      error: error => {
        this.handleErrorFromService(error);
      },
    });
  }

  ngOnDestroy() {
    this.canteenSubscription?.unsubscribe();
  }

  GoBackClick() {
    this._location.back();
  }
}
