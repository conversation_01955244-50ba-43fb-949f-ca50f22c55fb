<div class="container-fluid">
  <nav-back-button (navBack)="GoBackClick()" text="Go back"></nav-back-button>

  <ng-container *ngIf="option">
    <div class="row">
      <div class="col-6 col-lg-4 col-sm-12">
        <editor-detail-option-form [(option)]="option"></editor-detail-option-form>
      </div>
      <div class="col-6 col-lg-8 col-sm-12">
        <editor-detail-option-choices
          *ngIf="option.MenuItemOptionsCategoryId"
          [(option)]="option"
          (updateOption)="reloadOptionData()"
        ></editor-detail-option-choices>
      </div>
    </div>

    <editor-detail-option-items
      *ngIf="option.MenuItemOptionsCategoryId > 0"
      [option]="option"
      [canteenId]="selectedCanteen.CanteenId"
    ></editor-detail-option-items>
  </ng-container>
</div>
