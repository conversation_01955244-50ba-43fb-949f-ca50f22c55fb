<div class="col-12">
  <div class="col-12 col-md-8">
    <nav-back-button (navBack)="GoBackClick()" text="Go Back" class="backButton"></nav-back-button>

    <notice-header
      title="Article"
      (schoolChanged)="OnSchoolSelect($event)"
      (merchantChanged)="OnMerchantChanged($event)"
      (openForm)="AddNotice()"
    ></notice-header>
  </div>

  <div class="col-12 row infoBlock">
    <div class="col-12 col-md-8">
      <notice-table
        [tableData]="noticeData"
        type="NoticeType.Article"
        (selectRow)="openEditForm($event)"
      ></notice-table>
    </div>

    <div class="col-12 col-md-4" [ngClass]="{ invisible: !showNoticeForm }">
      <div class="cardWrapper">
        <div class="cardWrapperPadding">
          <div class="crossIconWrapper">
            <a (click)="hideForm()" class="closeBtn">
              <img src="assets/icons/cross.svg" />
            </a>
          </div>
        </div>

        <form [formGroup]="form" class="cashlessForm">
          <mat-form-field appearance="outline">
            <mat-label>Title</mat-label>
            <input
              maxlength="50"
              matInput
              placeholder="Enter title of article"
              formControlName="title"
              type="text"
            />
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Body</mat-label>
            <textarea
              maxlength="1000"
              rows="1"
              matInput
              placeholder="Add article content"
              formControlName="description"
              type="text"
              class="description"
              #description
              rows="5"
            ></textarea>
            <mat-hint align="end">{{ description.value.length }} / 1000</mat-hint>
          </mat-form-field>

          <div class="separator bottom"></div>

          <!-- validation explain -->
          <div
            *ngIf="selectedNotice && selectedNotice.Status == NoticeStatusEnum.Refused"
            class="validationExplanation"
          >
            <h4>Validation explanation</h4>
            <p>{{ selectedNotice.ValidationDescription }}</p>
          </div>

          <form-buttons
            (saveEvent)="saveNotice()"
            (cancelEvent)="hideForm()"
            (deleteEvent)="deleteNoticeCheck()"
            [disableSaveButton]="IsSubmitButtonActive()"
            [showDeleteButton]="showDeleteButton()"
          ></form-buttons>
        </form>
      </div>
    </div>
  </div>
</div>
