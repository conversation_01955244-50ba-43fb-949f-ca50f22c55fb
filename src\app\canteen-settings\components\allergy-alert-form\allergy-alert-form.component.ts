import { KeyValue } from '@angular/common';
import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

//Models
import { BaseComponent, OptionSchoolResponse, SchoolFeatureEnum } from 'src/app/sharedModels';

//Services
import { SchoolService, SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'allergy-alert-form',
  templateUrl: './allergy-alert-form.component.html',
  styleUrls: ['./allergy-alert-form.component.scss'],
})
export class AllergyAlertFormComponent extends BaseComponent implements OnInit {
  @Input() schoolId: number;
  @Input() schoolFeatures: OptionSchoolResponse[];
  form: FormGroup;
  currentAllergyFeature: OptionSchoolResponse;
  checkBoxValue: KeyValue<string, string>[] = [];

  constructor(private spinnerService: SpinnerService, private schoolService: SchoolService) {
    super();
  }

  ngOnInit(): void {
    this.checkBoxValue.push({ key: 'allergyAlert', value: 'Allow' });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.schoolId?.currentValue) {
      this._createForm();
    }
  }

  private _createForm() {
    this.currentAllergyFeature = this.getAllergyFeature();
    let hasAllergyAlert = Boolean(this.currentAllergyFeature?.IsActive);

    this.form = new FormGroup({
      allergyAlert: new FormControl(hasAllergyAlert, Validators.required),
    });
  }

  submitForm() {
    this.spinnerService.start();
    let request = this.getAllergyUpdateRequest();

    this.schoolService.UpsertSchoolOptionsApi(request).subscribe({
      next: () => {
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  getAllergyFeature(): OptionSchoolResponse | null {
    if (!this.schoolFeatures) {
      return null;
    }
    let index = this.schoolFeatures.findIndex(x => x.OptionName === SchoolFeatureEnum.AllergyAlert);
    return index >= 0 ? this.schoolFeatures[index] : null;
  }

  getAllergyUpdateRequest(): OptionSchoolResponse {
    let request: OptionSchoolResponse;

    //update existing allergy feature
    if (this.currentAllergyFeature) {
      this.currentAllergyFeature.IsActive = this.allergyAlert.value;
      request = this.currentAllergyFeature;
    } else {
      //create new allergy feature
      request = new OptionSchoolResponse();
      request.SchoolId = this.schoolId;
      request.OptionName = SchoolFeatureEnum.AllergyAlert;
      request.OptionDescription = SchoolFeatureEnum.AllergyAlert;
      request.IsActive = this.allergyAlert.value;
    }
    return request;
  }

  get allergyAlert() {
    return this.form.get('allergyAlert');
  }
}
