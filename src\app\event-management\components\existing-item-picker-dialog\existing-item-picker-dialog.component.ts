import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  AddEventItemData,
  BaseComponent,
  CategoryEditor,
  EventItem,
  MenuItem,
  MenuItemSelectList,
  SchoolEvent,
} from 'src/app/sharedModels';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { EditEventDialog } from '../edit-event-dialog/edit-event-dialog.component';
import { SharedToolsModule } from 'src/app/shared-tools/shared-tools.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { MenuEditorApiService, SchoolEventManagerService, SpinnerService } from 'src/app/sharedServices';
import { SchoolsButtonModule } from "../../../schools-button/schools-button.module";
import { Subscription } from 'rxjs';
import { CanteenState } from 'src/app/states';
import { select, Store } from '@ngrx/store';
import { menuCategories } from 'src/app/states/canteen/canteen.selectors';
import { ItemImageComponent } from "../../../menu-editor/components/item-image/item-image.component";
import { CategorySelectListComponent } from "../../../menu-editor/components/category-select-list/category-select-list.component";

@Component({
  selector: 'existing-item-picker-dialog',
  standalone: true,
  imports: [
    CommonModule,
    SharedToolsModule,
    MatDialogModule,
    MatCheckboxModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    SharedModule,
    FormsModule,
    SchoolsButtonModule,
    ItemImageComponent,
    CategorySelectListComponent
],
  templateUrl: './existing-item-picker-dialog.component.html',
  styleUrls: ['./existing-item-picker-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ExistingItemPickerDialog extends BaseComponent implements OnInit {
  loading: boolean = true;
  selectedItemIds: number[] = [];
  selectedCategory: number;
  private menuItems: MenuItemSelectList[];
  menuItemsToDisplay: MenuItemSelectList[];
  canteenSubscription: Subscription;
  listCategories: CategoryEditor[];
  // form: FormGroup;
  search: string = '';

  constructor(
    public dialogRef: MatDialogRef<EditEventDialog>,
    @Inject(MAT_DIALOG_DATA) public data: AddEventItemData,
    protected schoolEventManagerService: SchoolEventManagerService,
    protected menuEditorAPIService: MenuEditorApiService,
    protected store: Store<{ canteen: CanteenState }>,
    private spinnerService: SpinnerService,
    private cd: ChangeDetectorRef,
  ) {
    super();
  }

  ngOnInit(): void {
      this.canteenSubscription = this.store
      .pipe(select(menuCategories))
      .subscribe((menuCategories: CategoryEditor[]) => {
        this.listCategories = menuCategories;
        this.selectedCategory = 0;
      });

      this.getMenuItems(this.data.MerchantId);
      this.selectedItemIds = this.data.SelectedItemIds;
    }

    categoryChanged(categoryId: number) {
      this.selectedCategory = categoryId;

      this.getItemsToDisplay();
    }

  getItemsToDisplay() {
    this.menuItemsToDisplay = this.menuItems.filter(m => (m.categoryId == this.selectedCategory || this.selectedCategory == 0) && (this.search == '' || m.name.toLowerCase().indexOf(this.search.toLowerCase(), 0) > -1));
  }

  ItemIsSelected(menuItemId: number) {
    if(this.selectedItemIds != null && this.selectedItemIds.length > 0){
      return this.selectedItemIds.findIndex(m => m == menuItemId) > -1;
    }

    return false;
  }

  closeModal(): void {
    this.dialogRef.close();
  }

  saveEvent(schoolEvent: SchoolEvent): void {
    this.dialogRef.close(schoolEvent);
  }

  itemPress(menuItemId: number) {

    if(!this.selectedItemIds) {
      this.selectedItemIds = [];
    }

    let index = this.selectedItemIds.findIndex(i => i == menuItemId);

    if(index > -1){
      this.removeItem(menuItemId, index)
    }else{
      this.addItem(menuItemId);
    }
  }

  private addItem(menuItemId: number): void {
    this.spinnerService.start();

    this.schoolEventManagerService.LinkItemToEvent(this.data.EventId, menuItemId).subscribe({
      next: () => {
        this.spinnerService.stop();
        this.selectedItemIds.push(menuItemId);
        this.cd.markForCheck();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  private removeItem(menuItemId: number, index: number): void {
    this.spinnerService.start();

    this.schoolEventManagerService.RemoveItemFromEvent(this.data.EventId, menuItemId).subscribe({
      next: () => {
        this.spinnerService.stop();
        this.selectedItemIds.splice(index,1);
        this.cd.markForCheck();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  private getMenuItems(canteenId: number) {
    this.menuEditorAPIService.MenuItemSelectList(canteenId).subscribe({
      next: (response: MenuItemSelectList[]) => {
        this.menuItems = response;
        this.spinnerService.stop();
        this.getItemsToDisplay();
      },
      error: error => {
        this.spinnerService.stop();
      },
    });
  }
}
