<mat-dialog-content>
  <modal-header title="Edit Event" (close)="closeModal()"></modal-header>

  <ng-container *ngIf="loading; else form">
    <div class="col-12 mt-2 d-flex align-items-center justify-content-center">
      <app-spinner [manual]="true"></app-spinner>
    </div>
  </ng-container>
  <ng-template #form>
    <event-form
      (saveEvent)="saveEvent($event)"
      [eventToEdit]="selectedEvent"
      [schoolClasses]="schoolClasses"
    ></event-form>
  </ng-template>
</mat-dialog-content>
