<mat-dialog-content>
  <modal-header [title]="title" (close)="closeModal()"></modal-header>

  <form *ngIf="form" [formGroup]="form">
    <div class="pb-2">
      <input-text-v2
        placeholder="First name"
        formControlName="firstName"
        [showErrorOutline]="firstName.invalid && firstName.touched"
      ></input-text-v2>
    </div>

    <div class="pb-2">
      <input-text-v2
        placeholder="Last name"
        formControlName="lastName"
        [showErrorOutline]="lastName.invalid && lastName.touched"
      ></input-text-v2>
    </div>

    <div *ngIf="isParent" class="pb-2">
      <input-text-v2
        placeholder="Mobile"
        formControlName="mobile"
        [showErrorOutline]="mobile.invalid && mobile.touched"
        (keyUp)="formatMobile()"
      ></input-text-v2>
    </div>

    <div *ngIf="!isParent" class="pb-2">
      <input-select-list-v2
        formControlName="schoolId"
        placeholder="School"
        [values]="listSchools"
        [showErrorOutline]="schoolId.invalid && schoolId.touched"
      ></input-select-list-v2>
    </div>

    <div *ngIf="!isParent && listClasses" class="pb-2">
      <input-select-list-v2
        formControlName="classId"
        placeholder="Class"
        [values]="listClasses"
        addNullRow="true"
        [showErrorOutline]="classId.invalid && classId.touched"
        nullValueText="Select class"
      ></input-select-list-v2>
    </div>
  </form>

  <div class="d-flex justify-content-end align-items-end pt-3" style="gap: 10px">
    <basic-button-v2 text="Cancel" buttonStyle="secondary" (onPress)="closeModal()"></basic-button-v2>
    <basic-button-v2 text="Save" buttonStyle="primary" (onPress)="onSubmit()"></basic-button-v2>
  </div>
</mat-dialog-content>
