@import '../../../../styles/cashless-breakpoints.scss';
@import '../../../../styles/cashless-theme.scss';
@import '../../../../styles/cashless-font.scss';

.table {
  margin: auto;
  width: 50% !important;

  &.newOrderTable {
    padding-top: 10px;
  }
}

.tableHeader {
  margin: auto;
  text-align: center;
}

.headerText {
  font-family: 'bariol_bold';
  font-size: 18px;
}

.tdDesktop {
  display: table-cell;

  @media (max-width: $breakpoint-md) {
    display: none;
  }
}

.trMobile {
  display: table-row;

  @media (min-width: $breakpoint-md) {
    display: none;
  }

  & td {
    font-family: 'bariol_bold';
    font-size: 18px;
    text-align: center;
    padding-top: 10px;
    padding-bottom: 10px;

    &.recessType {
      border-bottom: 1px solid $grey-3;
    }

    &.daysRow {
      padding-top: 5px;
      padding-bottom: 5px;
      border-bottom: 1px solid $grey-3;
    }
  }
}

.familyBlockLine {
  & td {
    padding-top: 10px;
    @media (min-width: $breakpoint-md) {
      padding-top: 0px;
    }
  }
}

.description {
  font-size: 16px;
  white-space: pre-wrap;
  margin-top: 21px;
  margin-bottom: 21px;
}

.background {
  background-color: white;
}
