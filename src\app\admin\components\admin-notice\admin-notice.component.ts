import { Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';

// Models
import {
  BasePaginatorComponent,
  Notice,
  UpdateNoticeStatusRequest,
  NoticeStatusEnum,
  BaseComponent,
} from 'src/app/sharedModels';

// Services
import { NoticeService, SpinnerService } from 'src/app/sharedServices';

const _columns = ['noticeId', 'title', 'description', 'schoolId', 'status'];

@Component({
  selector: 'app-admin-notice',
  templateUrl: './admin-notice.component.html',
  styleUrls: ['./admin-notice.component.scss'],
})
export class AdminNoticeComponent extends BasePaginatorComponent<Notice> implements OnInit {
  constructor(
    private activatedRoute: ActivatedRoute,
    public dialog: MatDialog,
    private spinnerService: SpinnerService,
    private noticeService: NoticeService
  ) {
    super(_columns);
  }

  ngOnInit() {
    this.RetrieveNoticeData();
  }

  RetrieveNoticeData() {
    this.spinnerService.start();
    // this.activatedRoute.data will not change once its been called
    // only the first time works
    this.activatedRoute.data.subscribe((data: { notices: Notice[] }) => {
      this.dataSource.data = data.notices;
      this.spinnerService.stop();
    });
  }

  openDialog(element: Notice) {
    const dialogRef = this.dialog.open(AdminNoticeDialog, {
      width: '400px',
      data: element,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.noticeService.GetAllNoticeWaitingForValidation().subscribe((response: Notice[]) => {
          this.dataSource.data = response;
          this.spinnerService.stop();
        });
      }
    });
  }
}

@Component({
  selector: 'app-admin-notice-dialog',
  templateUrl: './admin-notice-dialog.component.html',
})
export class AdminNoticeDialog extends BaseComponent {
  constructor(
    public dialogRef: MatDialogRef<AdminNoticeDialog>,
    @Inject(MAT_DIALOG_DATA) public data: Notice,
    private noticeService: NoticeService,
    private spinnerService: SpinnerService
  ) {
    super();
  }

  noticeDialogForm: FormGroup;
  displayNotice: Notice;
  noticeStatusEnum = NoticeStatusEnum;

  ngOnInit() {
    this.displayNotice = this.data;
    this.noticeDialogForm = new FormGroup({
      declineReason: new FormControl(''),
    });
  }

  get declineReason() {
    return this.noticeDialogForm.get('declineReason');
  }

  closeModal(): void {
    this.dialogRef.close();
  }

  submitForm(status: string) {
    this.spinnerService.start();

    const updatedNotice = new UpdateNoticeStatusRequest();
    updatedNotice.NoticeId = this.data.NoticeId;
    updatedNotice.NoticeStatus = status;
    updatedNotice.ValidationDescription =
      status === this.noticeStatusEnum.Refused ? this.declineReason.value : null;

    this.noticeService.UpdateNoticeStatus(updatedNotice).subscribe({
      next: response => {
        this.spinnerService.stop();
        this.dialogRef.close(true);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }
}
