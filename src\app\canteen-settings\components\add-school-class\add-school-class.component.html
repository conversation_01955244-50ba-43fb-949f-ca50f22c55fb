<mat-dialog-content>
  <modal-header [title]="GetTitle()" (close)="closeModal()"></modal-header>

  <form [formGroup]="form" class="cashlessForm">
    <mat-form-field appearance="outline">
      <mat-label>Class Name</mat-label>
      <input matInput placeholder="Enter class Name" formControlName="name" type="text" />
      <mat-error *ngIf="name.invalid">{{ getErrorMessageName() }}</mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>Teacher Name</mat-label>
      <input matInput placeholder="Enter teacher name" formControlName="teacher" type="text" />
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>Display Order</mat-label>
      <input matInput placeholder="Display Order" formControlName="sortOrder" type="number" step="1"/>
    </mat-form-field>

    <div class="buttonsWrapper">
      <basic-form-buttons
        (saveEvent)="onSubmit()"
        (cancelEvent)="closeModal()"
        [disableSaveButton]="!form.valid"
        [loading]="loading"
      ></basic-form-buttons>
    </div>
  </form>
</mat-dialog-content>
