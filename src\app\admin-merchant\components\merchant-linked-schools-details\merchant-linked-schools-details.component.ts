import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { KeyValue, Location } from '@angular/common';
import { Subject } from 'rxjs';
import * as moment from 'moment';
import { FormControl, FormGroup, Validators } from '@angular/forms';

//models
import {
  MerchantStatus,
  School,
  BaseComponent,
  ResultDialogData,
  Merchant,
  MerchantTypeEnum,
  MerchantStatusEnum,
  SchoolsInvoicingRequest,
  SchoolInternalStatusEnum,
  SchoolInternalStatus,
  SchoolWithBillingDetails,
} from '../../../sharedModels';

//services
import { MerchantService, SpinnerService } from 'src/app/sharedServices';

//dialog imports
import { MatDialog } from '@angular/material/dialog';
import { DialogResultComponent } from 'src/app/shared/components/';

export enum SchoolInvoicingFunctions {
  unlinkSchool,
  updateStatus,
  cancelChanges,
  saveChanges,
}
@Component({
  selector: 'app-merchant-linked-schools-details',
  templateUrl: './merchant-linked-schools-details.component.html',
  styleUrls: ['./merchant-linked-schools-details.component.scss'],
})
export class MerchantLinkedSchoolsDetailsComponent extends BaseComponent implements OnInit {
  form: FormGroup;
  schoolStatusOptions: string[];
  MerchantStatus = MerchantStatus;
  currentSchool: SchoolWithBillingDetails;
  currentMerchant: Merchant;
  merchantType: MerchantTypeEnum;
  isUniformMerchant: boolean = false;
  currentAbsorbFees: boolean;
  currentBillingStatus: string;
  editMode: boolean = false;
  listStatus: KeyValue<string, string>[] = [];
  confirmStatusChange: Subject<void> = new Subject<void>();

  // const
  invalidValueError: string = 'Invalid value entered';

  constructor(
    private location: Location,
    private merchantService: MerchantService,
    private spinnerService: SpinnerService,
    public dialog: MatDialog
  ) {
    super();
  }

  ngOnInit(): void {
    if (!history.state.row) {
      this.goBack();
    }

    // setup list status
    this.listStatus.push({
      key: SchoolInternalStatusEnum.Default.toString(),
      value: SchoolInternalStatus.Default,
    });
    this.listStatus.push({ key: SchoolInternalStatusEnum.Skip.toString(), value: SchoolInternalStatus.Skip });
    this.listStatus.push({ key: SchoolInternalStatusEnum.Demo.toString(), value: SchoolInternalStatus.Demo });
    this.listStatus.push({
      key: SchoolInternalStatusEnum.Internal.toString(),
      value: SchoolInternalStatus.Internal,
    });
    this.listStatus.push({
      key: SchoolInternalStatusEnum.Extra.toString(),
      value: SchoolInternalStatus.Extra,
    });
    this.listStatus.push({
      key: SchoolInternalStatusEnum.Error.toString(),
      value: SchoolInternalStatus.Error,
    });

    this.currentSchool = history.state.row;
    this.currentMerchant = history.state.selectedMerchant;
    this.merchantType = history.state.merchantType;
    this.currentBillingStatus = this.currentSchool.BillingStatus;
    this.schoolStatusOptions = [this.MerchantStatus.Active, this.MerchantStatus.Churned];
    this.CreateForm();
  }

  goBack() {
    this.location.back();
  }

  CreateForm() {
    this.form = new FormGroup({
      canteenFee: new FormControl(this.currentSchool.CanteenFee, [
        Validators.required,
        Validators.min(0),
        Validators.max(1),
      ]),
      startDate: new FormControl(this.currentSchool.BillingStartDate, [Validators.required]),
      internalStatus: new FormControl(this.currentSchool.InternalStatus.toString(), [Validators.required]),
      churnedDate: new FormControl(this.currentSchool.BillingEndDate),
      absorbFees: new FormControl(this.currentSchool.CanteenAbsorbsFees),
      instructions: new FormControl(this.currentSchool.SpecialInstructions),
      waiveEventOrderFee: new FormControl(this.currentSchool.WaiveEventOrderFee),
    });
    this.form.disable();

    this.internalStatus.setValue(this.currentSchool.InternalStatus.toString());
  }

  get canteenFee() {
    return this.form.get('canteenFee');
  }

  get waiveEventOrderFee() {
    return this.form.get('waiveEventOrderFee');
  }

  get startDate() {
    return this.form.get('startDate');
  }

  get internalStatus() {
    return this.form.get('internalStatus');
  }

  get churnedDate() {
    return this.form.get('churnedDate');
  }

  get instructions() {
    return this.form.get('instructions');
  }

  get absorbFees() {
    return this.form.get('absorbFees');
  }

  updateInformation(callingFunction: SchoolInvoicingFunctions) {
    this.spinnerService.start();

    if(this.currentSchool.BillingStatus == 'Churned') {
  
      this.merchantService
        .UpdateSchoolInternalStatus(this.currentSchool.SchoolCanteenId, +this.internalStatus.value,)
        .subscribe({
          next: res => {
            this.spinnerService.stop();
            this.closeForm();
  
            if (callingFunction === SchoolInvoicingFunctions.updateStatus) {
              this.confirmStatusChange.next();
              this.SuccessPopUp('School status successfully updated.', 1);
            } else {
              this.SuccessPopUp('School invoicing data successfully updated.', 3);
            }
          },
          error: error => {
            this.spinnerService.stop();
            let errorMessage = this.GetApiError(error, 'School invoicing data unable to be updated.');
            this.SomethingWentWrongPopup(errorMessage, callingFunction);
            this.handleErrorFromService(error);
          },
        });
    }else{
      let request: SchoolsInvoicingRequest = {
        SchoolId: this.currentSchool.SchoolId,
        CanteenId: this.currentMerchant.canteenId,
        CanteenFee: this.canteenFee.value,
        StartDate: this.currentSchool.StartDate,
        BillingStartDate: this.startDate.value ? moment(this.startDate.value).format('YYYY-MM-DD') : null,
        BillingEndDate: this.churnedDate.value ? moment(this.churnedDate.value).format('YYYY-MM-DD') : null,
        SpecialInstructions: this.instructions.value,
        CanteenType: this.merchantType,
        BillingStatus: MerchantStatusEnum[this.currentBillingStatus],
        InternalStatus: +this.internalStatus.value,
        CanteenAbsorbsFees: this.absorbFees.value,
        WaiveEventOrderFee: this.waiveEventOrderFee.value
      };
  
      this.merchantService
        .UpdateSchoolInvoicingDetails(request.CanteenId, request.SchoolId, request)
        .subscribe({
          next: res => {
            this.spinnerService.stop();
            this.closeForm();
            this.currentSchool.CanteenFee = request.CanteenFee;
            this.currentSchool.BillingStartDate = request.StartDate;
            this.currentSchool.BillingEndDate = request.BillingEndDate;
            this.currentSchool.SpecialInstructions = request.SpecialInstructions;
            this.currentSchool.CanteenAbsorbsFees = request.CanteenAbsorbsFees;
  
            if (callingFunction === SchoolInvoicingFunctions.updateStatus) {
              this.confirmStatusChange.next();
              this.SuccessPopUp('School status successfully updated.', 1);
            } else {
              this.SuccessPopUp('School invoicing data successfully updated.', 3);
            }
          },
          error: error => {
            this.spinnerService.stop();
            let errorMessage = this.GetApiError(error, 'School invoicing data unable to be updated.');
            this.SomethingWentWrongPopup(errorMessage, callingFunction);
            this.handleErrorFromService(error);
          },
        });
    }
  }

  ///////////////////////
  // Edit form
  ///////////////////////

  GetApiError(error: any, defaultErrorMessage: string): string {
    if (!error || !error?.errors) {
      return defaultErrorMessage ?? 'Something went wrong';
    }

    let errorArray = [];
    let errorList = Object.entries(error.errors);
    errorList.forEach(err => errorArray.push(err[1][0]));

    // Show first error
    if (errorArray.length > 0) {
      return errorArray[0];
    }

    return defaultErrorMessage ?? 'Something went wrong';
  }

  clickEdit() {
    this.editMode = true;
    this.form.enable();
    if (!this.currentSchool.BillingEndDate) {
      this.churnedDate.disable();
    }

    if(!this.isNotChurned()){
      this.canteenFee.disable();
      this.startDate.disable();
      this.churnedDate.disable();
      this.absorbFees.disable();
      this.instructions.disable();
      this.waiveEventOrderFee.disable();
    }
  }

  isNotChurned(){
    return this.currentSchool.BillingStatus != 'Churned'
  }

  areYouSureCancel() {
    let dialogMessage = 'Are you sure you want to cancel your changes?';
    let cancelMessage = 'No, Return';
    let confirmMessage = 'Yes, Cancel';
    this.areYouSurePopUp(dialogMessage, cancelMessage, confirmMessage, 2);
  }

  cancelChanges() {
    this.form.patchValue({
      canteenFee: this.currentSchool.CanteenFee,
      startDate: this.currentSchool.BillingStartDate,
      churnedDate: this.currentSchool.BillingEndDate,
      instructions: this.currentSchool.SpecialInstructions,
      absorbFees: this.currentSchool.CanteenAbsorbsFees,
    });
    this.closeForm();
  }

  closeForm() {
    this.merchantService.setDisableMode(false);
    this.editMode = false;
    this.form.disable();
  }

  ///////////////////////
  // Unpdate school status
  ///////////////////////

  UpdateBillingStatus(billingStatus: string) {
    this.currentBillingStatus = billingStatus;
    let dialogMessage = `Are you sure you want to change the status of ${this.currentSchool.Name} to ${this.currentBillingStatus}?`;
    let cancelMessage = 'No, Cancel';
    let confirmMessage = 'Yes, Change status';
    this.areYouSurePopUp(dialogMessage, cancelMessage, confirmMessage, 1);
  }

  ///////////////////////
  // Unlinking Schools
  ///////////////////////

  unlinkSchool() {
    let dialogMessage = `Are you sure you want to unlink '${this.currentSchool.Name}' from '${this.currentMerchant.merchantName}'?`;
    let cancelMessage = 'Cancel';
    let confirmMessage = 'Yes, unlink now';
    this.areYouSurePopUp(dialogMessage, cancelMessage, confirmMessage, 0);
  }

  confirmUnlinkSchool() {
    this.spinnerService.start();
    this.merchantService
      .UnlinkSchoolFromMerchant(this.currentMerchant.canteenId, this.currentSchool.SchoolId)
      .subscribe({
        next: res => {
          this.spinnerService.stop();
          this.SuccessPopUp('School unlinked from merchant successfully.', 0);
        },
        error: error => {
          this.spinnerService.stop();
          let errorMessage =
            typeof error === 'string' ? error : 'School could not be unlinked from merchant.';
          this.SomethingWentWrongPopup(errorMessage, 0);
          this.handleErrorFromService(error);
        },
      });
  }

  ///////////////////////
  // Pop ups
  ///////////////////////

  triggerFunction(functionEnum: SchoolInvoicingFunctions) {
    switch (functionEnum) {
      case 0:
        this.confirmUnlinkSchool();
        break;
      case 1:
        this.updateInformation(1);
        break;
      case 2:
        this.cancelChanges();
        break;
      case 3:
        this.updateInformation(3);
        break;
      default:
        break;
    }
  }

  SomethingWentWrongPopup(dialogMessage: string, functionEnum: SchoolInvoicingFunctions) {
    let data = new ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = dialogMessage;
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.triggerFunction(functionEnum);
      }
    });
  }

  SuccessPopUp(dialogMessage: string, functionEnum: SchoolInvoicingFunctions) {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = dialogMessage;
    data.ConfirmButton = 'Okay';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        if (functionEnum === SchoolInvoicingFunctions.unlinkSchool) {
          this.location.back();
        }
      }
    });
  }

  areYouSurePopUp(
    dialogMessage: string,
    cancelMessage: string,
    confirmMessage: string,
    functionEnum: SchoolInvoicingFunctions
  ) {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Are you sure?';
    data.TextLine1 = dialogMessage;
    data.CancelButton = cancelMessage;
    data.ConfirmButton = confirmMessage;

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.triggerFunction(functionEnum);
      }
    });
  }
}
