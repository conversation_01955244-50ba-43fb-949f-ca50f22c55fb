import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { environment } from '../../../../environments/environment';

//Models
import { BaseComponent, ImageUrlEnum, MenuItemImage } from 'src/app/sharedModels';

//Services
import { MenuEditorApiService, SpinnerService } from 'src/app/sharedServices';


@Component({
  selector: 'item-image-upload-form',
  templateUrl: './item-image-upload-form.component.html',
  styleUrls: ['./item-image-upload-form.component.scss'],
})
export class ItemImageUploadFormComponent extends BaseComponent implements OnInit {
  @Input() menuItemId: number;
  @Input() itemImages: MenuItemImage[];
  @Input() schoolCode: string;
  @Output() updateImages: EventEmitter<MenuItemImage[]> = new EventEmitter<MenuItemImage[]>();
  showError: boolean = false;

  constructor(private spinnerService: SpinnerService, private menuEditorAPIService: MenuEditorApiService) {
    super();
  }

  ngOnInit(): void {}

  GetUrlImage(img: MenuItemImage): string {
    if (img.ImageUrl.indexOf('https:') >= 0) {
      return img.ImageUrl;
    } else {
      return environment.blobStorage + ImageUrlEnum.ItemsLG + img.ImageUrl;
    }
  }

  NewImage(url: string) {
    let newImage = new MenuItemImage();
    newImage.ImageUrl = url;
    newImage.MenuItemId = this.menuItemId;

    if (!this.itemImages) {
      this.itemImages = [];
    }
    this.itemImages.push(newImage);
  }

  DeleteImage(image: MenuItemImage) {
    this.showError = false;
    this.spinnerService.start();

    this.menuEditorAPIService.DeleteItemImage(image).subscribe(
      res => {
        this.removeImageFromImageList(image.ImageUrl);
        this.spinnerService.stop();
      },
      error => {
        this.showError = true;
        this.spinnerService.stop();
      }
    );
  }

  removeImageFromImageList(imageUrl: string) {
    let index = this.itemImages.findIndex(x => x.ImageUrl == imageUrl);

    if (index >= 0) {
      this.itemImages.splice(index, 1);
    }
  }

  onSubmit() {
    this.updateImages.emit(this.itemImages);
  }
}
