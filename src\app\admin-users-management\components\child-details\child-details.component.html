<school-panel fullHeight="true" title="Child Details">
  <ul class="infoList">
    <li><strong>User id:</strong> {{ child.UserId }}</li>
    <li><strong>First name:</strong> {{ child.FirstName }}</li>
    <li><strong>Last name:</strong> {{ child.Lastname }}</li>
    <li><strong>School</strong> {{ child.SchoolName }}</li>
    <li><strong>Class:</strong> {{ child.ClassName }}</li>
    <li><strong>Class ID:</strong> {{ child.ClassId }}</li>
    <li><strong>Is Active:</strong> {{ child.IsActive }}</li>
  </ul>

  <div class="row pb-3">
    <div class="pr-3">
      <icon-button text="Edit Details" buttonStyle="secondary" (onPress)="editUser()">
        <img src="/assets/icons/black-pencil.svg" alt="pencil" />
      </icon-button>
    </div>
    <basic-button-v2
      *ngIf="canArchiveChild()"
      (onPress)="archiveClicked()"
      text="Archive User"
      buttonStyle="archive"
    ></basic-button-v2>
  </div>
</school-panel>
