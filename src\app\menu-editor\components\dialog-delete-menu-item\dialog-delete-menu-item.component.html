<mat-dialog-content>
  <div class="container">
    <p class="title">Are you sure you want to delete this {{ data.displayOption }}?</p>
    <p class="title">This action cannot be undone.</p>
    <div class="buttonsWrapper">
      <button class="PrimaryButton button buttonSpace" (click)="onDeletePress()">Delete</button>
      <button class="PrimaryButton button" (click)="onNoClick()">Cancel</button>
    </div>
  </div>
</mat-dialog-content>
