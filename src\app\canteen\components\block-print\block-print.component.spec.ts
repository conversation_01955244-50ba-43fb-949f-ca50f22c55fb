import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { BlockPrintComponent } from './block-print.component';

describe('BlockPrintComponent', () => {
  let component: BlockPrintComponent;
  let fixture: ComponentFixture<BlockPrintComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [BlockPrintComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BlockPrintComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
