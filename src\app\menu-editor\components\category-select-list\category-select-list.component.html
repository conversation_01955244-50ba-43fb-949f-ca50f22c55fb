
<div *ngIf="listCategories">
      <mat-form-field appearance="outline">
        <mat-label>Category</mat-label>
        <mat-select (selectionChange)="valuechanged($event)" [value]="0" required>
          <mat-option [value]="0">All</mat-option>
          <mat-option *ngFor="let category of listCategories" [value]="category.MenuCategoryId">{{
            category.CategoryName
          }}</mat-option>
        </mat-select>
      </mat-form-field>
  </div>
