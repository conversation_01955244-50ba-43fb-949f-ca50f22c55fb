import { Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { NgClass } from '@angular/common';

//Models
import { BaseComponent, Roles } from 'src/app/sharedModels';

//Services
import { ReconciliationService } from 'src/app/sharedServices/reconciliation/reconciliation.service';

//dialog
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

//ngrx
import { MatCheckboxChange } from '@angular/material/checkbox';
import { SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'dialog-user-reconciliation',
  templateUrl: './dialog-user-reconciliation.component.html',
  styleUrls: ['./dialog-user-reconciliation.component.scss'],
})
export class DialogUserReconciliationComponent extends BaseComponent implements OnInit {
  form: FormGroup;
  selectRange: boolean;
  isReconciliationFail: boolean;
  isReconciliationSuccess: boolean;
  title: string;
  balance: string;
  reconciliationDateUtc: string;
  roles = Roles;

  constructor(
    public dialogRef: MatDialogRef<DialogUserReconciliationComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    private reconciliationApiService: ReconciliationService,
    public dialog: MatDialog,
    private spinnerService: SpinnerService
  ) {
    super();
  }

  ngOnInit(): void {
    this.selectRange = false;
    this.balance = this.data.SpriggyBalance;
    this.isReconciliationFail = false;
    this.isReconciliationSuccess = false;

    this.getReconciliationRecord();
  }

  closeModal() {
    this.dialogRef.close();
  }

  getReconciliationRecord(): void {
    this.spinnerService.start();
    this.reconciliationApiService.GetReconciliationRecord(this.data.UserId).subscribe((response: any) => {
      if (response.reconciliationDateUtc == null) {
        this.reconciliationDateUtc = 'N/A';
      } else {
        this.reconciliationDateUtc = response.reconciliationDateUtc;
      }
      this.spinnerService.stop();
    });
  }

  updateSelectRange(result: MatCheckboxChange) {
    this.selectRange = result.checked;
    this.CreateForm();
  }

  CreateForm() {
    this.form = new FormGroup({
      selectedStartDate: new FormControl(),
      selectedEndDate: new FormControl(),
    });
  }

  get selectedStartDate() {
    return this.form.get('selectedStartDate');
  }

  get selectedEndDate() {
    return this.form.get('selectedEndDate');
  }

  onSubmit() {
    let startDate = null;
    let endDate = null;

    if (this.selectRange) {
      startDate = this.selectedStartDate.value.utc().format('YYYY-MM-DD');
      endDate = this.selectedEndDate.value.utc().format('YYYY-MM-DD');
    }

    this.reconciliationApiService.ReconcileUser(this.data.UserId, startDate, endDate).subscribe({
      next: (response: any) => {
        if (response.isReconciled == false) {
          this.isReconciliationFail = true;
        } else {
          this.reconciliationDateUtc = response.reconciliationDateUtc;
          this.isReconciliationSuccess = true;
        }
      },
      error: (error: any) => {
        this.isReconciliationFail = true;
      },
    });
  }
}
