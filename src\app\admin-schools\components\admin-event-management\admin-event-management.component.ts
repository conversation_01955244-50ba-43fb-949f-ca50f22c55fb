import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

// models
import { MerchantPerSchoolResponse } from 'src/app/sharedModels';

@Component({
  selector: 'admin-event-management',
  templateUrl: './admin-event-management.component.html',
})
export class AdminEventManagementComponent implements OnInit {
  schoolId: number;
  merchants: MerchantPerSchoolResponse[];

  constructor(route: ActivatedRoute) {
    this.schoolId = route.snapshot.parent.params['id'];
    this.merchants = route.snapshot.data['merchants'];
  }

  ngOnInit(): void {}
}
