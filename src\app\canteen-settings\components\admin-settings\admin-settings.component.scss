.backButton {
  color: orange;
}

.titleWrapper {
  display: flex;
  flex-direction: row;
  margin-bottom: 13px;
  align-items: center;
}

.title {
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 22px;
  margin: 0;
  margin-left: 8px;
}

.schoolSelection {
  background-color: #ffffff;
  border-radius: 12px;
  padding-top: 10px;
  padding-left: 20px;
  padding-right: 20px;
  margin-bottom: 16px;
}

.tableTitle {
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 22px;
  color: #000000;
  margin: 0;
}

.usersTable {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
}

.mat-mdc-cell {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 20px;
  color: #000000;
  border-bottom: 1px solid #dadada;
  padding-right: 5px;
  padding-left: 5px;
}

.centerAlignment {
  text-align: center;
}

.mat-elevation-z8 {
  box-shadow: 0 0 #000000;
}

.inActiveCheckbox {
  border: 1px solid #333b44;
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.checkboxWrapper {
  display: flex;
  justify-content: center;
}

.emailHeader {
  padding-left: 5px;
}

.adminLabel {
  display: inline;
  color: #6d7681;
}

.titleDescriptionWrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px 18px;
}
