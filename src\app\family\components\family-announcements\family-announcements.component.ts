import { Component, Input, OnInit } from '@angular/core';

// models
import { Notice } from 'src/app/sharedModels';

@Component({
  selector: 'family-announcements',
  templateUrl: './family-announcements.component.html',
  styleUrls: ['./family-announcements.component.scss'],
})
export class FamilyAnnouncementsComponent implements OnInit {
  @Input() announcements: Notice[];
  openedAnnouncements: number[] = [];

  constructor() {}

  ngOnInit(): void {}

  OnMoreInfoPress(id) {
    if (this.openedAnnouncements.includes(id)) {
      this.openedAnnouncements = this.openedAnnouncements.filter(announcementId => announcementId !== id);
    } else {
      this.openedAnnouncements = [...this.openedAnnouncements, id];
    }
  }
}
