import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { UserCashless } from 'src/app/sharedModels';

@Component({
  selector: 'user-management-parents-list',
  templateUrl: './parents-list.component.html',
  styleUrls: ['./parents-list.component.scss'],
})
export class ParentsListComponent implements OnInit {
  @Input() parents: UserCashless[];
  dataSource = new MatTableDataSource<UserCashless>();
  displayedColumns: string[] = ['name', 'id', 'mobile', 'email', 'balance'];

  constructor() {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    for (const propName in changes) {
      switch (propName) {
        case 'parents':
          this.dataSource.data = this.parents;
          break;

        default:
          break;
      }
    }
  }
}
