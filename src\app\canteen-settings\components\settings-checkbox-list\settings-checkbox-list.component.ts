import { KeyValue } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { ControlContainer, FormGroup } from '@angular/forms';

@Component({
  selector: 'settings-checkbox-list',
  templateUrl: './settings-checkbox-list.component.html',
  styleUrls: ['./settings-checkbox-list.component.scss'],
})
export class SettingsCheckboxListComponent implements OnInit {
  @Input() schoolId: number; //used to track form value changes 
  @Input() title: string;
  @Input() description: string;
  @Input() values: KeyValue<string, string>[] = [];
  @Output() submit = new EventEmitter();
  public form: FormGroup;

  constructor(private controlContainer: ControlContainer) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.schoolId?.currentValue) {
      this.form = <FormGroup>this.controlContainer.control;
    }
  }

  submitForm() {
    this.submit.emit();
  }
}
