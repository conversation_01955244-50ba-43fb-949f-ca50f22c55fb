import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { DateSchool } from 'src/app/sharedModels';

@Component({
  selector: 'school-close-date-table',
  templateUrl: './school-close-date-table.component.html',
  styleUrls: ['./school-close-date-table.component.scss'],
})
export class SchoolCloseDateTableComponent implements OnInit {
  @Input() dates: DateSchool[];
  @Output() archive: EventEmitter<number> = new EventEmitter();
  displayedColumns: string[] = ['StartDate', 'EndDate', 'Actions'];

  constructor() {}

  ngOnInit(): void {}

  archiveClicked(dateId: number) {
    this.archive.emit(dateId);
  }
}
