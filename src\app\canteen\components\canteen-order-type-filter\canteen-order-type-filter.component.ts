import { Component, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';

// models
import { CanteenStatusTotals, CanteenStatusEnum, MerchantTypeEnum, MenuTypeEnum } from 'src/app/sharedModels';

// services
import { OrderStatusService } from 'src/app/sharedServices';

@Component({
  selector: 'canteen-order-type-filter',
  templateUrl: './canteen-order-type-filter.component.html',
  styleUrls: ['./canteen-order-type-filter.component.scss'],
})
export class CanteenOrderTypeFilterComponent implements OnInit {
  @Input() merchantType: MerchantTypeEnum;
  @Input() formGroup: FormGroup;
  @Input() forReports: boolean;
  @Input() statusTotals: CanteenStatusTotals;
  filterForm: FormGroup;
  formTitle: string;
  isUniformShop: boolean;
  isEventMerchant: boolean;
  MenuTypeEnum = MenuTypeEnum;

  constructor(private orderStatusService: OrderStatusService) {}

  ngOnInit() {
    if (!this.formGroup) {
      this._createForm();
    }
  }

  ngOnChanges() {
    this.isUniformShop = this.merchantType == MerchantTypeEnum.Uniform;
    this.isEventMerchant = this.merchantType == MerchantTypeEnum.Event;
    this.formTitle = this.isUniformShop ? 'Order Status' : 'Order Type';
  }

  getForm() {
    if (!this.formGroup) {
      return this.filterForm;
    }
    return this.formGroup;
  }

  /**
   * Setup the filters form and listen to events
   */
  private _createForm() {
    this.filterForm = new FormGroup({
      uniNew: new FormControl(true),
      uniProcessing: new FormControl(true),
      uniReady: new FormControl(true),
      uniCompleted: new FormControl(true),
    });

    this.filterForm.valueChanges.subscribe(x => {
      let activeCanteenStatuses = [];

      if (this.uniProcessing.value) {
        activeCanteenStatuses.push(CanteenStatusEnum.Processing);
      }
      if (this.uniReady.value) {
        activeCanteenStatuses.push(CanteenStatusEnum.Ready);
      }
      if (this.uniCompleted.value) {
        activeCanteenStatuses.push(CanteenStatusEnum.Completed);
      }
      if (this.uniNew.value) {
        activeCanteenStatuses.push(CanteenStatusEnum.New);
      }

      //save orderStatus values to subscription
      this.orderStatusService.setOrderStatus(activeCanteenStatuses);
    });
  }

  get uniProcessing() {
    return this.filterForm.get('uniProcessing');
  }

  get uniReady() {
    return this.filterForm.get('uniReady');
  }

  get uniCompleted() {
    return this.filterForm.get('uniCompleted');
  }

  get uniNew() {
    return this.filterForm.get('uniNew');
  }
}
