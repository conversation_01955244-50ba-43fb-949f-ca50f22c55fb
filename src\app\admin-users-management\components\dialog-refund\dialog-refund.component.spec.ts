import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogRefundComponent } from './dialog-refund.component';

describe('DialogConfirmV2Component', () => {
  let component: DialogRefundComponent;
  let fixture: ComponentFixture<DialogRefundComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogRefundComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(DialogRefundComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
