<ng-container *ngIf="!categoryAdded">
  <div class="row no-gutters paddingLine">
    <div class="col-12">
      <h3 *ngIf="!categoryAdded" class="titleDialog">Add Category</h3>
      <h3 *ngIf="categoryAdded" class="titleDialog">Category added!</h3>
    </div>
  </div>

  <div *ngIf="!categoryAdded">
    <div class="row no-gutters paddingLine">
      <div class="col-12">
        <mat-form-field appearance="outline">
          <input matInput placeholder="Name" type="text" [(ngModel)]="categoryName" required />
        </mat-form-field>
      </div>
    </div>

    <div class="row no-gutters paddingLine">
      <div class="col-12">
        <button mat-flat-button type="button" class="SecondaryButton" (click)="Close()">Cancel</button>
      </div>
    </div>
    <div class="row no-gutters paddingLine">
      <div class="col-12">
        <button type="button" class="PrimaryButton" (click)="SubmitCategory()">Add</button>
      </div>
    </div>
  </div>

  <div *ngIf="categoryAdded">
    <div class="row no-gutters paddingLine">
      <div class="col-12">
        <button type="button" class="PrimaryButton" (click)="Close()">Close</button>
      </div>
    </div>
  </div>
</ng-container>

<div *ngIf="isProcessing" class="spinnerDialog">
  <app-spinner [manual]="true"></app-spinner>
</div>
