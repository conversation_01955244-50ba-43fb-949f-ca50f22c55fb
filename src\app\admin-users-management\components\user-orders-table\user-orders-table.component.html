<div class="row" *ngIf="dataSource.data?.length > 0; else noOrdersRow">
  <div class="col-12 ordersTable">
    <table mat-table [dataSource]="dataSource">
      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef>Order Id</th>
        <td mat-cell *matCellDef="let element">{{ element.OrderId }}</td>
      </ng-container>

      <ng-container matColumnDef="child">
        <th mat-header-cell *matHeaderCellDef>Child</th>
        <td mat-cell *matCellDef="let element">{{ element.StudentName }}</td>
      </ng-container>

      <ng-container matColumnDef="orderDate">
        <th mat-header-cell *matHeaderCellDef>Order Date</th>
        <td mat-cell *matCellDef="let element">{{ element.OrderDate | date }}</td>
      </ng-container>

      <ng-container matColumnDef="dateCreated">
        <th mat-header-cell *matHeaderCellDef>Date Created</th>
        <td mat-cell *matCellDef="let element">{{ element.DateCreatedUtc | date : 'MMM d, y, h:mm a' }}</td>
      </ng-container>

      <ng-container matColumnDef="menuType">
        <th mat-header-cell *matHeaderCellDef>Menu Type</th>
        <td mat-cell *matCellDef="let element">{{ element.MenuType | merchantMenuName }}</td>
      </ng-container>

      <ng-container matColumnDef="menuName">
        <th mat-header-cell *matHeaderCellDef>Menu Name</th>
        <td mat-cell *matCellDef="let element">{{ element.MenuType | customMenuName : element.MenuName }}</td>
      </ng-container>

      <ng-container matColumnDef="price">
        <th mat-header-cell *matHeaderCellDef>Price</th>
        <td mat-cell *matCellDef="let element">{{ element.Price | currency }}</td>
      </ng-container>

      <ng-container matColumnDef="items">
        <th mat-header-cell *matHeaderCellDef>Items</th>
        <td mat-cell *matCellDef="let order">
          <ul *ngIf="order.Items">
            <li *ngFor="let item of order.Items">
              {{ item.Quantity }} x {{ item.Name }}
              <span *ngIf="item.SelectedOptions?.length > 0">
                ({{ item.SelectedOptions | orderOptionsString }})
              </span>
              ({{ [item] | calculateOrderItemsPrice }})
            </li>
          </ul>
        </td>
      </ng-container>

      <ng-container matColumnDef="pickedUp">
        <th mat-header-cell *matHeaderCellDef>Picked Up</th>
        <td mat-cell *matCellDef="let element; let i = index">
          <mat-checkbox [checked]="element.PickedUp" (click)="clickPickedUp($event, element)">
            <span *ngIf="element.PickedUp">
              {{ element.PickedUpDateUtc | date : 'mediumDate' }} ({{
                element.PickedUpDateUtc | date : 'shortTime'
              }})</span
            >
          </mat-checkbox>
        </td>
      </ng-container>

      <ng-container matColumnDef="refunded">
        <th mat-header-cell *matHeaderCellDef>Refunded</th>
        <td mat-cell *matCellDef="let element">{{ element.RefundedDate | date : 'MMM d, y, h:mm a' }}</td>
      </ng-container>

      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let element">
          <span
            class="status-text general-button"
            [ngClass]="{
              'status-red':
                element.OrderStatusId == orderStatus.Error || element.OrderStatusId == orderStatus.Cancelled,
              'status-green': element.OrderStatusId == orderStatus.Completed
            }"
          >
            {{ element.OrderStatusId | orderStatusString }}
          </span>
        </td>
      </ng-container>

      <ng-container matColumnDef="message">
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let element">{{ element.ErrorMessage }}</td>
      </ng-container>

      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let element">
          <button class="general-button options-button" [matMenuTriggerFor]="actions">Actions</button>
          <mat-menu #actions="matMenu">
            <button mat-menu-item 
              *ngIf="!element.RefundedDate && element.OrderStatusId != orderStatus.Cancelled" 
              (click)="ConfirmRefund(element)">
              Refund
            </button>
            <button
              mat-menu-item
              *ngIf="
                element.OrderStatusId != orderStatus.Completed && element.OrderStatusId != orderStatus.New
              "
              (click)="ClickValid(element.OrderId)"
            >
              Confirm Order
            </button>
            <button mat-menu-item *ngIf="!element.RefundedDate && element.OrderStatusId == orderStatus.Completed" (click)="EditOrder(element)">
              Edit Order
            </button>
            <button mat-menu-item *ngIf="!element.RefundedDate && element.OrderStatusId != orderStatus.Cancelled" (click)="ChangeMenuType(element)">
              {{ GetTextChange(element) }}
            </button>
          </mat-menu>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>

    <schools-table-paginator
      [length]="totalRows"
      [selectedPage]="selectedPage"
      [numberRows]="numberRows"
      (pageChanged)="pageChangedEvent($event)"
    ></schools-table-paginator>
  </div>
</div>

<ng-template #noOrdersRow>
  <no-data-table-row text="No orders"></no-data-table-row>
</ng-template>
