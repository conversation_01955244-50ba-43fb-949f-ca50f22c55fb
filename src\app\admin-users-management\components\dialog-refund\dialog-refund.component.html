<mat-dialog-content>
  <modal-header title="Refund" (close)="closeModal()"></modal-header>

  <h5 class="mt-0 mb-2">{{ data.title }}</h5>

  <form *ngIf="form" [formGroup]="form">
    <div class="pb-1">
      <mat-radio-group formControlName="refundType" class="radio-button-wrapper">
        <mat-radio-button [value]="AdminRefundType.FullRefund" class="mr-4"
          ><span class="ml-1">Refund the complete order (including {{originalOrder.OrderFee | currency}} fee)</span></mat-radio-button
        >
        <mat-radio-button [value]="AdminRefundType.PartialRefund"
          ><span class="ml-1">Refund selected Items from order</span></mat-radio-button
        >
      </mat-radio-group>

      <form
        *ngIf="refundType.value === AdminRefundType.PartialRefund"
        [formGroup]="orderItemsForm"
        class="cashlessForm pl-4 pt-3"
      >
        <h5 class="m-0">Please select the items to refund</h5>

        <div class="order-line" *ngFor="let oi of originalOrder.Items; let i = index">
          <div class="pb-2" formArrayName="orderItems">
            <mat-checkbox class="checkbox" [formControlName]="i"></mat-checkbox>
          </div>

          <div formArrayName="orderItemsQuantity">
            <select [formControlName]="i" class="quantitySelectList">
              <option
                *ngFor="
                  let item of [].constructor(originalOrder.Items[i].Quantity);
                  let index = index
                "
                [value]="index + 1"
              >
                {{ index + 1 }}
              </option>
            </select>
          </div>

          <p class="checkboxText">
              {{ oi.Name }}
            <span *ngIf="oi.SelectedOptions?.length > 0">
              ({{ oi.SelectedOptions | orderOptionsString }})
            </span>
            ({{ [oi] | calculateOrderItemsPrice }})
          </p>
        </div>
        <p *ngIf="selectItemsErrorMessage" class="errorMessage">{{ partialRefundErrorMessage }}</p>
      </form>

      <div class="pt-2 pb-2">
        <input-text-v2
          placeholder="Reason for return"
          formControlName="description"
          [showErrorOutline]="description.invalid && description.touched"
        ></input-text-v2>
      </div>
    </div>
  </form>
  <span class="d-flex align-items-center pt-1">
    <mat-icon class="pr-2">info</mat-icon>
    <p class="m-0">The refund will edit the order in the same time as transferring the money</p>
  </span>

  <div class="row pt-2">
    <div class="col-12 choices">
      <basic-button-v2 text="Cancel" buttonStyle="secondary" [mat-dialog-close]="false"></basic-button-v2>
      <basic-button-v2
        [text]="getRefundPriceToDisplay() | moneyButtonDisplay : 'Refund'"
        buttonStyle="primary"
        (onPress)="confirmClick()"
        [loading]="buttonLoading"
      ></basic-button-v2>
    </div>
  </div>
  <p *ngIf="showError" class="errorMessage text-align-right">{{ API_ERROR_MESSAGE }}</p>
</mat-dialog-content>
