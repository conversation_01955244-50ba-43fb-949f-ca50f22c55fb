import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import * as _ from 'lodash';
import { Location } from '@angular/common';

// Models
import { UserCashless, BaseComponent } from '../../../sharedModels';

// Services
import { UserService, SpinnerService, PhoneNumberService } from '../../../sharedServices';

@Component({
  selector: 'app-account-edit',
  templateUrl: './account-edit.component.html',
  styleUrls: ['./account-edit.component.scss'],
})
export class AccountEditComponent extends BaseComponent implements OnInit {
  private user: UserCashless;
  accountForm: FormGroup;

  constructor(
    private userService: UserService,
    private spinnerService: SpinnerService,
    private routerService: Router,
    private phoneNumberService: PhoneNumberService,
    private _location: Location
  ) {
    super();
  }

  ngOnInit(): void {
    this.user = this.userService.GetUserConnected();

    if (!this.user) {
      this.user = new UserCashless();
    }

    const formattedMobile = this.phoneNumberService.formatToPhone(this.user.Mobile);

    this.accountForm = new FormGroup({
      name: new FormControl(this.user.FirstName, [Validators.required]),
      lastname: new FormControl(this.user.Lastname, [Validators.required]),
      email: new FormControl(this.user.Email, [Validators.required]),
      mobile: new FormControl(formattedMobile, [Validators.required, Validators.minLength(12)]),
    });

    this.accountForm.get('email').disable();
  }

  backClicked(): void {
    this._location.back();
  }

  get name(): AbstractControl<any, any> {
    return this.accountForm.get('name');
  }

  get lastname(): AbstractControl<any, any> {
    return this.accountForm.get('lastname');
  }

  get mobile(): AbstractControl<any, any> {
    return this.accountForm.get('mobile');
  }

  getErrorMessageName(): string {
    return this.accountForm.get('name').hasError('required') ? 'You must enter a value' : '';
  }

  getErrorMessageLastname(): string {
    return this.accountForm.get('lastname').hasError('required') ? 'You must enter a value' : '';
  }

  getErrorMessageMobile(): string {
    return this.accountForm.get('mobile').hasError('required') || this.accountForm.get('mobile').invalid
      ? 'You must enter a valid mobile number'
      : '';
  }

  private ConvertUser(): UserCashless {
    const userCopy = _.cloneDeep(this.user);
    userCopy.FirstName = this.name.value;
    userCopy.Lastname = this.lastname.value;
    userCopy.Mobile = this.phoneNumberService.serverMobileNumber(this.mobile.value);
    return userCopy;
  }

  ClickSubmit(): void {
    if (this.accountForm.invalid) {
      this.accountForm.markAllAsTouched();
      return;
    }
    if (this.accountForm.valid) {
      this.spinnerService.start();
      let user = this.ConvertUser();

      this.userService.UpsertUser(user).subscribe({
        next: (userResponse: UserCashless) => {
          this.userService.SetUserConnected(userResponse);
          this.spinnerService.stop();
          this.routerService.navigate(['/family/account']);
        },
        error: error => {
          this.spinnerService.stop();
          this.handleErrorFromService(error);
        },
      });
    }
  }

  formatMobileInput(): void {
    let res = this.phoneNumberService.aussieMobileBranded(this.mobile.value);
    this.mobile.setValue(res);
  }
}
