import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// material
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';

// module
import { ManageOrderRoutingModule } from './manage-order-routing.module';
import { SharedModule } from '../shared/shared.module';
import { PaymentModule } from '../payment/payment.module';
import { SchoolsButtonModule } from '../schools-button/schools-button.module';

// components
import {
  ManageOrderComponent,
  ShoppingCartComponent,
  SelectedOrderHistoryComponent,
  DialogPlaceOrderComponent,
  OrdersDetailsComponent,
  OrdersPlacedComponent,
  FiltersComponent,
  FilterMenuDateSheetComponent,
  FiltersItemsComponent,
  FiltersItemsSheetComponent,
  ReorderFilterComponent,
} from './components';
import { NewOrderDetailsComponent } from './components/new-order-details/new-order-details.component';
import { EditOrderDetailsComponent } from './components/edit-order-details/edit-order-details.component';
import { OrderHistoryModule } from '../order-history/order-history.module';

//pipes
import {
  AbsoluteMoneyValuePipe,
  CalculateOrderItemsPricePipe,
  MoneyButtonDisplayPipe,
  PlaceOrderButtonTextPipe,
  CalculateCartItemsPricePipe,
} from '../sharedPipes';

import { ClearCartButtonComponent } from './components/clear-cart-button/clear-cart-button.component';
import { CategoryTileComponent } from './components/category-tile/category-tile.component';

@NgModule({
  declarations: [
    ManageOrderComponent,
    ShoppingCartComponent,
    SelectedOrderHistoryComponent,
    DialogPlaceOrderComponent,
    OrdersDetailsComponent,
    OrdersPlacedComponent,
    FiltersComponent,
    FilterMenuDateSheetComponent,
    FiltersItemsComponent,
    FiltersItemsSheetComponent,
    NewOrderDetailsComponent,
    EditOrderDetailsComponent,
  ],
  imports: [
    ReorderFilterComponent,
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    ManageOrderRoutingModule,
    SharedModule,
    PaymentModule,
    SchoolsButtonModule,
    // material
    MatFormFieldModule,
    MatCardModule,
    MatDialogModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
    MatExpansionModule,
    MatBottomSheetModule,
    MatCheckboxModule,
    MatTooltipModule,
    OrderHistoryModule,
    MoneyButtonDisplayPipe,
    CalculateOrderItemsPricePipe,
    PlaceOrderButtonTextPipe,
    AbsoluteMoneyValuePipe,
    ClearCartButtonComponent,
    CalculateCartItemsPricePipe,
    CategoryTileComponent,
  ],
})
export class ManageOrderModule {}
