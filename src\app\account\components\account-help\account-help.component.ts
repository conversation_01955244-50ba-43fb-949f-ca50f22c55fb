import { Component } from '@angular/core';
import { Location } from '@angular/common';

// Services
import { UserService } from '../../../sharedServices';

@Component({
  selector: 'app-account-help',
  templateUrl: './account-help.component.html',
  styleUrls: ['./account-help.component.scss'],
})
export class AccountHelpComponent {
  constructor(private userService: UserService, private _location: Location) {}

  triggerIntercom(): void {
    this.userService.openIntercom();
  }

  backClicked(): void {
    this._location.back();
  }
}
