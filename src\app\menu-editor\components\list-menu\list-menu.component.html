<form *ngIf="form" [formGroup]="form" class="cashlessForm">
  <div class="row justify-content-sm-center">
    <div class="col-12 col-sm-4">
      <mat-form-field appearance="outline" *ngIf="listSchools">
        <mat-select Place formControlName="school" required>
          <mat-option *ngFor="let school of listSchools" [value]="school.SchoolId">{{
            school.Name
          }}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="col-12 col-sm-4">
      <!-- <button mat-flat-button color="primary" type="button" routerLink="/canteen/menus/add">Add Menu</button> -->
    </div>
  </div>
</form>

<div class="row">
  <div class="col-8 offset-md-2">
    <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 tableau">
      <!--- Note that these columns can be defined in any order.
                The actual rendered columns are set as a property on the row definition" -->

      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef>Name</th>
        <td mat-cell *matCellDef="let element">{{ element.Name }}</td>
      </ng-container>

      <ng-container matColumnDef="friendlyName">
        <th mat-header-cell *matHeaderCellDef>friendlyName</th>
        <td mat-cell *matCellDef="let element">{{ element.friendlyName }}</td>
      </ng-container>

      <ng-container matColumnDef="IsActive">
        <th mat-header-cell *matHeaderCellDef>Is active</th>
        <td mat-cell *matCellDef="let element">
          <mat-checkbox [checked]="element.IsActive" [disabled]="true"></mat-checkbox>
        </td>
      </ng-container>

      <ng-container matColumnDef="options" stickyEnd>
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let element">
          <mat-icon matTooltip="Edit" class="actionTableau" [routerLink]="['../menu', element.MenuId]"
            >edit</mat-icon
          >
          <!-- <mat-icon matTooltip="Delete" class="actionTableau">delete_forever</mat-icon> -->
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
  </div>
</div>
