import { Component, OnInit, EventEmitter, Input, Output, OnChanges } from '@angular/core';
import { BaseComponent, ConfirmModal, SchoolClass } from '../../../sharedModels';
import { Subscription } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { SchoolClassesService, SpinnerService } from '../../../sharedServices';
import { MatTableDataSource } from '@angular/material/table';
import { Sort } from '@angular/material/sort';
import { DialogConfirmComponent } from 'src/app/shared/components';

const compare = (a: number | string | boolean, b: number | string | boolean, isAsc: boolean) => {
  if (a < b) {
    return isAsc ? -1 : 1;
  }
  if (a === null) {
    return 1;
  }
  if (b === null) {
    return -1;
  }
  if (a > b) {
    return isAsc ? 1 : -1;
  }
  return 0;
};

@Component({
  selector: 'classes-list',
  templateUrl: './classes-list.component.html',
  styleUrls: ['./classes-list.component.scss'],
})
export class ClassesListComponent extends BaseComponent implements OnInit, OnChanges {
  @Output() editClicked: EventEmitter<SchoolClass> = new EventEmitter();
  displayedColumns: string[] = ['name', 'teacher', 'Actions'];
  @Input() listClasses: SchoolClass[];
  private addClassSubscription: Subscription;
  dataSource = new MatTableDataSource<SchoolClass>();

  constructor(
    public dialog: MatDialog,
    private spinnerService: SpinnerService,
    private schoolClassService: SchoolClassesService
  ) {
    super();
  }

  ngOnInit() {}

  ngOnChanges() {
    this.RefreshTable(this.listClasses);
  }

  sortData(sort: Sort) {
    const data = this.listClasses ? [...this.listClasses] : [];
    if (!sort.active || sort.direction === '') {
      this.dataSource.data = this.listClasses;
      return;
    }

    const newData = data.sort((a, b) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'name':
          return compare(a.Name.toLocaleLowerCase(), b.Name.toLocaleLowerCase(), isAsc);
        case 'teacher':
          return compare(
            a.Teacher ? a.Teacher.toLocaleLowerCase() : null,
            b.Teacher ? b.Teacher.toLocaleLowerCase() : null,
            isAsc
          );
        case 'IsActive':
          return compare(!a.IsActive, !b.IsActive, isAsc);
        default:
          return 0;
      }
    });
    this.dataSource.data = newData;
  }

  onEdit(classe: SchoolClass) {
    this.editClicked.emit(classe);
  }

  RefreshTable(listClasses: SchoolClass[]) {
    this.dataSource.data = listClasses;
  }

  ArchiveClicked(classe: SchoolClass) {
    let data = new ConfirmModal();
    data.Title = 'Archive Class';
    data.Text =
      "Archiving this class will inactivate the class permanently and can not be undone. Parents will be instructed to change their child's class. Proceed?";
    data.CancelButton = 'No';
    data.ConfirmButton = 'Yes';

    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.ArchiveClickConfirmed(classe);
      }
    });
  }

  ArchiveClickConfirmed(classe: SchoolClass) {
    this.spinnerService.start();
    classe.IsArchived = true;

    this.schoolClassService.ArchiveClassApi(classe).subscribe({
      next: (response: SchoolClass) => {
        let index = this.listClasses.findIndex(i => i.ClassId == classe.ClassId);
        if (index > -1) {
          this.listClasses.splice(index, 1);
          this.RefreshTable(this.listClasses);
        }

        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }
}
