import { Component, Input, OnInit } from '@angular/core';

//Models
import { AddRemoveStockRequest, BaseComponent, Stock } from 'src/app/sharedModels';

//Services
import { MenuEditorApiService, SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'item-stock-table',
  templateUrl: './item-stock-table.component.html',
  styleUrls: ['./item-stock-table.component.scss'],
})
export class ItemStockTableComponent extends BaseComponent implements OnInit {
  @Input() menuItemId: number;
  @Input() stockList: Stock[] = [];
  @Input() merchantId: number;
  @Input() menuId: number;

  constructor(private spinnerService: SpinnerService, private menuEditorAPIService: MenuEditorApiService) {
    super();
  }

  ngOnInit(): void {}

  RemoveStock() {
    this.spinnerService.start();

    let request: AddRemoveStockRequest = new AddRemoveStockRequest();
    request.ItemId = this.menuItemId;
    request.StockId = null;
    request.CanteenId = this.merchantId;
    request.MenuId = this.menuId;

    this.menuEditorAPIService.RemoveStockFromItemAPI(request).subscribe({
      next: (response: any) => {
        this.stockList = null;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }
}
