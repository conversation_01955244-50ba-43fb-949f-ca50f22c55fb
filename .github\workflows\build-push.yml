####
#
# Do not edit this worklow, managed by -> https://github.com/piggymoney/github-central-workflows
# Located -> https://github.com/piggymoney/github-central-workflows/blob/main/workflows/terraform-workflows/terraform-aws.yml
####
name: "AWS-Deploy"

on:
  workflow_call:
    inputs:
      DEPLOY:
        required: true
        type: string
      ENVIRONMENT:
        required: true
        type: string

permissions:
  id-token: write
  contents: read

jobs:
  build:
    runs-on: prod-runner
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # Only need everything on prod to get tags
          fetch-depth: ${{ inputs.ENVIRONMENT == 'prod' && '0' || 1 }}

      - name: Set Environment Variables from Environment Config File
        uses: ./.github/actions/setvars
        with:
          varFilePath: ./.github/variables/${{ inputs.ENVIRONMENT }}.env
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
        
      - name: Load environment.ts
        run: |
          mkdir -p ./src/environments/
          aws secretsmanager get-secret-value --secret-id '${{ env.SECRET_ID }}' --query SecretString --output text > ./src/environments/${{ env.SECRET_OUTPUT }}
          # This file must exist so just copying across
          cp ./src/environments/${{ env.SECRET_OUTPUT }} ./src/environments/environment.ts

      - uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Link Angular
        run: npm link @angular/cli
      
      - name: Install Angular and Dependencies
        run: npm ci
      
      - name: Build Environment
        run: |
          case "${{ inputs.ENVIRONMENT }}" in

          "dev")  ng build --configuration dev
              ;;
          "test")  ng build --configuration uat
              ;;
          "prod")  ng build --configuration production
              ;;
          esac
      
      # Unit tests not currently running
      # - name: Run tests
      #   run: |
      #     ng test 
      
      # no longer used as no dev and we push to test on master

      # - name: Check for test tag on prod execute
      #   if: inputs.ENVIRONMENT == 'prod' && startsWith(github.ref, 'refs/tags/v')
      #   run: |
      #     # Prod tag
      #     echo ${{github.ref_name}}
          
      #     # Get all tags
      #     touch ./tag-output.txt
      #     git tag --points-at HEAD >> ./tag-output.txt
      #     echo "./tag-output.txt"
      #     echo "----"
      #     cat ./tag-output.txt

      #     # Remove prod tag from output
      #     sed -i '0,/${{github.ref_name}}/d' ./tag-output.txt
      #     # MAC version cause different
      #     #sed -i '' '/${{github.ref_name}}/d' ./tag-output.txt
          
      #     echo "after sed ./tag-output.txt"
      #     echo "----"
      #     cat ./tag-output.txt
      #     cat ./tag-output.txt

      #     echo "Starting loop"
      #     # Look through file and if matches, exit succesfully
      #     while IFS= read -r line; do
      #       echo "Text read from file: $line"
      #       if [[ "$line" == *"${{github.ref_name}}"* ]]; then
      #         echo "It's there."
      #         exit 0
      #       fi
      #     done < ./tag-output.txt
      #     echo "Finishing loop"
      #     exit 1


  build-deploy:
    # Duplicate steps, but only takes 1.5 min to run the above and would take almost as long to upload artificate and pull down with more complications. This has just been done to make GUI look nicer (seperate build and deploy)
    runs-on: prod-runner
    needs: [build]
    if: inputs.DEPLOY == 'true'
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set Environment Variables from Environment Config File
        uses: ./.github/actions/setvars
        with:
          varFilePath: ./.github/variables/${{ inputs.ENVIRONMENT }}.env    
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Load environment.ts
        run: |
          mkdir -p ./src/environments/
          aws secretsmanager get-secret-value --secret-id '${{ env.SECRET_ID }}' --query SecretString --output text > ./src/environments/${{ env.SECRET_OUTPUT }}
          # This file must exist so just copying across
          cp ./src/environments/${{ env.SECRET_OUTPUT }} ./src/environments/environment.ts

      - uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Link Angular
        run: npm link @angular/cli
      
      - run: |
          ls ./src/environments/
      
      - name: Install Angular and Dependencies
        run: npm ci
      
      - name: Build Environment
        run: |
          case "${{ inputs.ENVIRONMENT }}" in

          "dev")  ng build
              ;;
          "test")  ng build --configuration uat
              ;;
          "prod")  ng build --configuration production
              ;;
          esac

      - name: Upload to S3
        run: |
          cd dist/cashless
          aws s3 sync ./ s3://${{ inputs.ENVIRONMENT }}-cashless/

      - name: Invalidate Cloudfront Cache
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ env.DISTRIBUTION_ID }} --paths '/*'

      - name: Post to a Slack channel Success
        uses: slackapi/slack-github-action@v1
        with:
          # Check which channel to post to
          channel-id: ${{ inputs.ENVIRONMENT == 'prod' && 'deployment' || 'test-deployment-notifications'}}
          payload: |
            {
              "text": "✅ Success Deployment of ${{inputs.ENVIRONMENT }} ${{ env.SLACK_NOTIFICATION_NAME }}",
              "attachments": [
                {
                  "pretext": "",
                  "color": "#8fce00",
                  "fields": [
                    {
                      "title": "",
                      "short": true,
                      "value": "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"  
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_WEBHOOK_INTEGRATION }}
      
