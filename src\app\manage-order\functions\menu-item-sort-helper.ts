import { Category, MenuItem, RefinedOrderItem } from 'src/app/sharedModels';

export function GetMenuItemsThatMatchOrderItemMenuId(
  menuJSON: Category[],
  menuItemIdsToFind: number[]
): MenuItem[] {
  const allMenuItems: MenuItem[] = menuJSON.reduce(
    (accumulator, currentValue) => [...accumulator, ...currentValue.item],
    []
  );
  return allMenuItems.filter(x => menuItemIdsToFind.includes(x.MenuItemId));
}

export function UpdateOrderItemsWithMenuData(
  menuJSON: Category[],
  orderItems: RefinedOrderItem[]
): RefinedOrderItem[] {
  const orderItemIdList = orderItems.map(item => item.MenuItemId);
  const matchingMenuItems: MenuItem[] = GetMenuItemsThatMatchOrderItemMenuId(menuJSON, orderItemIdList);

  const updatedOrderItems = orderItems.map(x => {
    const match = matchingMenuItems.find(menuItem => menuItem.MenuItemId === x.MenuItemId);
    if (match) {
      return { ...x, Name: match.Name, ItemPriceIncGst: match.Price };
    }
  });

  return updatedOrderItems;
}
