@import '../../../../styles/cashless-theme.scss';

.backButton {
  color: orange;
}

.optionTitle {
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  color: #000000;
  margin: 0;
}

.tableCat {
  width: 100%;
  margin-top: 10px;
  padding-bottom: 80px;
  background-color: #ffffff;

  & th {
    color: $grey-1;
    text-align: left;
    padding-left: 20px;
    padding-top: 10px;
    padding-bottom: 16px;
    border-bottom: 1px solid #dadada;
  }
}

.catLine {
  padding: 16px 0px 16px 20px;
  border-bottom: 1px solid #dadada;
}

.iconTable {
  margin-right: 20px;
  cursor: pointer;
}

.closePopupIcon {
  text-align: center;
  padding-top: 20px;
}

.imageBlock {
  padding: 4px;
  margin: 4px;
  overflow: hidden;
  border-radius: 12px;
  border: 2px solid transparent;
  cursor: pointer;

  &.selected {
    border-color: $orange-1;
  }

  .imageWrapper {
    height: 100%;
    width: 100%;
    overflow: hidden;

    img {
      width: 100%;
      height: auto;
      cursor: pointer;
    }
  }
}

.cancelButton {
  border: 1px solid $orange-1;
  color: $orange-1;
  margin-right: 15px;
  font-size: 16px;
  border-radius: 14px;
}

.headerAddCategory {
  display: flex;
  justify-content: flex-end;
}
