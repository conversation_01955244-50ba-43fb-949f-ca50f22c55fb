@import '../../../../styles/cashless-navbar.scss';
@import '../../../../styles/cashless-font.scss';
@import '../../../../styles//cashless-theme.scss';

mat-sidenav-container {
  height: 100%;
}

.mobileNav {
  height: 60px;
  width: 100%;
  position: fixed;
  background-color: white;
  border-bottom: 1px solid rgb(194, 194, 194);
  z-index: 10000;

  .IconMenuMobile {
    padding-top: 20px;
    padding-right: 45px;
    float: right;
    width: 100%;
    height: 100%;
  }

  & .childrenCol {
    padding-top: 10px;
  }
}

mat-nav-list {
  & a {
    & .nav-tab {
      display: block;
      padding: 15px;
      padding-left: 60px;
      padding-right: 60px;
      text-decoration: none;
      color: black;
    }

    &.activeLink {
      color: $orange-2;
      text-decoration: underline;

      & .nav-tab {
        color: $orange-2;
      }
    }
  }
}

.textApp {
  text-align: center;
  margin-top: 40px;
  margin-bottom: 5px;
}
.appLink {
  width: 100%;
  font-size: 18px;
}

.mobileContainer {
  @media (max-width: $breakpoint-md) {
    margin-top: 60px;
    margin-bottom: 80px;
  }
  background-color: #f2f2f2;
}

// .accountBalanceText{
//     margin-top:8px;
//     font-family: 'bariol_regular';
// }
