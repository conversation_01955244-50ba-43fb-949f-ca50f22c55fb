import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';

// models
import { CanteenSchool } from 'src/app/sharedModels';

@Component({
  selector: 'canteen-order-school-filter',
  templateUrl: './canteen-order-school-filter.component.html',
  styleUrls: ['./canteen-order-school-filter.component.scss'],
})
export class CanteenOrderSchoolFilterComponent implements OnChanges {
  @Input() schools: CanteenSchool[];
  @Output() schoolsSelected: EventEmitter<number[]> = new EventEmitter();
  selectedValueSchools: number[] = [];
  constructor() {}

  ngOnChanges(changes: SimpleChanges) {
    for (const propName in changes) {
      switch (propName) {
        case 'schools':
          this._prepareList();
          break;

        default:
          break;
      }
    }
  }

  /**
   * Prepare the school list to display
   */
  private _prepareList() {
    if (this.schools && this.schools.length > 0) {
      //this.usePrintingApp = this.listSchools[0].UsePrintingApp;
      this.selectedValueSchools = [];

      this.schools.forEach(s => {
        this.selectedValueSchools.push(s.SchoolId);
      });

      // if there is a preferred school, use it.
      let savedValue: number[] = JSON.parse(localStorage.getItem('prefSchoolId'));
      if (savedValue != null && savedValue.length > 0) {
        let index = this.selectedValueSchools.find(i => i == savedValue[0]);

        if (index > -1) {
          this.selectedValueSchools = savedValue;
        }
      } else {
        this._SaveSchools();
      }

      // trigger selected schools
      this.schoolsSelected.emit(this.selectedValueSchools);
    }
  }

  /**
   * Check if the given schoolId is selected
   * @param schoolId
   * @returns
   */
  IsChecked(schoolId: number): boolean {
    return this.selectedValueSchools.findIndex(s => s == schoolId) > -1;
  }

  /**
   * Checkbox value changed
   * @param event Checkbox event
   */
  CheckboxChanged(event: MatCheckboxChange) {
    const schoolId = +event.source.name;

    if (event.checked) {
      this.selectedValueSchools.push(schoolId);
    } else {
      let index = this.selectedValueSchools.findIndex(i => i == schoolId);
      if (index > -1) {
        this.selectedValueSchools.splice(index, 1);
      }
    }

    // save school selection
    this._SaveSchools();

    // trigger selected schools
    this.schoolsSelected.emit(this.selectedValueSchools);
  }

  /**
   * Save the schools list in localStorage
   * @param schools
   */
  private _SaveSchools() {
    localStorage.setItem('prefSchoolId', JSON.stringify(this.selectedValueSchools));
  }

  /**
   * Check if all schools are selected
   * @returns
   */
  IsAllSelected(): boolean {
    return this.selectedValueSchools.length == this.schools.length;
  }

  /**
   * Check if the selected schools list is empty
   * @returns
   */
  IsNoSchoolsSelected(): boolean {
    return this.selectedValueSchools.length == 0;
  }

  /**
   * Select all the schools available
   */
  SelectAll() {
    if (!this.IsAllSelected()) {
      this.selectedValueSchools = [];

      this.schools.forEach(s => {
        this.selectedValueSchools.push(s.SchoolId);
      });

      this._SaveSchools();

      // trigger selected schools
      this.schoolsSelected.emit(this.selectedValueSchools);
    }
  }

  /**
   * Clear selected list
   */
  Clear() {
    if (!this.IsNoSchoolsSelected()) {
      this.selectedValueSchools = [];
    }
  }
}
