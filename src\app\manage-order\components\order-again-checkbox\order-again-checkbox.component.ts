import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SchoolsButtonModule } from 'src/app/schools-button/schools-button.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ReOrderDateAvailability } from 'src/app/sharedModels';

@Component({
  selector: 'order-again-checkbox',
  standalone: true,
  imports: [
    MatCheckboxModule,
    MatFormFieldModule,
    SchoolsButtonModule,
    ReactiveFormsModule,
    FormsModule,
    CommonModule,
  ],
  templateUrl: './order-again-checkbox.component.html',
  styleUrls: ['./order-again-checkbox.component.scss'],
})
export class OrderAgainCheckboxComponent {
  @Input() dateFormArray: FormArray;
  @Input() dateList: ReOrderDateAvailability[];
  @Input() menuName: string;
  @Input() form: FormGroup;
  disabled = false;
}
