<div class="row">
  <div class="col-8">
    <h3>
      Processing
      <span *ngIf="orders">( {{ orders.length }} )</span>
    </h3>
  </div>
  <div class="col-4">
    <ng-container *ngIf="orders && orders.length != 0">
      <button
        mat-button
        [matMenuTriggerFor]="dropDownMenu"
        [disabled]="noOrdersSelected()"
        class="defaultButton action-button"
      >
        Actions
      </button>

      <mat-menu #dropDownMenu="matMenu">
        <button mat-menu-item (click)="PushToProcessedClicked()">Move selected to 'Processed'</button>
        <button mat-menu-item (click)="PushToErrorClicked()">Move selected to 'Error'</button>
      </mat-menu>
    </ng-container>
  </div>
</div>

<div *ngIf="orders.length" class="order-wrapper">
  <mat-checkbox
    [indeterminate]="true"
    [checked]="allComplete"
    [indeterminate]="someComplete()"
    (change)="setAll($event.checked)"
  >
  </mat-checkbox>

  <div *ngFor="let ord of orders" class="row errorRow">
    <div class="col-2 d-flex align-items-center">
      <mat-checkbox [(ngModel)]="ord.selected" (ngModelChange)="updateAllComplete()"> </mat-checkbox>
    </div>
    <div class="col-10">
      <h4>Order id: {{ ord.orderId }}</h4>
      <p class="subTitle mb-2">{{ ord.parentName }} - {{ ord.studentName }}</p>
      <p class="subTitle"><u>Amount:</u> ${{ ord.orderAmountIncGst | number : '1.2-2' }}</p>
      <p class="subTitle"><u>Menu type:</u> {{ ord.menuType }}</p>
      <p class="subTitle"><u>Cut-off time:</u> {{ ord.cutOffTime }}</p>
      <p class="subTitle"><u>Logic app:</u> {{ ord.logicAppIdentifier }}</p>
      <p class="subTitle">
        <u>Created:</u> {{ LocalDate(ord.dateCreatedUtc) }}
        <span *ngIf="CheckDate(ord.dateCreatedUtc)" class="stuck">Stuck</span>
      </p>
    </div>
  </div>
</div>
