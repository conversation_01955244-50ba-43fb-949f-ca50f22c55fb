@import 'src/styles/schools-table';
@import 'src/styles/schools-theme';

.status-text {
  font-weight: 700;
  background-color: $warning-background-orange;
}

.status-green {
  background-color: $success-green;
}

.status-red {
  background-color: $error-background-red;
}

.general-button {
  padding: 5px 8px;
  border-radius: 8px;
}

.options-button {
  background-color: #ffffff;
  outline: none;
  border: 1px solid $neutral-grey-2;
  cursor: pointer;
}

.ordersTable {
  width: 100%;
  max-width: 100%;
  overflow: auto;

  & * {
    -webkit-user-select: text; /* Chrome 49+ */
    -moz-user-select: text; /* Firefox 43+ */
    -ms-user-select: text; /* No support yet */
    user-select: text; /* Likely future */
  }
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
.ordersTable ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background-color: $status-success;
}
/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
.ordersTable ::ng-deep .mat-mdc-checkbox.mat-accent.mat-checkbox-indeterminate .mat-checkbox-background {
  background-color: $status-success;
}
/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
.ordersTable ::ng-deep.mat-mdc-checkbox .mat-checkbox-frame {
  border-color: $text-dark-medium;
}
