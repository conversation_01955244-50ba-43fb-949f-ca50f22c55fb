import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { Observable } from 'rxjs';
import { MenuTypeEnum, RefinedOrder } from 'src/app/sharedModels';
import { NumberOfPreviousOrdersRequest } from 'src/app/sharedModels/order/orderRequests';

// services
import { OrderApiService } from 'src/app/sharedServices';

export const UniformEventHistoryResolver: ResolveFn<RefinedOrder[]> = (
  route: ActivatedRouteSnapshot
): Observable<any> => {
  const schoolService = inject(OrderApiService);
  const menuTypeFromPath = route.routeConfig.path;
  const menuType = getMenuTypeFromPath(menuTypeFromPath);

  const request: NumberOfPreviousOrdersRequest = {
    StartIndex: 0,
    NumberOfOrders: 30,
    MenuType: menuType,
  };

  return schoolService.getUniformOrEventOrderHistoryByParent(request);
};

function getMenuTypeFromPath(pathValue: string): MenuTypeEnum {
  const capitalizeValue = pathValue[0].toUpperCase() + pathValue.slice(1);
  return MenuTypeEnum[capitalizeValue];
}
