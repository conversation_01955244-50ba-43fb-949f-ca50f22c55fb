import { ChangeDetectionStrategy, Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { UserCashless } from 'src/app/sharedModels';

@Component({
  selector: 'user-management-children-list',
  templateUrl: './children-list.component.html',
  styleUrls: ['./children-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChildrenListComponent implements OnInit {
  @Input() children: UserCashless[];
  dataSource = new MatTableDataSource<UserCashless>();
  displayedColumns: string[] = ['name', 'id', 'school', 'class'];

  constructor() {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    for (const propName in changes) {
      switch (propName) {
        case 'children':
          this.dataSource.data = this.children;
          break;

        default:
          break;
      }
    }
  }
}
