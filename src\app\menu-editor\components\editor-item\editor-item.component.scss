@import '../../../../styles/cashless-theme.scss';

.menuItemForm {
  display: inline-block;
  .formInputs {
    width: 300px;
    max-width: 100%;

    mat-form-field {
      width: 100%;
    }
  }
}

h2 {
  color: $orange-3;
}

.subTitle {
  margin-bottom: 5px;
  margin-top: 30px;
  color: $orange-3;
}

.subTitleWrapper {
  display: flex;
}

.divButton {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.submitButton {
  width: 55px;
  height: 35px;
  font-size: 16px;
  padding: 10px;
}

.deleteButton {
  font-size: 16px;
  width: 100px;
  height: 35px;
  padding: 9px;
}

.bottomSpacing {
  margin-bottom: 70px;
}

.infoIcon {
  align-self: flex-end;
  margin-bottom: 2px;
  margin-left: 10px;
  cursor: pointer;
}

.noPadding {
  padding-right: 0;
  padding-left: 0;
}

::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 4px;
}
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}

table {
  width: 100%;

  & tr.line {
    height: 40px;

    &:hover {
      background-color: $grey-2;
    }

    &.lineSelected {
      background-color: $orange-1;
      color: white;
    }

    & td {
      text-align: center;
    }
  }

  & .actionTableau {
    cursor: pointer;
  }
}
