import { Component, Input, OnChang<PERSON>, On<PERSON><PERSON>roy, SimpleChanges } from '@angular/core';

//Models
import {
  MenuTypeEnum,
  OrderBlockInfo,
  MenuNames,
  UserCashless,
  GroupedOrderBlockInfo,
} from '../../../sharedModels';

// services
import { ParentHomeService, UserService } from 'src/app/sharedServices';
import { Store, select } from '@ngrx/store';
import { FamilyState } from 'src/app/states';
import { Subscription } from 'rxjs';
import { selectedChild } from 'src/app/states/children/children.selectors';

@Component({
  selector: 'family-table-week-order',
  templateUrl: './table-week-order.component.html',
  styleUrls: ['./table-week-order.component.scss'],
})
export class TableWeekOrderComponent implements OnChanges, OnDestroy {
  @Input() orderTableData: GroupedOrderBlockInfo;
  menuNames: MenuNames = null;
  isLoading: boolean = false;
  selectChild: UserCashless;
  private selectedChildSubscription: Subscription;
  private loadingSubscription: Subscription;
  menuTypeEnum = MenuTypeEnum;

  recessDayData: OrderBlockInfo[];
  lunchDayData: OrderBlockInfo[];

  recessName: string;
  lunchName: string;

  constructor(
    private store: Store<{ family: FamilyState }>,
    private userService: UserService,
    private parentHomeService: ParentHomeService
  ) {}

  ngOnInit(): void {
    this.loadingSubscription = this.parentHomeService.loadingEvent$.subscribe((res: boolean) => {
      this.isLoading = res;
    });

    this.selectedChildSubscription = this.store
      .pipe(select(selectedChild))
      .subscribe((child: UserCashless) => {
        this.selectChild = child;
      });
  }

  ngOnDestroy(): void {
    if (this.loadingSubscription) {
      this.loadingSubscription.unsubscribe;
    }
    if (this.selectedChildSubscription) {
      this.selectedChildSubscription.unsubscribe;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    //trigger getWeekOrderData() on every onChanges event - this works while this component has one input var
    this.getWeekOrderData();
  }

  getWeekOrderData() {
    if (!this.orderTableData) {
      this.initNoCanteenMenus();
    }
    this.recessDayData = this.orderTableData?.Recess;
    this.lunchDayData = this.orderTableData?.Lunch;
    this.getMenuName();
  }

  initNoCanteenMenus() {
    this.recessDayData = null;
    this.lunchDayData = null;
    this.lunchName = null;
    this.recessName = null;
  }

  getMenuName(): void {
    this.recessName = this.recessDayData?.length > 0 ? this.recessDayData[0].menuName : null;
    this.lunchName = this.lunchDayData?.length > 0 ? this.lunchDayData[0].menuName : null;
  }

  showOrderTable(): boolean {
    return (!this.isLoading && this.recessDayData?.length > 0) || this.lunchDayData?.length > 0;
  }

  triggerIntercom(): void {
    this.userService.openIntercom();
  }
}
