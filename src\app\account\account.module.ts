import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccountComponent } from './components/account/account.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// google material
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

import { AccountRoutingModule } from './account-routing.module';
import { SchoolsButtonModule } from '../schools-button/schools-button.module';
import { PaymentModule } from '../payment/payment.module';
import { SharedModule } from '../shared/shared.module';
import {
  AccountAboutComponent,
  AccountCloseComponent,
  AccountHelpComponent,
  BillingHistoryComponent,
  CloseAccountRowComponent,
  AccountTopupComponent,
  AccountEditComponent,
} from './components';

@NgModule({
  declarations: [
    AccountComponent,
    AccountEditComponent,
    AccountTopupComponent,
    AccountAboutComponent,
    BillingHistoryComponent,
    AccountCloseComponent,
    AccountHelpComponent,
    CloseAccountRowComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    AccountRoutingModule,
    PaymentModule,
    SharedModule,
    SchoolsButtonModule,
    // material
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
  ],
  exports: [AccountComponent],
})
export class AccountModule {}
