<div class="merchant-section">
  <div [ngClass]="{ disableCoverWhite: disableMode }"></div>
  <div class="details-header">
    <h4>Schools linked to Merchant</h4>
    <basic-button text="Link School" (onPress)="LinkSchoolClick()" [buttonStyle]="1"></basic-button>
  </div>

  <hr class="details-divider" />

  <table *ngIf="!isListEmpty()" mat-table [dataSource]="dataSource" class="table">
    <ng-container matColumnDef="school">
      <th mat-header-cell *matHeaderCellDef class="header">School</th>
      <td mat-cell *matCellDef="let element" class="noBorder">
        <h5>
          <strong>{{ element.Name }}</strong>
        </h5>
      </td>
    </ng-container>
    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef class="header">Status</th>
      <td mat-cell *matCellDef="let element" class="noBorder smallColumn">
        <h5>{{ element.BillingStatus }}</h5>
      </td>
    </ng-container>
    <ng-container matColumnDef="canteen-fee">
      <th mat-header-cell *matHeaderCellDef class="header">Canteen Fee</th>
      <td mat-cell *matCellDef="let element" class="noBorder smallColumn">
        <h5>{{ element.CanteenFee * 100 | number : '1.2' }}%</h5>
      </td>
    </ng-container>
    <ng-container matColumnDef="start-date">
      <th mat-header-cell *matHeaderCellDef class="header">Start Date</th>
      <td mat-cell *matCellDef="let element" class="noBorder smallColumn">
        <h5>{{ element.StartDate | date : 'dd/MM/yyyy' }}</h5>
      </td>
    </ng-container>
    <ng-container matColumnDef="invoicing-start-date">
      <th mat-header-cell *matHeaderCellDef class="header">Invoicing Start Date</th>
      <td mat-cell *matCellDef="let element" class="noBorder">
        <h5>{{ element.BillingStartDate | date : 'dd/MM/yyyy' }}</h5>
      </td>
    </ng-container>

    <ng-container matColumnDef="chevron" class="result-tab">
      <th mat-header-cell *matHeaderCellDef class="header smallColumn"></th>
      <td mat-cell *matCellDef="let element" class="noBorder smallColumn lastColumn">
        <mat-icon class="chevron">chevron_right</mat-icon>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr
      mat-row
      *matRowDef="let row; columns: displayedColumns"
      routerLink="./{{ selectedMerchant.canteenId }}/school/{{ row.SchoolId }}"
      [state]="{row, selectedMerchant, merchantType}"
    ></tr>
  </table>

  <!-- table row on empty -->
  <div *ngIf="isListEmpty()" class="emptyMessage">No linked schools</div>
</div>
