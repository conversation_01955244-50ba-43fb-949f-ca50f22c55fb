import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

// google material
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';

// modules
import { SharedModule } from '../shared/shared.module';

// Ngrx
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';

// reducers
import { childrenReducer, childrenFeatureKey } from '../states/children/children.reducer';

// Effects
import { ChildrenEffects } from '../states/children/children.effects';

import { ManageChildrenRoutingModule } from './manage-children-routing.module';
import { ListChildrenComponent, AddChildComponent } from './components';

@NgModule({
  declarations: [ListChildrenComponent, AddChildComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ManageChildrenRoutingModule,
    StoreModule.forFeature(childrenFeatureKey, childrenReducer),
    EffectsModule.forFeature([ChildrenEffects]),

    // material
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
    MatCheckboxModule,
    // other
    SharedModule,
  ],
})
export class ManageChildrenModule {}
