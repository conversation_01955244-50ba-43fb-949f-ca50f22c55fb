import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { DetailOptionItemsComponent } from './detail-option-items.component';

describe('DetailOptionItemsComponent', () => {
  let component: DetailOptionItemsComponent;
  let fixture: ComponentFixture<DetailOptionItemsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [DetailOptionItemsComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DetailOptionItemsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
