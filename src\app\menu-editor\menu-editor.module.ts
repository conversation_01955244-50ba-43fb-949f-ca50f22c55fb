import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// google material
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';

import { SharedModule } from '../shared/shared.module';
import { SharedToolsModule } from '../shared-tools/shared-tools.module';
import { SchoolsFormModule } from '../schools-form/schools-form.module';
import { MenuEditorRoutingModule } from './menu-editor-routing.module';
import {
  EditorNavComponent,
  ListItemsComponent,
  ListMenuComponent,
  EditorItemComponent,
  DialogAddCategoryComponent,
  DialogAddOptionComponent,
  EditorItemOptionComponent,
  EditorMenuComponent,
  ImageUploadComponent,
  ListOptionsComponent,
  DetailOptionComponent,
  DialogDeleteMenuItemComponent,
  CategoryItemTableComponent,
  DetailOptionFormComponent,
  LinkedItemTableComponent,
  DetailOptionChoicesComponent,
  DetailOptionItemsComponent,
  ListStocksComponent,
  DetailStockComponent,
  DetailStockItemsComponent,
  EditorCheckboxListComponent,
  ItemMenuEditorListComponent,
  ItemImageUploadFormComponent,
  ItemStockTableComponent,
  ItemCutOffTimePickerComponent,
  CutOffTimeFormatPipe
} from './components';
import { TwoDecimalDirective } from '../directives/two-decimal.directive';
import { OptionChipListComponent } from './components/option-chip-list/option-chip-list.component';
import { OptionChoicesV2Component } from './components/option-choices-v2/option-choices-v2.component';

@NgModule({
  declarations: [
    EditorNavComponent,
    ListItemsComponent,
    ListMenuComponent,
    EditorItemComponent,
    DialogAddCategoryComponent,
    DialogAddOptionComponent,
    EditorItemOptionComponent,
    EditorMenuComponent,
    ImageUploadComponent,
    ListOptionsComponent,
    DetailOptionComponent,
    DetailOptionFormComponent,
    DetailOptionChoicesComponent,
    DetailOptionItemsComponent,
    ListStocksComponent,
    DetailStockComponent,
    DetailStockItemsComponent,
    DialogDeleteMenuItemComponent,
    CategoryItemTableComponent,
    CategoryItemTableComponent,
    LinkedItemTableComponent,
    LinkedItemTableComponent,
    EditorCheckboxListComponent,
    ItemMenuEditorListComponent,
    ItemImageUploadFormComponent,
    ItemStockTableComponent,
    ItemCutOffTimePickerComponent,
    CutOffTimeFormatPipe,
    OptionChoicesV2Component
  ],
  imports: [
    CommonModule,
    MenuEditorRoutingModule,
    ReactiveFormsModule,
    FormsModule,
    SharedModule,
    SharedToolsModule,
    SchoolsFormModule,
    // material
    MatDialogModule,
    MatCheckboxModule,
    MatSelectModule,
    MatTableModule,
    MatRadioModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
    MatChipsModule,
    MatDatepickerModule,
    MatTooltipModule,
    TwoDecimalDirective,
    OptionChipListComponent,
  ],
  exports: [
    CategoryItemTableComponent,
    ItemCutOffTimePickerComponent,
    EditorCheckboxListComponent,
    DetailOptionChoicesComponent,
  ],
})
export class MenuEditorModule {}
