<div class="container-fluid">
  <a4-print-form
    (generate)="generateLabels($event)"
    (sliceLabels)="sliceLabels()"
    (updateCustomValue)="updateCustomValue($event)"
    (updateDisplaySchool)="updateDisplaySchoolValue($event)"
  ></a4-print-form>

  <div class="labelsContainer">
    <div class="row">
      <div class="col-6">
        <button
          *ngIf="IsDesktop()"
          mat-flat-button
          color="primary"
          type="button"
          [disabled]="!listLabels"
          printSectionId="labelsToPrint"
          [useExistingCss]="true"
          ngxPrint
        >
          Print
        </button>
        <button
          *ngIf="!IsDesktop()"
          mat-flat-button
          color="primary"
          type="button"
          [disabled]="!listLabels"
          (click)="ClickTabletPrint()"
        >
          Print
        </button>
      </div>
      <div class="col-6"></div>
    </div>
  </div>
</div>

<div id="labelsToPrint">
  <div class="pageA4" [ngClass]="{ custom: custom }" *ngFor="let page of a4PageLabel">
    <!-- fake labels are the empty ones starts from beginning -->
    <div *ngFor="let label of page.fakeLabels" class="colLabel" [ngClass]="{ custom: custom }">.</div>

    <div *ngFor="let label of page.listLabels" class="colLabel" [ngClass]="{ custom: custom }">
      <label-template [label]="label" [displaySchoolName]="displaySchoolName"></label-template>
    </div>
  </div>
</div>
