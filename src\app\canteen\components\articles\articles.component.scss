.backButton {
  color: orange;
  font-size: 14px;
}

.checkBox {
  color: #ff7a00;
}

.inActiveCheckbox {
  transition: border-color 90ms cubic-bezier(0, 0, 0.2, 0.1);
  border-width: 2px;
  border-style: solid;
  border-color: #ff7a00;
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.cardWrapper {
  background-color: white;
  border-radius: 12px;
  padding-top: 20px;
  padding-bottom: 23px;
  padding-right: 15px;
  padding-left: 20px;
}

.crossIconWrapper {
  display: flex;
  justify-content: flex-end;
  padding-right: 2px;
  cursor: pointer;
}

.invisible {
  display: none;
}

.inputTitle:last-of-type {
  margin-bottom: 0;
}

.checkboxLabel {
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  color: #000000;
}

.image {
  width: 28px;
}

.separator {
  width: 100%;
  background-color: #dadada;
  height: 1px;
  margin-top: 10px;
}

.infoBlock {
  padding-bottom: 30px;
}

.tableElement {
  padding-top: 10px;
  padding-bottom: 10px;
  padding-right: 10px;
  white-space: pre-line;
  vertical-align: top;
}

.closeBtn {
  padding: 5px;
}

.validationExplanation {
  & h4 {
    margin-bottom: 0;
    font-weight: bold;
  }

  & p {
    margin-bottom: 50px;
  }
}
