import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import * as moment from 'moment';

// Models
import { Menu, OrderBlockInfo, UserCashless, WeekDayStatusEnum } from 'src/app/sharedModels';

// Services
import { CashlessAppInsightsService } from 'src/app/sharedServices';
import { Subscription } from 'rxjs';
import { CreateOrderService } from 'src/app/sharedServices/order/create-order.service';

@Component({
  selector: 'family-menu-block',
  templateUrl: './family-menu-block.component.html',
  styleUrls: ['./family-menu-block.component.scss'],
})
export class FamilyMenuBlockComponent implements OnInit, OnDestroy {
  private listOrdersSubscription: Subscription;
  @Input() dayData: OrderBlockInfo;
  @Input() child: UserCashless;
  hasOrder: boolean;
  hasError: boolean;
  isProcessing: boolean;
  mobileShowText: boolean;
  isPassedOrClosed: boolean;
  menuList: Menu[];

  constructor(
    private router: Router,
    private appInsightsService: CashlessAppInsightsService,
    private createOrderService: CreateOrderService
  ) {}

  ngOnInit() {
    this.hasOrder = this.dayData.orderStatus.toLowerCase().includes(WeekDayStatusEnum.success);
    this.hasError = this.dayData.orderStatus.toLowerCase().includes(WeekDayStatusEnum.error);
    this.isProcessing = this.dayData.orderStatus == WeekDayStatusEnum.processing;
    this.mobileShowText = this.isMobileTextDisplay();
    this.isPassedOrClosed = this.isOrderPassedOrClosed();
  }

  ngOnDestroy(): void {
    this.listOrdersSubscription?.unsubscribe();
  }

  blockPressed(): void {
    if (
      this.dayData.orderStatus === WeekDayStatusEnum.passed ||
      this.dayData.orderStatus === WeekDayStatusEnum.closed
    ) {
      return;
    }
    this.prepareStateForNewOrder();
    if (this.dayData.orderStatus === WeekDayStatusEnum.order) {
      this.createOrder();
      return;
    }
    this.viewSelectedOrder();
  }

  viewSelectedOrder() {
    this.router.navigate(['family/order/selectedOrderHistory/', this.dayData.orderId]);
  }

  prepareStateForNewOrder(): void {
    const date = moment(this.dayData.orderDate).toDate();
    this.createOrderService.getAndSetDayDetail(
      this.dayData.menuType,
      this.dayData.menuName,
      this.dayData.menuId,
      date,
      this.child,
      this.dayData.cutOffDate
    );
  }

  createOrder() {
    this.appInsightsService.TrackEvent('ClickMenuBlock', {
      Date: moment(this.dayData.orderDate).format(),
      MenuType: this.dayData.menuType,
    });
    this.createOrderService.parentCreateOrder();
  }

  isOrderPassedOrClosed(): boolean {
    return (
      this.dayData.orderStatus.toLowerCase().includes(WeekDayStatusEnum.passed) ||
      this.dayData.orderStatus.toLowerCase().includes(WeekDayStatusEnum.closed)
    );
  }

  isMobileTextDisplay(): boolean {
    return (
      this.dayData.orderStatus === WeekDayStatusEnum.order ||
      this.dayData.orderStatus === WeekDayStatusEnum.passed ||
      this.dayData.orderStatus === WeekDayStatusEnum.closed
    );
  }
}
