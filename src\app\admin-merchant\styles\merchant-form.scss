@import '../../../styles/cashless-theme.scss';

.top-margin {
  margin: 10px 0 30px 0;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;

  h4 {
    font-size: 28px;
    font-weight: 700;
    color: $grey-12;
    padding-top: 8px;
    padding-bottom: 8px;
    margin: 0;
  }
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

li {
  padding: 6px;
  font-size: 14px;
  margin: 0;
  color: $grey-12;
}

.editBtn {
  cursor: pointer;
  border: 0;
  outline: 0;
  background-color: white;
}

.editIcon {
  height: 16px;
  width: 16px;
}
.details-divider {
  border: none;
  height: 0.1px;
  background-color: #b8b8b8;
  margin: 5px 0;
}

.editInput {
  width: 300px;
}

.editBtnContainer {
  display: flex;
  gap: 20px;
  margin: 10px 0 20px 0;
}

.saveBtn {
  border-radius: 10px;
  background-color: $orange-3;
  border: 0;
  font-size: 18px;
  color: white;
  cursor: pointer;
  width: 70px;
  height: 32px;
}

.saveBtn[disabled] {
  background-color: $grey-6;
  color: $grey-7;
  cursor: default;
}

.cancelBtn {
  color: $orange-3;
  border: 0;
  border: 0;
  font-size: 18px;
  background-color: white;
  height: 32px;
  cursor: pointer;
  width: 70px;
}

.merchant-section {
  position: relative;
  margin: 0;
  padding: 0;
}

.disableCoverWhite {
  position: absolute;
  background-color: rgb(255, 255, 255);
  opacity: 0.5;
  width: 100%;
  height: 100%;
  z-index: 100;
}
