import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-orders-placed',
  templateUrl: './orders-placed.component.html',
  styleUrls: ['./orders-placed.component.scss'],
})
export class OrdersPlacedComponent implements OnInit {
  @Output() goToOrders: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor() {}

  ngOnInit() {}

  GotToOrders() {
    this.goToOrders.emit(true);
  }
}
