import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'cutOffTimeFormat',
})
export class CutOffTimeFormatPipe implements PipeTransform {
  constructor() {}

  transform(value: number) {
    if (!value || value <= 0) {
      return 0;
    }
    let integerVal = Math.trunc(value);
    let decimalVal = value - integerVal;

    //convert decimal val to minute val
    let minuteVal = 60 * decimalVal;

    let formattedHourVal = integerVal > 0 ? `${integerVal}h ` : '';
    let formattedMinuteVal = decimalVal > 0 ? `${minuteVal}min` : '';

    return formattedHourVal + formattedMinuteVal;
  }
}
