# See http://help.github.com/ignore-files/ for more about ignoring files.
# config that should not be in git
src/environments/*ts*


# compiled output
/dist
/tmp
/out-tsc
# Only exists if <PERSON><PERSON> was run
/bazel-out

/environments

# dependencies
/node_modules
package-lock.json

# profiling files
chrome-profiler-events.json
speed-measure-plugin.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# misc
/.angular/cache
/.sass-cache
/connect.lock
*/coverage
*/tests
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db
/.vs

# Lock file
package-lock.json
