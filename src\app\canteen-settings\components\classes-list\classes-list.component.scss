@import '../../../../styles/cashless-theme.scss';

.classesTable {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 30px;
}

.actionTableau {
  margin-left: 5px;
  margin-right: 5px;
  cursor: pointer;
  background-color: green;
}
.title {
  font-size: 24px;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 15px;
  color: #828282;
}
.checkBox {
  color: $orange-3;
}

.rowElement {
  height: 61px;
}

.rowElement:hover {
  background-color: #fbfaf9;
}

.actions {
  text-align: right;

  & mat-icon {
    margin-right: 30px;
    cursor: pointer;
  }
}

.mat-mdc-cell {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 20px;
  color: #000000;
  border-bottom: 1px solid #dadada;
}

.mat-elevation-z8 {
  box-shadow: 0 0 #000000;
}

.inActiveCheckbox {
  transition: border-color 90ms cubic-bezier(0, 0, 0.2, 0.1);
  border-width: 2px;
  border-style: solid;
  border-color: $orange-3;
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.checkBox {
  margin-top: 4px;
}
