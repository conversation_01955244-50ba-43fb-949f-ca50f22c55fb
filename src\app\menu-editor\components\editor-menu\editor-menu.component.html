<h3>Edit</h3>
<form [formGroup]="form" (ngSubmit)="onSubmit()" class="cashlessForm">
  <mat-form-field appearance="outline">
    <input matInput placeholder="Name" formControlName="name" type="text" required />
    <mat-error *ngIf="name.invalid">{{ getErrorMessageName() }}</mat-error>
  </mat-form-field>
  <mat-form-field appearance="outline">
    <input matInput placeholder="Friendly name" formControlName="friendlyName" type="text" />
  </mat-form-field>
  <mat-checkbox formControlName="isActive">Is Active</mat-checkbox>
  <mat-error *ngIf="errorAPI">{{ WriteError() }}</mat-error>
  <p>
    <button mat-flat-button color="primary" type="submit" [disabled]="!form.valid">Save</button>
    <button mat-flat-button type="button" (click)="NavBack()">Cancel</button>
  </p>
</form>
