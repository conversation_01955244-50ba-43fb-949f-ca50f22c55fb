import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SchoolEvent } from 'src/app/sharedModels';
import { EventRowComponent } from '../event-row/event-row.component';
import { SharedModule } from 'src/app/shared/shared.module';

@Component({
  selector: 'event-list',
  standalone: true,
  imports: [CommonModule, EventRowComponent, SharedModule],
  templateUrl: './event-list.component.html',
  styleUrls: ['./event-list.component.scss'],
  // changeDetection: ChangeDetectionStrategy.OnPush, //TODO: bring this back
})
export class EventListComponent {
  @Input() eventList: SchoolEvent[];
  @Input() loading: boolean;
  @Output() onPress = new EventEmitter<number>();

  rowClick(eventId: number): void {
    this.onPress.emit(eventId);
  }
}
