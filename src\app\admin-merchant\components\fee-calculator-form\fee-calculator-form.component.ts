import { KeyValue } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { DialogConfirmV2Component } from 'src/app/shared/components';

//Models
import { BaseComponent, ConfirmModal, MerchantTypeEnum, OrderTypeEnum } from 'src/app/sharedModels';
import {
  AdminFeeResponse,
  FeeCalculator,
  FeeCalculatorInitialValue,
  FeeChangedEvent,
  SchoolFeeCalculator,
} from 'src/app/sharedModels/fee/FeeCalculator';

//Services
import { FeeCalculatorService } from 'src/app/sharedServices/fee/feeCalculator.service';

@Component({
  selector: 'fee-calculator-form',
  templateUrl: './fee-calculator-form.component.html',
  styleUrls: ['./fee-calculator-form.component.scss'],
})
export class FeeCalculatorFormComponent extends BaseComponent implements OnInit {
  @Input() merchantType: string;
  merchantTypeEnum = MerchantTypeEnum;
  schoolId: number;
  merchantId: number;
  currentSchoolFeeCalculators: SchoolFeeCalculator[];
  fullFeeList: FeeCalculator[] = [];
  loading: boolean = true;
  latestUpdateEvent: FeeChangedEvent;
  initialValues: FeeCalculatorInitialValue = new FeeCalculatorInitialValue();

  recessFeeList: KeyValue<string, string>[] = [];
  lunchFeeList: KeyValue<string, string>[] = [];
  eventFeeList: KeyValue<string, string>[] = [];
  uniformFeeList: KeyValue<string, string>[] = [];

  constructor(
    private feeCalculatorService: FeeCalculatorService,
    private route: ActivatedRoute,
    public dialog: MatDialog
  ) {
    super();
  }

  ngOnInit(): void {
    this.schoolId = this.route.snapshot.params['schoolId'];
    this.merchantId = this.route.snapshot.params['merchantId'];

    // get data from resolver
    this.route.data.subscribe(data => {
      this.processFeeData(data.feeData);
    });
  }

  processFeeData(res: AdminFeeResponse): void {
    this.fullFeeList = res.allFeeCalculators;
    this.currentSchoolFeeCalculators = res.schoolFeeCalculators;
    this.getFeeSelectListForEachMenuType(this.fullFeeList);
    this.getInitialValues();
    this.loading = false;
  }

  getInitialValues(): void {
    this.initialValues.Recess = this.getDefaultValue(OrderTypeEnum['Recess']);
    this.initialValues.Lunch = this.getDefaultValue(OrderTypeEnum['Lunch']);
    this.initialValues.Event = this.getDefaultValue(OrderTypeEnum['Event']);
    this.initialValues.Uniform = this.getDefaultValue(OrderTypeEnum['Uniform']);
  }

  getFeeSelectListForEachMenuType(feeOptionArray: FeeCalculator[]): void {
    if (this.merchantType === MerchantTypeEnum.Canteen) {
      this.recessFeeList = this.filterCalculatorByOrderType(feeOptionArray, OrderTypeEnum['Recess']);
      this.lunchFeeList = this.filterCalculatorByOrderType(feeOptionArray, OrderTypeEnum['Lunch']);
    }
    if (this.merchantType === MerchantTypeEnum.Canteen || this.merchantType === MerchantTypeEnum.Event) {
      this.eventFeeList = this.filterCalculatorByOrderType(feeOptionArray, OrderTypeEnum['Event']);
    }
    if (this.merchantType === MerchantTypeEnum.Uniform) {
      this.uniformFeeList = this.filterCalculatorByOrderType(feeOptionArray, OrderTypeEnum['Uniform']);
    }
  }

  filterCalculatorByOrderType(calculatorList: FeeCalculator[], menuType: number): KeyValue<string, string>[] {
    let filteredList = calculatorList.filter(x => x.calculatorOrderType === menuType);
    let selectListValues = [];
    filteredList.forEach(x => {
      selectListValues.push({ key: x.feeCalculatorId, value: x.feeCalculatorName });
    });
    return selectListValues;
  }

  getDefaultValue(menuType: OrderTypeEnum): number {
    let customFee = null;
    if (this.currentSchoolFeeCalculators?.length) {
      customFee = this.getOrderTypeFee(this.currentSchoolFeeCalculators, menuType);
    }
    if (!customFee) {
      let defaultList = this.fullFeeList.filter(fee => fee.isDefault === true);
      customFee = this.getOrderTypeFee(defaultList, menuType);
    }
    return customFee;
  }

  getOrderTypeFee(feeArray: any[], menuType: OrderTypeEnum): number {
    let fee = feeArray.find(x => x.calculatorOrderType === menuType);
    return fee?.feeCalculatorId || null;
  }

  getFeeById(id: number): FeeCalculator {
    return this.fullFeeList.find(x => x.feeCalculatorId === id);
  }

  saveFeeCalculator(event: FeeChangedEvent): void {
    this.latestUpdateEvent = event;
    let newFeeCalculator = this.getFeeById(event.newFeeId);
    let initialFeeCalculator = this.getFeeById(event.initialFeeId);

    if (!newFeeCalculator || !initialFeeCalculator) {
      return;
    }
    this.loading = true;
    //change from custom option to default
    if (newFeeCalculator.isDefault) {
      this.removeFeeCalculator(initialFeeCalculator.feeCalculatorId, newFeeCalculator.feeCalculatorId);
      return;
    }
    //change from default to custom option
    if (initialFeeCalculator.isDefault) {
      this.addFeeCalculator(newFeeCalculator.feeCalculatorId);
      return;
    }
    //change from custom option to custom option
    this.updateFeeCalculator(newFeeCalculator.feeCalculatorId);
  }

  removeFeeCalculator(feeId: number, newFeeId: number): void {
    this.feeCalculatorService.RemoveSchoolFromFeeCalculator(feeId, this.schoolId, this.merchantId).subscribe(
      res => {
        this.processApiSuccess(newFeeId);
      },
      error => {
        this.errorPopUp();
        this.handleErrorFromService(error);
      }
    );
  }

  addFeeCalculator(feeId: number): void {
    this.feeCalculatorService.AddSchoolToFeeCalculator(feeId, this.schoolId, this.merchantId).subscribe({
      next: res => {
        this.processApiSuccess(feeId);
      },
      error: error => {
        this.errorPopUp();
        this.handleErrorFromService(error);
      },
    });
  }

  updateFeeCalculator(feeId: number): void {
    this.feeCalculatorService.UpdateSchoolFromFeeCalculator(feeId, this.schoolId, this.merchantId).subscribe({
      next: res => {
        this.processApiSuccess(feeId);
      },
      error: error => {
        this.errorPopUp();
        this.handleErrorFromService(error);
      },
    });
  }

  processApiSuccess(newFeeId: number) {
    this.initialValues[this.latestUpdateEvent.menuType] = newFeeId;
    this.successPopUp();
    this.loading = false;
  }

  errorPopUp(): void {
    let data = new ConfirmModal();
    data.Title = 'Something went wrong';
    data.Text = 'The fee calculator could not be updated.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';

    const dialogRef = this.dialog.open(DialogConfirmV2Component, {
      width: '500px',
      disableClose: false,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.saveFeeCalculator(this.latestUpdateEvent);
      }
    });
  }

  successPopUp(): void {
    let data = new ConfirmModal();
    data.Title = 'Success!';
    data.Text = 'The fee calculator has been successfully updated.';
    data.ConfirmButton = 'Ok';

    const dialogRef = this.dialog.open(DialogConfirmV2Component, {
      width: '500px',
      disableClose: false,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
      }
    });
  }
}
