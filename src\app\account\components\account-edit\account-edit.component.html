<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <nav-back-button (navBack)="backClicked()" text="Profile"></nav-back-button>
    </div>
  </div>
  <div class="row justify-content-center">
    <div class="col-12 col-md-8 col-lg-6 align-self-center">
      <mat-card appearance="outlined" class="cardForm">
        <mat-card-content>
          <div class="row justify-content-center">
            <div class="col-12 col-md-8">
              <form [formGroup]="accountForm" class="cashlessForm">
                <mat-form-field appearance="outline">
                  <mat-label>First name</mat-label>
                  <input matInput placeholder="Name" formControlName="name" type="text" required />
                  <mat-error *ngIf="name.invalid">{{ getErrorMessageName() }}</mat-error>
                </mat-form-field>
                <mat-form-field appearance="outline">
                  <mat-label>Last name</mat-label>
                  <input matInput placeholder="Lastname" formControlName="lastname" type="text" required />
                  <mat-error *ngIf="lastname.invalid">{{ getErrorMessageLastname() }}</mat-error>
                </mat-form-field>
                <mat-form-field appearance="outline">
                  <mat-label>Email</mat-label>
                  <input matInput placeholder="Email" formControlName="email" type="text" />
                </mat-form-field>
                <mat-form-field appearance="outline">
                  <mat-label>Mobile number</mat-label>
                  <input
                    matInput
                    placeholder="Mobile number"
                    formControlName="mobile"
                    type="tel"
                    (keyup)="formatMobileInput()"
                    required
                  />
                  <mat-error *ngIf="mobile.invalid">{{ getErrorMessageMobile() }}</mat-error>
                </mat-form-field>
                <mat-error *ngIf="errorAPI">{{ WriteError() }}</mat-error>
              </form>
            </div>
          </div>

          <div class="row justify-content-center rowButton">
            <div class="col-12 col-md-8">
              <button class="PrimaryButton" type="button" (click)="ClickSubmit()">Save</button>
            </div>

            <div class="col-12 col-md-8">
              <button mat-flat-button class="SecondaryButton" type="button" [routerLink]="['../']">
                Cancel
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
