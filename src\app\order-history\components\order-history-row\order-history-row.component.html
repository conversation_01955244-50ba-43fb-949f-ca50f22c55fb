<div class="row-container" (click)="viewSelectedOrder()">
  <div class="left-info">
    <p class="title">{{ order.StudentName }}</p>
    <p class="subtitle">
      {{ order.MenuType | customMenuName : order.MenuName }} - {{ order?.Items?.length }} items
    </p>
  </div>
  <div class="right-info">
    <p class="title price align-right">{{ order.Price + order.OrderFee | currency }}</p>
    <p class="subtitle align-right">
      {{ orderToShow | orderHistoryStatus }}
    </p>
  </div>
</div>
