<nav-back-button
  smallText="true"
  (navBack)="goBackClick()"
  text="Go Back"
  class="backButton"
  smallFont="true"
  noPadding="true"
></nav-back-button>
<div class="container-fluid">
  <div class="header">
    <h3 class="merchant-heading">Add merchant user</h3>
    <p>Search existing users to add them as a merchant user</p>
  </div>

  <search-panel
    (triggerSearch)="fetchData($event)"
    (triggerClear)="clearFilter()"
    [searchInput]="listfilters.Filter"
    placeholder="Search parent account name..."
  ></search-panel>

  <div>
    <div *ngIf="noResultsMessage" class="noResults">
      <h3>{{ noResultsMessage }}</h3>
    </div>

    <div *ngIf="showResultsTable">
      <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 tableau userTable">
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef>User ID</th>
          <td mat-cell *matCellDef="let element">{{ element.UserId }}</td>
        </ng-container>

        <ng-container matColumnDef="firstName">
          <th mat-header-cell *matHeaderCellDef>First name</th>
          <td mat-cell *matCellDef="let element">{{ element.FirstName }}</td>
        </ng-container>

        <ng-container matColumnDef="lastName">
          <th mat-header-cell *matHeaderCellDef>Last name</th>
          <td mat-cell *matCellDef="let element">{{ element.Lastname }}</td>
        </ng-container>

        <ng-container matColumnDef="phone">
          <th mat-header-cell *matHeaderCellDef>Phone</th>
          <td mat-cell *matCellDef="let element">{{ element.Mobile }}</td>
        </ng-container>

        <ng-container matColumnDef="email">
          <th mat-header-cell *matHeaderCellDef>Email</th>
          <td mat-cell *matCellDef="let element">{{ element.Email }}</td>
        </ng-container>

        <ng-container matColumnDef="select" stickyEnd>
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let element">
            <a style="float: right">
              Add User
              <mat-icon class="chevron">chevron_right</mat-icon>
            </a>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns" routerLink="./{{ row.UserId }}"></tr>
      </table>
    </div>
  </div>

  <!-- spacer under table -->
  <div style="height: 70px"></div>
</div>
