import { Injectable, inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';

import { ArrayFilter } from 'src/app/sharedModels';

// state
import { Store } from '@ngrx/store';
import { UserManagementState } from 'src/app/states/user-management/user-management-state.interface';
import { SearchOrders } from 'src/app/states/user-management/user-management.actions';
import { Observable } from 'rxjs';

export const UserOrdersResolver: ResolveFn<any> = (route: ActivatedRouteSnapshot): Observable<any> => {
  const store = inject(Store<{ userManagement: UserManagementState }>);
  let id = route.params['id'];

  if (id == null || id == undefined) {
    id = route.parent.params['id'];
  }

  let listFilters = new ArrayFilter();
  listFilters.Filter = '';
  listFilters.NumberRows = 25;
  listFilters.PageIndex = 0;

  store.dispatch(SearchOrders({ listFilters: listFilters, userId: id }));

  return null;
};
