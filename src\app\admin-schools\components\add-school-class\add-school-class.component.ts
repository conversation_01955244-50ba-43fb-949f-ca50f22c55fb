import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { DialogConfirmComponent } from 'src/app/shared/components/dialog-confirm/dialog-confirm.component';

import { SchoolClass, BaseComponent, ConfirmModal } from '../../../sharedModels';
import { SchoolClassesService, AdminService, SpinnerService } from '../../../sharedServices';

@Component({
  selector: 'admin-add-school-class',
  templateUrl: './add-school-class.component.html',
  styleUrls: ['./add-school-class.component.scss'],
})
export class AddSchoolClassComponent extends BaseComponent implements OnInit, OnDestroy {
  @Input() onCancel?: () => void;
  @Output() loadSchoolClasses = new EventEmitter();
  private editClassSubscription: Subscription;
  isEdit: boolean = false;
  private schoolId: number;
  errorAPI: any;
  form: FormGroup;
  textSubmit: string;
  showButtonCancel: boolean;
  classValues: string[] = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];

  constructor(
    private schoolClassService: SchoolClassesService,
    private adminService: AdminService,
    private spinnerService: SpinnerService,
    public dialog: MatDialog
  ) {
    super();
  }

  ngOnInit() {
    this.schoolId = this.adminService.GetSchoolId();

    this.createForm(new SchoolClass());

    this.editClassSubscription = this.adminService.classEditRequestEvent$.subscribe(schoolClass => {
      this.createForm(schoolClass, true);
    });
  }

  ngOnDestroy() {
    if (this.editClassSubscription) {
      this.editClassSubscription.unsubscribe();
    }
  }

  ////////////////////////////////////////
  // Form
  ////////////////////////////////////////
  get name() {
    return this.form.get('name');
  }

  getErrorMessageName() {
    return this.form.get('name').hasError('required') ? 'You must enter a value' : '';
  }

  createForm(schoolClass: SchoolClass, isEdit: boolean = false) {
    this.isEdit = isEdit;
    this.textSubmit = this._GetTextSubmit(schoolClass.Name);

    let yearGroup = [];
    if (schoolClass.YearGroup) {
      //remove any commas from the end of string
      let yearGroupString =
        schoolClass?.YearGroup.slice(-1) === ',' ? schoolClass.YearGroup.slice(0, -1) : schoolClass.YearGroup;
      // transform string into array
      yearGroup = yearGroupString.split(',');
    }

    this.form = new FormGroup({
      id: new FormControl(schoolClass.ClassId),
      schoolId: new FormControl(this.schoolId),
      name: new FormControl(schoolClass.Name, [Validators.required]),
      teacher: new FormControl(schoolClass.Teacher),
      isActive: new FormControl(schoolClass.IsActive != null ? schoolClass.IsActive : true),
      yearGroup: new FormControl(yearGroup),
      sortOrder: new FormControl(schoolClass.SortOrder),
    });
  }

  ////////////////////////////////////////
  // Add school
  ////////////////////////////////////////
  onSubmit() {
    this.spinnerService.start();
    let data = this.convertObject();

    if (data.ClassId) {
      this.updateClass(data);
    } else {
      this.addClass(data);
    }
  }

  addClass(data: SchoolClass) {
    this.schoolClassService.CreateClassApi(data).subscribe({
      next: (response: SchoolClass) => {
        this.initClassForm();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  updateClass(data: SchoolClass) {
    this.schoolClassService.UpdateClassApi(data).subscribe({
      next: (response: SchoolClass) => {
        this.initClassForm();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  initClassForm() {
    this.loadSchoolClasses.emit();
    this.createForm(new SchoolClass());
  }

  private convertObject(): SchoolClass {
    let schoolClass = new SchoolClass();

    schoolClass.ClassId = this.form.get('id').value;
    schoolClass.SchoolId = this.form.get('schoolId').value;
    schoolClass.Name = this.form.get('name').value;
    schoolClass.Teacher = this.form.get('teacher').value;
    schoolClass.IsActive = Boolean(this.form.get('isActive').value);
    schoolClass.SortOrder = this.form.get('sortOrder').value;

    let yearGroupResult = '';

    if (this.yearGroup.value?.length > 1) {
      // convert year group array into string
      this.yearGroup.value.forEach((el: string) => {
        yearGroupResult += el + ',';
      });
    } else if (this.yearGroup.value?.length === 1) {
      yearGroupResult = this.yearGroup.value.toString();
    }

    schoolClass.YearGroup = yearGroupResult;

    return schoolClass;
  }

  CancelForm() {
    this.createForm(new SchoolClass());
  }

  get yearGroup() {
    return this.form.get('yearGroup');
  }

  ////////////////////////////////////////
  // Get Text
  ////////////////////////////////////////
  private _GetTextSubmit(className: string): string {
    if (className != null && className != '') {
      this.showButtonCancel = true;
      return 'Edit';
    } else {
      this.showButtonCancel = false;
      return 'Add';
    }
  }

  ArchiveClicked() {
    let data = new ConfirmModal();
    data.Title = 'Archive Class';
    data.Text =
      "Archiving this class will inactivate the class permanently and can not be undone. Parents will be instructed to change their child's class. Proceed?";
    data.CancelButton = 'No';
    data.ConfirmButton = 'Yes';

    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.ArchiveClickConfirmed();
      }
    });
  }

  ArchiveClickConfirmed() {
    this.spinnerService.start();
    let archiveClass = this.convertObject();
    archiveClass.IsArchived = true;

    this.schoolClassService.ArchiveClassApi(archiveClass).subscribe({
      next: (response: SchoolClass) => {
        this.loadSchoolClasses.emit();
        this.createForm(new SchoolClass());
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }
}
