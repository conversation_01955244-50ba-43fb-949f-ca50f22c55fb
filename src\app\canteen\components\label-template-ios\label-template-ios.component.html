<ng-container>
  <!-- First line: School Name -->
  <div *ngIf="displaySchoolName">
    <span class="title">{{ GetPrintValue('Title', label) }}</span>
  </div>
  <!-- Second line: Name - SchoolClass -->
  <div>
    <span class="subTitle">{{ GetPrintValue('SubTitle', label) }}</span>
  </div>

  <!-- Allergies -->
  <div *ngIf="ShowAllergies(label)">
    <span class="allergies">Allergies: {{ GetPrintValue('Allergies', label) }}</span>
  </div>

  <!-- Items -->
  <ul *ngIf="label.Items">
    <li class="items" *ngFor="let item of label.Items">
      {{ item }}
    </li>
  </ul>

  <!-- Bottom line: Label number, menu type, date -->
  <span class="footer labelNumber">{{ label.LabelNumber }}</span>
  <span class="footer footerLeft">{{ GetPrintValue('FooterLeft', label) }}</span>
  <span class="footer footerRight">{{ GetPrintValue('FooterRight', label) }}</span>
</ng-container>
