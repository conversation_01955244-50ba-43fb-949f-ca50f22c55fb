<div class="container-fluid divWrapper">
  <div *ngIf="!deactivatedUser" class="row headerContainer" [@bounce]="bounce">
    <div class="col-4">
      <p class="headerTextLeft">My Children</p>
    </div>
    <div class="col-4">
      <p class="headerTextCentre">My Children</p>
    </div>
    <div class="col-4 buttonContainer">
      <button type="button" class="PrimaryButton" routerLink="./add">Add Child</button>
      <button type="button" class="buttonAddMobile" routerLink="./add">Add Child</button>
    </div>
  </div>
  <div *ngIf="!deactivatedUser" class="row cardDefaultParent" [@bounce]="bounce">
    <ng-container *ngIf="ShowListStudents()">
      <div
        *ngFor="let student of listStudents"
        class="row col-12 cardChild"
        (click)="EditChildClick(student)"
      >
        <img class="child-image" src="assets/icons/child-blank.svg" />
        <!-- <div class="row childName">{{student.FirstName}}</div> -->
        <div class="row">
          <div class="col-12">
            <h4 class="childName">{{ student.FirstName }}</h4>
            <span>{{ student.ClassName }}, {{ student.SchoolName }}</span>
          </div>
        </div>
      </div>
    </ng-container>

    <div *ngIf="addedFirstChild" class="row">
      <div class="col-12">
        <p class="firstChild">
          You're now ready to make an order. <a [routerLink]="['/family/home']">Go to Orders</a>.
        </p>
      </div>
    </div>

    <div *ngIf="!ShowListStudents() && !deactivatedUser" class="row">
      <div class="col-12">
        <h3 class="noChild">Add your child to begin using Spriggy Schools!</h3>
      </div>
    </div>
  </div>

  <div *ngIf="deactivatedUser" class="row">
    <div class="col-12">
      <app-warning
        title="Your account is deactivated"
        description="Please get in touch with us if you would like to reopen your account."
      ></app-warning>
    </div>
  </div>
</div>
