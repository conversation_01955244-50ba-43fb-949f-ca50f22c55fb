import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import moment from 'moment';

// Ngrx
import { Subscription } from 'rxjs';

// models
import {
  MenuTypeEnum,
  UserCashless,
  BaseComponent,
  OrderBlockInfo,
  WeekDayStatusEnum,
  OptionSchoolResponse,
} from '../../../sharedModels';

// services
import { ParentHomeService, SchoolEventApiService, UserService } from 'src/app/sharedServices';
import { Store, select } from '@ngrx/store';
import { selectedChild } from 'src/app/states/children/children.selectors';
import { FamilyState } from 'src/app/states';

@Component({
  selector: 'family-uniform',
  templateUrl: './family-uniform.component.html',
  styleUrls: ['./family-uniform.component.scss'],
})
export class FamilyUniformComponent extends BaseComponent implements OnInit, OnDestroy {
  @Input() selectedChild: UserCashless;
  @Input() hasUniformMenu: boolean = false;
  private selectedSubscription: Subscription;
  private selectedChildSubscription: Subscription;
  private loadingSubscription: Subscription;
  isLoading: boolean = false;
  dayData: OrderBlockInfo;
  selectChild: UserCashless;

  //used by schools that have no uniform menu but want to display a custom message to users
  displayMessage: string;

  constructor(
    private store: Store<{ family: FamilyState }>,
    private userService: UserService,
    private schoolEventService: SchoolEventApiService,
    private parentHomeService: ParentHomeService
  ) {
    super();
  }

  ngOnInit() {
    this.getOrderBlockInfo();
    this.loadingSubscription = this.parentHomeService.loadingEvent$.subscribe((res: boolean) => {
      this.isLoading = res;
    });

    this.selectedChildSubscription = this.store
      .pipe(select(selectedChild))
      .subscribe((child: UserCashless) => {
        this.selectChild = child;
      });

    if (!this.hasUniformMenu) {
      return;
    }
    this.isLoading = true;
    this.schoolEventService.GetUniformShopBySchoolAPI(this.selectedChild.SchoolId).subscribe({
      next: (res: OptionSchoolResponse) => {
        if (!res?.MenuId) {
          this.displayMessage = res?.OptionDescription || null;
        }
      },
      error: error => {
        this.handleErrorFromService(error);
      },
      complete: () => {
        this.isLoading = false;
      },
    });
  }

  getOrderBlockInfo(): void {
    this.dayData = {
      menuType: MenuTypeEnum.Uniform,
      menuName: MenuTypeEnum.Uniform,
      orderDate: moment().toDate(),
      cutOffDate: moment().add(1, 'days').toDate(),
      orderStatus: WeekDayStatusEnum.order,
      orderId: null,
      menuId: null,
    };
  }

  triggerIntercom() {
    this.userService.openIntercom();
  }

  ngOnDestroy(): void {
    if (this.selectedSubscription) {
      this.selectedSubscription.unsubscribe();
    }
    if (this.selectedChildSubscription) {
      this.selectedChildSubscription.unsubscribe();
    }
    if (this.loadingSubscription) {
      this.loadingSubscription.unsubscribe();
    }
  }
}
