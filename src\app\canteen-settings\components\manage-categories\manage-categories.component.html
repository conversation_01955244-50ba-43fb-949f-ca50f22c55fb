<div class="col-12">
  <nav-back-button (navBack)="GoBackClick()" text="Go Back" class="backButton"></nav-back-button>

  <div class="schoolSelection printerSection">
    <div class="row">
      <div class="col-8">
        <h3 class="optionTitle">Menu Categories</h3>
      </div>
      <div class="col-4">
        <div class="headerAddCategory">
          <basic-button text="Add New Category" (onPress)="NewCategoryClick()"></basic-button>
        </div>
      </div>
    </div>

    <table class="tableCat">
      <thead>
        <tr>
          <th>Category</th>
          <th>Image</th>
          <th></th>
        </tr>
      </thead>
      <tr *ngFor="let cat of listCategories">
        <td class="catLine">{{ cat.CategoryName }}</td>
        <td class="catLine">
          <category-icon [iconName]="cat.CategoryUrl"></category-icon>
        </td>
        <td class="catLine" style="text-align: right">
          <mat-icon matTooltip="Delete" class="iconTable" (click)="ArchiveClicked(cat)">delete</mat-icon>
          <mat-icon matTooltip="Edit" class="iconTable" (click)="OpenDialog(cat)">edit</mat-icon>
        </td>
      </tr>
    </table>
  </div>
</div>
