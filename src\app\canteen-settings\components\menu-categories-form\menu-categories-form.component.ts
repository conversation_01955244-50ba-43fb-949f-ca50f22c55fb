import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';

//ngrx
import { select, Store } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { CategoryEditor, School } from 'src/app/sharedModels';
import { CanteenState } from 'src/app/states';
import { canteenStateSelector } from 'src/app/states/canteen/canteen.selectors';

@Component({
  selector: 'menu-categories-form',
  templateUrl: './menu-categories-form.component.html',
  styleUrls: ['./menu-categories-form.component.scss'],
})
export class MenuCategoriesFormComponent implements OnInit {
  @Output() updateSchool: EventEmitter<School> = new EventEmitter();
  canteenSubscription: Subscription;
  listCategories: CategoryEditor[] = [];

  constructor(private store: Store<{ canteen: CanteenState }>, private router: Router) {}

  ngOnInit(): void {
    this.canteenSubscription = this.store
      .pipe(select(canteenStateSelector))
      .subscribe((state: CanteenState) => {
        if (state.selected) {
          this.listCategories = state.menuCategories;
        }
      });
  }

  ngOnDestroy() {
    this.canteenSubscription.unsubscribe();
  }

  editCategories() {
    this.router.navigate(['canteen/settings/menu/categories']);
  }
}
