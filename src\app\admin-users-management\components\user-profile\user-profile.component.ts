import { ChangeDetectorRef, Component, OnInit } from '@angular/core';

// state
import { select, Store } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { Roles } from 'src/app/sharedModels';
import { SpinnerService } from 'src/app/sharedServices';
import { UserManagementState } from 'src/app/states/user-management/user-management-state.interface';
import {
  SelectedUserSelector,
  userProfileLoadingSelector,
} from 'src/app/states/user-management/user-management.selectors';

@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.scss'],
})
export class UserProfileComponent implements OnInit {
  userSubscription: Subscription;
  loadSubscription: Subscription;
  isChild: boolean;
  constructor(
    private cd: ChangeDetectorRef,
    private store: Store<{ userManagement: UserManagementState }>,
    private spinnerService: SpinnerService
  ) {}

  ngOnInit(): void {
    this.userSubscription = this.store.pipe(select(SelectedUserSelector)).subscribe(userRes => {
      if (userRes) {
        this.isChild = userRes.Role == Roles.Child;
        this.cd.markForCheck();
      }
    });

    this.loadSubscription = this.store.pipe(select(userProfileLoadingSelector)).subscribe(res => {
      if (res >= 3) {
        this.spinnerService.stop();
        return;
      }
      this.spinnerService.start();
    });
  }

  ngOnDestroy(): void {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
    if (this.loadSubscription) {
      this.loadSubscription.unsubscribe();
    }
  }
}
