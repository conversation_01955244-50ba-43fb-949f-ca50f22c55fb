// Models
import { SortedOrderHistory } from '../../../sharedModels';
import { ActivatedRoute } from '@angular/router';
import { SortGroupOrderHistory } from './group-order-history-helper';

export class CommonOrderHistoryClass {
  listOrders: SortedOrderHistory[] = [];
  loading: boolean = false;

  constructor(protected route: ActivatedRoute) {}

  onInitFunction() {
    const orders = this.route.snapshot.data['orders'];
    this.listOrders = SortGroupOrderHistory(orders);
  }
}
