import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { OrderWithCutOffTimesResolver } from '../sharedServices/order/orderWithCutOffTimes.resolver';

// components
import { ManageOrderComponent, ShoppingCartComponent, SelectedOrderHistoryComponent } from './components';

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'place',
      },
      {
        path: 'place',
        component: ManageOrderComponent,
      },
      {
        path: 'shoppingCart',
        component: ShoppingCartComponent,
        data: { isMobile: true },
      },
      {
        path: 'history',
        loadChildren: () => import('../order-history/order-history.module').then(m => m.OrderHistoryModule),
      },
      {
        path: 'selectedOrderHistory/:orderId',
        component: SelectedOrderHistoryComponent,
        resolve: {
          orderWithCutOffTimes: OrderWithCutOffTimesResolver,
        },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ManageOrderRoutingModule {}
