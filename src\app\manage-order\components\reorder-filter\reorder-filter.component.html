<ng-container *ngIf="loading; else showForm">
  <div class="col-12 d-flex align-items-center justify-content-center">
    <app-spinner [manual]="true"></app-spinner>
  </div>
</ng-container>

<ng-template #showForm>
  <div class="result">
    <div class="row justify-content-lg-center">
      <div class="col-12 col-lg-8 pt-3">
        <h2 class="center-text">Which days do you want to repeat this order?</h2>
      </div>
    </div>

    <div *ngIf="priceUpdate" class="infoContainer">
      <div class="groupMessage">
        <img src="assets/icons/black-error.svg" alt="error" />
        <p class="m-0 pb-2">One or more items have had a price update since your last order</p>
      </div>
      <ul class="m-0">
        <li *ngFor="let item of updatedOrderItems">
          {{ item.Quantity }} x {{ item.Name }}
          <span *ngIf="item.SelectedOptions?.length > 0">
            ({{ item.SelectedOptions | orderOptionsString }})
          </span>
          ({{ [item] | calculateOrderItemsPrice }})
        </li>
      </ul>
    </div>

    <div class="formContainer mt-3" *ngIf="form">
      <order-again-checkbox
        [form]="form"
        [dateFormArray]="dateFormArray"
        [menuName]="data.menuName"
        [dateList]="dateList"
      >
      </order-again-checkbox>
    </div>

    <div class="col-12 pt-4">
      <primary-button
        text="Go to checkout"
        (onPress)="closeSheet()"
        [disabled]="anyDatesSelected()"
      ></primary-button>
    </div>
  </div>
</ng-template>
