import { KeyValue } from '@angular/common';
import { Component, Input, OnChanges } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import moment from 'moment';
import { BaseFormComponent } from 'src/app/schools-form/components';
import {
  DownloadCSV,
  GenerateExportRequest,
  GeneratedInvoice,
  InvoiceDataForFiltering,
  InvoiceExportRequest,
  InvoiceToExport,
  InvoiceType,
  InvoiceTypeEnum,
  SelectedWeekAndYear,
  WeekDateType,
  WeekDates,
} from 'src/app/sharedModels';
import { MerchantService, SpinnerService } from 'src/app/sharedServices';
import { UNIVERSAL_DATE_FORMAT } from 'src/app/utility';
import * as _ from 'lodash';

@Component({
  selector: 'weekly-report',
  templateUrl: './weekly-report.component.html',
  styleUrls: ['./weekly-report.component.scss'],
})
export class WeeklyReportComponent extends BaseFormComponent implements OnChanges {
  @Input() generatedInvoiceList: GeneratedInvoice[];
  INVOICE_WEEK_TOTAL = 100;
  formGroup: FormGroup;
  selectWeekValues: KeyValue<string, string>[] = [];
  generatedInvoiceData: InvoiceDataForFiltering[];

  constructor(private merchantService: MerchantService, private spinnerService: SpinnerService) {
    super();
  }

  ngOnChanges(): void {
    if (this.generatedInvoiceList) {
      this.getInputListData(this.generatedInvoiceList);
    }
  }

  getInputListData(generatedInvoiceList: GeneratedInvoice[]): void {
    this.selectWeekValues = this.getSelectWeekValues();
    this.generatedInvoiceData = this.mapGeneratedInvoiceToSelectedWeekDate(generatedInvoiceList);
    this.createFrom();
  }

  mapGeneratedInvoiceToSelectedWeekDate(generatedInvoiceList: GeneratedInvoice[]): InvoiceDataForFiltering[] {
    return generatedInvoiceList.map((invoiceData: GeneratedInvoice) => ({
      invoiceId: invoiceData.invoiceId,
      key: this.getWeekYearNumber(invoiceData.toDate),
    }));
  }

  private getWeekYearNumber(date: string): string {
    return moment(date).format('w-YYYY');
  }

  createFrom(): void {
    const weekToDisplay = this.getWeekToDisplay();

    this.formGroup = new FormGroup({
      week: new FormControl(weekToDisplay),
    });
  }

  getWeekToDisplay(): string {
    const currentWeek = this.selectWeekValues[0].key;
    const selectedWeek = this.selectedWeek?.value;
    return selectedWeek ? selectedWeek : currentWeek;
  }

  getSelectWeekValues(): KeyValue<string, string>[] {
    let endCurrentWeek = moment().endOf('week').add(1, 'week').format(UNIVERSAL_DATE_FORMAT);
    const weekValues: KeyValue<string, string>[] = [];

    for (let index = 0; index < this.INVOICE_WEEK_TOTAL; index++) {
      endCurrentWeek = moment(endCurrentWeek).subtract(1, 'week').format(UNIVERSAL_DATE_FORMAT);
      weekValues.push({
        key: this.getWeekYearNumber(endCurrentWeek),
        value: this.getWeekText(endCurrentWeek),
      });
    }

    return weekValues;
  }

  getWeeklyReport(invoiceToExport: InvoiceToExport): void {
    const { invoiceType } = invoiceToExport;
    if (invoiceType === InvoiceType.Generate) {
      this.generateInvoice();
      return;
    }
    this._downloadWeeklyReport(invoiceToExport);
  }

  getExportRequest(invoiceToExport: InvoiceToExport): GenerateExportRequest {
    const { invoiceId, invoiceType } = invoiceToExport;
    return {
      ExportType: InvoiceTypeEnum[invoiceType],
      InvoiceId: invoiceId,
    };
  }

  getGenerateInvoiceRequest(): InvoiceExportRequest {
    const selectedDateValues: SelectedWeekAndYear = this.getSelectedWeekAndYear();

    return {
      exportType: 0,
      startDate: this.getWeekDate(selectedDateValues, WeekDateType.start),
      endDate: this.getWeekDate(selectedDateValues, WeekDateType.end),
    };
  }

  getWeekDate(selectedDateValues: SelectedWeekAndYear, weekType: WeekDateType): string {
    const year = selectedDateValues.selectedYear.toString(); // year value needs to be string
    const week = selectedDateValues.selectedWeek; // week value needs to be int
    const momentValue = moment(year).weeks(week);
    const momentDate =
      weekType === WeekDateType.start ? momentValue.startOf('week') : momentValue.endOf('week');
    return moment(momentDate).format(UNIVERSAL_DATE_FORMAT);
  }

  private getWeekText(date: string): string {
    const { startDate, endDate } = this.getWeekStartAndEndDate(date);
    return `${startDate} to ${endDate}`;
  }

  getWeekStartAndEndDate(date: string): WeekDates {
    const endDate = _.cloneDeep(date);
    const startDate = _.cloneDeep(date);
    const textStart = moment(startDate).subtract(1, 'week').add(1, 'day').format(UNIVERSAL_DATE_FORMAT);
    const textEnd = moment(endDate).format(UNIVERSAL_DATE_FORMAT);

    return { startDate: textStart, endDate: textEnd };
  }

  getSelectedWeekAndYear(): SelectedWeekAndYear {
    const weekYearKeyValueData: string = this.selectedWeek.value;
    const weekYearArray: string[] = weekYearKeyValueData.split('-');
    const selectedWeek: number = +weekYearArray[0];
    const selectedYear: number = +weekYearArray[1];
    return { selectedWeek, selectedYear };
  }

  private generateInvoice(): void {
    const request: InvoiceExportRequest = this.getGenerateInvoiceRequest();
    this.spinnerService.animatedStart();
    this.merchantService.GenerateInvoice(request).subscribe({
      next: res => {
        this.refreshGeneratedInvoiceList();
      },
      error: error => {
        this.spinnerService.animatedStop();
        this.ErrorModal('Something went wrong', error);
      },
    });
  }

  private refreshGeneratedInvoiceList(): void {
    this.merchantService.GetGeneratedInvoiceList().subscribe({
      next: (res: GeneratedInvoice[]) => {
        this.getInputListData(res);
        this.spinnerService.animatedStop();
      },
      error: error => {
        this.spinnerService.animatedStop();
        this.ErrorModal('Something went wrong', error);
      },
    });
  }

  private _downloadWeeklyReport(invoiceToExport: InvoiceToExport): void {
    let filename = invoiceToExport.invoiceType + '.csv';

    const request: GenerateExportRequest = this.getExportRequest(invoiceToExport);
    this.spinnerService.animatedStart();
    this.merchantService.ExportInvoice(request).subscribe({
      next: (res: any) => {
        DownloadCSV(filename, res);
        this.spinnerService.animatedStop();
      },
      error: error => {
        this.spinnerService.animatedStop();
        this.ErrorModal('Something went wrong', error);
      },
    });
  }

  get selectedWeek() {
    return this.formGroup?.get('week');
  }
}
