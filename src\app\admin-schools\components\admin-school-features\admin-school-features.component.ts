import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { BaseFormComponent } from 'src/app/schools-form/components';
import { AdminSchoolFeatures, SchoolFeature, SchoolFeatureTypeEnum } from 'src/app/sharedModels';
import { SchoolFeatureService } from 'src/app/sharedServices';

@Component({
  selector: 'admin-school-features',
  templateUrl: './admin-school-features.component.html',
  styleUrls: ['./admin-school-features.component.scss'],
})
export class AdminSchoolFeaturesComponent extends BaseFormComponent implements OnInit {
  uniformShopAvailable: boolean = false;
  private schoolId: string;

  constructor(private route: ActivatedRoute, private schoolFeatureService: SchoolFeatureService) {
    super();
  }

  ngOnInit(): void {
    // create form
    this._CreateForm();

    // get school Id from the route
    const routeSnapshot = this.route.snapshot;

    this.schoolId = routeSnapshot.params['id'];

    if (this.schoolId == null || this.schoolId == undefined) {
      this.schoolId = routeSnapshot.parent.params['id'];
    }

    // get form values from the data received
    this._initScreen(this.route.snapshot.data['features']);
  }

  /**
   * Create form
   */
  private _CreateForm() {
    this.formGroup = new FormGroup({
      allergy: new FormControl(false),
      payAtCanteen: new FormControl(false),
      uniform: new FormControl(false),
      uniformDescription: new FormControl(''),
    });

    this.uniform.valueChanges.subscribe(event => {
      if (event) {
        this.uniformDescription.disable();
      } else {
        this.uniformDescription.enable();
      }
    });
  }

  get allergy() {
    return this.formGroup.get('allergy');
  }
  get payAtCanteen() {
    return this.formGroup.get('payAtCanteen');
  }
  get uniform() {
    return this.formGroup.get('uniform');
  }
  get uniformDescription() {
    return this.formGroup.get('uniformDescription');
  }

  /**
   *
   * @param data data received from the resolver
   */
  private _initScreen(data: AdminSchoolFeatures) {
    if (data) {
      // manage uniform shop availability
      this.uniformShopAvailable = data.uniformFeatureAvailable;

      if (data.uniformFeatureAvailable) {
        this.uniform.enable();
      } else {
        this.uniform.disable();
      }

      // manage features check
      if (data.features) {
        const payAtCanteen = data.features.find(x => x.type == SchoolFeatureTypeEnum.PayAtCanteen);
        this.payAtCanteen.setValue(payAtCanteen ? payAtCanteen.isActive : false);

        const allergy = data.features.find(x => x.type == SchoolFeatureTypeEnum.Allergies);
        this.allergy.setValue(allergy ? allergy.isActive : false);

        const uniform = data.features.find(x => x.type == SchoolFeatureTypeEnum.Uniform);
        this.uniform.setValue(uniform ? uniform.isActive : false);
        this.uniformDescription.setValue(uniform?.description ? uniform.description : '');

        if (this.uniform.value) {
          this.uniformDescription.disable();
        } else {
          this.uniformDescription.enable();
        }
      }
    }
  }

  SaveFeatures() {
    if (this.formGroup.valid) {
      let request: SchoolFeature[] = [];

      // allergy
      let allergyFeature = new SchoolFeature();
      allergyFeature.type = SchoolFeatureTypeEnum.Allergies;
      allergyFeature.isActive = this.allergy.value;
      request.push(allergyFeature);

      // pay at canteen
      let paycanteenFeature = new SchoolFeature();
      paycanteenFeature.type = SchoolFeatureTypeEnum.PayAtCanteen;
      paycanteenFeature.isActive = this.payAtCanteen.value;
      request.push(paycanteenFeature);

      // pay at canteen
      let uniformFeature = new SchoolFeature();
      uniformFeature.type = SchoolFeatureTypeEnum.Uniform;
      uniformFeature.isActive = this.uniform.value;
      uniformFeature.description = this.uniformDescription.value;
      request.push(uniformFeature);

      // call API
      this.schoolFeatureService.CreateFeatureApi(+this.schoolId, request).subscribe({
        next: (res: any) => {
          window.location.reload();
        },
        error: error => {
          this.ErrorModal('Saving features was unsuccessful', error);
        },
      });
    }
  }
}
