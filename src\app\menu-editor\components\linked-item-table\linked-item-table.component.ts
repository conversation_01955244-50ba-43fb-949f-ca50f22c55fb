import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { MenuItem } from 'src/app/sharedModels';

@Component({
  selector: 'linked-item-table',
  templateUrl: './linked-item-table.component.html',
  styleUrls: ['./linked-item-table.component.scss'],
})
export class LinkedItemTableComponent implements OnInit {
  @Input() tableData: MenuItem[];
  @Output() remove: EventEmitter<MenuItem> = new EventEmitter();

  constructor() {}

  ngOnInit(): void {}

  removeItem(item: MenuItem) {
    this.remove.emit(item);
  }
}
