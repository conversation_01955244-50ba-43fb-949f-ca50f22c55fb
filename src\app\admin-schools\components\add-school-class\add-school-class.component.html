<h3>Add / Edit Class</h3>
<form [formGroup]="form" (ngSubmit)="onSubmit()" class="cashlessForm">
  <mat-form-field appearance="outline">
    <mat-label>Class name</mat-label>
    <input matInput placeholder="Name" formControlName="name" type="text" required />
    <mat-error *ngIf="name.invalid">{{ getErrorMessageName() }}</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline">
    <mat-label>Teacher</mat-label>
    <input matInput placeholder="Teacher" formControlName="teacher" type="text" />
  </mat-form-field>

  <mat-form-field appearance="outline">
    <mat-label>Select year group</mat-label>
    <mat-select [formControl]="yearGroup" multiple>
      <mat-option *ngFor="let val of classValues" [value]="val">{{ val }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="outline">
    <mat-label>Display Order</mat-label>
    <input matInput placeholder="Display Order" formControlName="sortOrder" type="number" step="1" />
  </mat-form-field>

  <mat-error *ngIf="errorAPI">{{ WriteError() }}</mat-error>

  <div class="row buttonsWrapper">
    <div class="col-6">
      <button mat-flat-button color="primary" type="submit" [disabled]="!form.valid">{{ textSubmit }}</button>
      <button *ngIf="showButtonCancel" mat-flat-button type="button" (click)="CancelForm()">Cancel</button>
    </div>
    <div *ngIf="isEdit" class="col-6 archiveDiv">
      <button mat-flat-button type="button" class="archiveButton" (click)="ArchiveClicked()">Archive</button>
    </div>
  </div>
</form>
