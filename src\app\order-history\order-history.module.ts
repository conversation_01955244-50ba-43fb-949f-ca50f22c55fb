import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// module
import { OrderHistoryRoutingModule } from './order-history-routing.module';
import { SharedModule } from '../shared/shared.module';
import { PaymentModule } from '../payment/payment.module';
import { SchoolsButtonModule } from '../schools-button/schools-button.module';

// components
import {
  OrderHistoryComponent,
  CanteenHistoryComponent,
  EventHistoryComponent,
  UniformHistoryComponent,
  OrderHistoryRowComponent,
} from './components';
import { SharedToolsModule } from '../shared-tools/shared-tools.module';
import { OrderHistoryListComponent } from './components/order-history-list/order-history-list.component';

//pipes
import { OrderHistoryStatusPipe } from '../sharedPipes';

@NgModule({
  declarations: [
    OrderHistoryComponent,
    CanteenHistoryComponent,
    EventHistoryComponent,
    UniformHistoryComponent,
    OrderHistoryRowComponent,
    OrderHistoryRowComponent,
    OrderHistoryListComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    OrderHistoryRoutingModule,
    SharedModule,
    PaymentModule,
    SchoolsButtonModule,
    SharedToolsModule,
    SharedModule,
    OrderHistoryStatusPipe,
  ],
  exports: [OrderHistoryRoutingModule],
})
export class OrderHistoryModule {}
