@import '../../../../styles/cashless-theme.scss';

.merchant-heading {
  font-size: 28px;
  color: $grey-12;
}

.header {
  color: $grey-12;
  padding-bottom: 15px;

  h3 {
    margin: 0;
    padding: 0;
  }
}

.backButton {
  color: $orange-3;
}

.userTable {
  width: 100%;
}

a {
  cursor: pointer;
  color: $orange-3;
  display: flex;
  align-items: center;
  font-weight: 700;
}

a:hover {
  color: $orange-3;
}

.chevron {
  color: $orange-3;
}

.filterField {
  width: 100%;
}

.noResults {
  background-color: #ffffff;
  padding: 18px;

  h3 {
    padding: 0;
    padding-left: 5px;
    margin: 0;
    font-weight: 400;
    font-size: 16px;
  }
}

.mat-mdc-row:hover {
  background-color: $orange-7;
  cursor: pointer;
}
