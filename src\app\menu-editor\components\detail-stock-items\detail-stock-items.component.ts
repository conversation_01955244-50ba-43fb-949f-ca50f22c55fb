import { Component, OnInit, Input } from '@angular/core';

//ngrx
import { select, Store } from '@ngrx/store';
import { menuCategories } from 'src/app/states/canteen/canteen.selectors';
import { CanteenState } from 'src/app/states';

// Models
import {
  MenuItem,
  Stock,
  CategoryEditor,
  AddRemoveStockRequest,
  LinkedItemsComponent,
} from 'src/app/sharedModels';

// Service
import { SpinnerService, MenuEditorApiService } from 'src/app/sharedServices';

@Component({
  selector: 'editor-detail-stock-items',
  templateUrl: './detail-stock-items.component.html',
  styleUrls: ['./detail-stock-items.component.scss'],
})
export class DetailStockItemsComponent extends LinkedItemsComponent implements OnInit {
  @Input() stock: Stock;
  @Input() canteenId: number;

  constructor(
    protected spinnerService: SpinnerService,
    protected menuEditorAPIService: MenuEditorApiService,
    protected store: Store<{ canteen: CanteenState }>
  ) {
    super(spinnerService, menuEditorAPIService, store);
  }

  ngOnInit() {
    this.canteenSubscription = this.store
      .pipe(select(menuCategories))
      .subscribe((menuCategories: CategoryEditor[]) => {
        this.listCategories = menuCategories;
        this.selectedCategory = this.listCategories[0];
        this.updateListItems();
        this.createForm();
      });

    this.getMenuItems(this.canteenId);
  }

  addItem(item: MenuItem) {
    this.spinnerService.start();
    let request = this.getStockRequest(item, false);

    this.menuEditorAPIService.AddStockToItemAPI(request).subscribe({
      next: (response: any) => {
        this.updateListItems();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  removeItem(item: MenuItem) {
    this.spinnerService.start();
    let request = this.getStockRequest(item, true);

    this.menuEditorAPIService.RemoveStockFromItemAPI(request).subscribe({
      next: (response: any) => {
        this.updateListItems();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  updateListItems() {
    this.spinnerService.start();

    this.menuEditorAPIService.GetItemsByStockIdAPI(this.stock.StockId).subscribe({
      next: (response: MenuItem[]) => {
        this.linkedItems = response;
        this.filterItemsByCategory();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  getStockRequest(item: MenuItem, remove: boolean) {
    let request: AddRemoveStockRequest = new AddRemoveStockRequest();
    request.ItemId = item.MenuItemId;
    request.StockId = remove ? null : this.stock.StockId;
    request.CanteenId = item.CanteenId;
    request.MenuId = item.MenuId;

    return request;
  }
}
