import { inject } from '@angular/core';

import { Observable } from 'rxjs';
import { OrderStatusApiService } from '../../sharedServices/orderStatus/order-status-api.service';
import { OrderDashboardDto } from 'src/app/sharedModels/order/order-status';
import { ResolveFn } from '@angular/router';

export const DasboardErrorsResolver: ResolveFn<any> = (): Observable<OrderDashboardDto> => {
  const orderStatusApiService = inject(OrderStatusApiService);
  return orderStatusApiService.GetDashboardErrorsAPI();
};
