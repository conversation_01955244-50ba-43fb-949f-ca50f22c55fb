import { Component, OnInit, OnD<PERSON>roy, Inject } from '@angular/core';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';

// models
import { FilterMenuDate } from 'src/app/sharedModels';
import { BaseMenuDateFilterComponent } from '../base-filter-menu-date.component';

// Ngrx
import { Store } from '@ngrx/store';
import { FamilyState } from '../../../states';
import { DateTimeService } from 'src/app/sharedServices';

@Component({
  selector: 'filter-menu-date-sheet',
  templateUrl: './filter-menu-date-sheet.component.html',
  styleUrls: ['./filter-menu-date-sheet.component.scss'],
})
export class FilterMenuDateSheetComponent extends BaseMenuDateFilterComponent implements OnInit, OnDestroy {
  constructor(
    private _bottomSheetRef: MatBottomSheetRef<FilterMenuDateSheetComponent>,
    dateService: DateTimeService,
    @Inject(MAT_BOTTOM_SHEET_DATA)
    public data: FilterMenuDate,
    protected store: Store<{ family: FamilyState }>
  ) {
    super(store);
  }

  ngOnInit() {
    this.menuType = this.data.menuType;

    this.OnInitFunction();
  }

  CloseSheet() {
    this._bottomSheetRef.dismiss();
  }

  ngOnDestroy() {
    this.OnDestroyFunction();
  }
}
