import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SchoolNavBarComponent } from './school-nav-bar.component';

describe('SchoolNavBarComponent', () => {
  let component: SchoolNavBarComponent;
  let fixture: ComponentFixture<SchoolNavBarComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SchoolNavBarComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SchoolNavBarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
