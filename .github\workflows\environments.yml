name: 'Environments'

on:
  workflow_dispatch:
  push:
    branches:
      - "**"
    tags:
      - "v*"

permissions:
  id-token: write
  contents: read

jobs:
  test:
    if: ${{ !startsWith(github.ref, 'refs/tags/v') }}
    uses: ./.github/workflows/build-push.yml
    with:
      ENVIRONMENT: test
      DEPLOY: ${{ github.ref_name == 'master' && true || false }}
    secrets: inherit

  prod:
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    uses: ./.github/workflows/build-push.yml
    with:
      ENVIRONMENT: prod
      DEPLOY: true
    secrets: inherit