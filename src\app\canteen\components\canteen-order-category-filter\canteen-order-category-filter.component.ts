import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';

// models
import { CategoryEditor } from 'src/app/sharedModels';

@Component({
  selector: 'canteen-order-category-filter',
  templateUrl: './canteen-order-category-filter.component.html',
  styleUrls: ['../canteen-order-school-filter/canteen-order-school-filter.component.scss'],
})
export class CanteenOrderCategoryFilterComponent implements OnChanges {
  @Input() categories: CategoryEditor[];
  @Input() rowExpanded: boolean = false;
  @Output() categoriesSelected: EventEmitter<number[]> = new EventEmitter();
  selectedValueCategories: number[] = [];

  constructor() {}

  ngOnChanges(changes: SimpleChanges) {
    for (const propName in changes) {
      switch (propName) {
        case 'categories':
          this._prepareList();
          break;

        default:
          break;
      }
    }
  }

  /**
   * Prepare the school list to display
   */
  private _prepareList() {
    if (this.categories && this.categories.length > 0) {
      this.selectedValueCategories = [];

      this.categories.forEach(s => {
        this.selectedValueCategories.push(s.MenuCategoryId);
      });

      // if there is a preferred school, use it.
      let savedValue: number[] = JSON.parse(localStorage.getItem('prefCategoryId'));
      if (savedValue != null && savedValue.length > 0) {
        let index = this.selectedValueCategories.find(i => i == savedValue[0]);

        if (index > -1) {
          this.selectedValueCategories = savedValue;
        }
      } else {
        this._SaveCategories();
      }

      // trigger selected categories
      this.categoriesSelected.emit(this.selectedValueCategories);
    }
  }

  /**
   * Check if the given categoryId is selected
   * @param categoryId
   * @returns
   */
  IsChecked(categoryId: number): boolean {
    return this.selectedValueCategories.findIndex(s => s == categoryId) > -1;
  }

  /**
   * Checkbox value changed
   * @param event Checkbox event
   */
  CheckboxChanged(event: MatCheckboxChange) {
    const schoolId = +event.source.name;

    if (event.checked) {
      this.selectedValueCategories.push(schoolId);
    } else {
      let index = this.selectedValueCategories.findIndex(i => i == schoolId);
      if (index > -1) {
        this.selectedValueCategories.splice(index, 1);
      }
    }

    // save categories selection
    this._SaveCategories();

    // trigger selected categories
    this.categoriesSelected.emit(this.selectedValueCategories);
  }

  /**
   * Save the categories list in localStorage
   */
  private _SaveCategories() {
    localStorage.setItem('prefCategoryId', JSON.stringify(this.selectedValueCategories));
  }

  /**
   * Check if all categories are selected
   * @returns
   */
  IsAllSelected(): boolean {
    return this.selectedValueCategories.length == this.categories.length;
  }

  /**
   * Check if the selected categories list is empty
   * @returns
   */
  IsNoCategoriessSelected(): boolean {
    return this.selectedValueCategories.length == 0;
  }

  /**
   * Select all the categories available
   */
  SelectAll() {
    if (!this.IsAllSelected()) {
      this.selectedValueCategories = [];

      this.categories.forEach(s => {
        this.selectedValueCategories.push(s.MenuCategoryId);
      });

      this._SaveCategories();

      // trigger selected categories
      this.categoriesSelected.emit(this.selectedValueCategories);
    }
  }

  /**
   * Clear selected list
   */
  Clear() {
    if (!this.IsNoCategoriessSelected()) {
      this.selectedValueCategories = [];
      this.categoriesSelected.emit(this.selectedValueCategories);
    }
  }
}
