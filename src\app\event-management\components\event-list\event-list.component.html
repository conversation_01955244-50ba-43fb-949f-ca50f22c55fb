<ng-container *ngIf="loading; else showResult">
  <div class="col-12 d-flex justify-content-center">
    <app-spinner [manual]="true"></app-spinner>
  </div>
</ng-container>

<ng-template #showResult>
  <ng-container *ngIf="eventList?.length > 0; else noEvents">
    <ul>
      <li *ngFor="let schoolEvent of eventList">
        <div class="event-container">
          <event-row [schoolEvent]="schoolEvent" (onPress)="rowClick($event)"></event-row>
        </div>
      </li>
    </ul>
  </ng-container>

  <ng-template #noEvents>
    <div class="no-result-container">
      <p>No events</p>
    </div>
  </ng-template>
</ng-template>
