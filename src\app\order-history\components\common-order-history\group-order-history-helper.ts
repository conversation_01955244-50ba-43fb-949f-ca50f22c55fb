import * as _ from 'lodash';
import moment from 'moment';
import { EXPIRED_ORDER_TIME } from 'src/constants';

//Models
import {
  CanteenStatusEnum,
  MenuTypeEnum,
  OrderStatusConfig,
  OrderStatusEnum,
  OrderStatusTypeEnum,
  OrderStatusTypes,
  OrdersSortedByStudent,
  RefinedOrder,
  SortedOrderHistory,
} from 'src/app/sharedModels';
import {
  AMERICAN_DATE_FORMAT,
  DateHasPassed,
  UNIVERSAL_DATE_FORMAT,
  formatDateToDisplay,
} from 'src/app/utility';

export function SortGroupOrderHistory(orders: RefinedOrder[]): SortedOrderHistory[] {
  if (orders.length <= 0) {
    return;
  }
  const groupByDate: OrdersSortedByStudent = _.groupBy(orders, (item: RefinedOrder) =>
    formatDateToDisplay(item.OrderDate)
  );

  return Object.entries(groupByDate).map(([date, orders]) => {
    return createSortedOrderHistoryObject(date, orders);
  });
}

function createSortedOrderHistoryObject(date: string, orders: RefinedOrder[]): SortedOrderHistory {
  return {
    date,
    items: groupOrdersByStudentId(orders),
  };
}

function groupOrdersByStudentId(orders: RefinedOrder[]): RefinedOrder[] {
  return orders.sort((a, b) => a.StudentId - b.StudentId);
}

export function GetOrderStatusAndCutOffTimeData(order: RefinedOrder): OrderStatusTypes {
  let passedCutOffTime = null;
  let orderStatus = null;

  switch (order.MenuType) {
    case MenuTypeEnum.Uniform:
      orderStatus = getUniformOrderStatus(order.OrderStatusId, CanteenStatusEnum[order.CanteenStatus]);
      passedCutOffTime = orderStatus !== OrderStatusTypeEnum.Ordered;
      break;
    case MenuTypeEnum.Event:
      passedCutOffTime = DateHasPassed(order.EventCutOffDate);
      orderStatus = getOrderStatus(order.OrderStatusId, passedCutOffTime);
      break;
    default:
      passedCutOffTime = getPassedCutOffTime(order.OrderDate, order.MenuCutOffTime);
      orderStatus = getOrderStatus(order.OrderStatusId, passedCutOffTime);
  }

  return orderStatus;
}

function getUniformOrderStatus(statusId: number, canteenStatus: CanteenStatusEnum): OrderStatusTypeEnum {
  if (statusId === OrderStatusEnum.Error) return OrderStatusTypeEnum.ErrorOccurred;
  if (statusId === OrderStatusEnum.Cancelled) return OrderStatusTypeEnum.Cancelled;
  return OrderStatusConfig[canteenStatus];
}

function getOrderStatus(statusId: number, passedCutOffTime): OrderStatusTypes {
  if (statusId === OrderStatusEnum.Cancelled) {
    return 'Cancelled';
  } else if (statusId === OrderStatusEnum.Draft) {
    return 'Processing...';
  } else if (statusId === OrderStatusEnum.Completed) {
    return passedCutOffTime ? 'Completed' : 'Ordered';
  } else if (statusId === OrderStatusEnum.Error) {
    return passedCutOffTime ? 'PassedError' : 'Error Occurred';
  } else return 'Processing...';
}

const getPassedCutOffTime = (orderDate: string, cutOffTime: string): boolean => {
  const currentTime = moment().format('YYYY-MM-DDTHH:mm:ss');
  const cutOffHour = cutOffTime || EXPIRED_ORDER_TIME;

  const formattedOrderDate = moment(orderDate, AMERICAN_DATE_FORMAT).format(UNIVERSAL_DATE_FORMAT);
  const orderCutOffTime = moment(formattedOrderDate + cutOffHour, 'YYYY-MM-DDTHH:mm:ss');

  return moment(currentTime).diff(orderCutOffTime) > 0;
};
