<div class="sticky">
  <mat-toolbar color="primary" class="schools-toolbar">
    <div [ngClass]="{ 'disable-nav': disableMode }"></div>

    <button mat-icon-button [matMenuTriggerFor]="menuNav" class="d-block d-lg-none">
      <mat-icon>more_vert</mat-icon>
    </button>
    <mat-menu #menuNav="matMenu">
      <!-- mobile menu for admin -->
      <div *ngIf="ShowMenu(adminRole)">
        <button *ngFor="let data of displayData" mat-menu-item [routerLink]="data.Link">
          <span>{{ data.Name }}</span>
        </button>
        <button mat-menu-item (click)="SignOut()">
          <span>Sign out</span>
        </button>
      </div>
    </mat-menu>

    <!-- nav bar for admin -->
    <ul *ngIf="ShowMenu(adminRole)" class="d-none d-lg-block">
      <li *ngFor="let data of displayData">
        <a [routerLink]="data.Link" routerLinkActive="activeLink">{{ data.Name }}</a>
        <span class="tab-selected"></span>
      </li>
    </ul>

    <span class="example-spacer"></span>

    <!-- Profile Icon -->
    <div *ngIf="ShowMenu(adminRole)" class="d-none d-lg-block profile-icon">
      <navigation-profile-icon-svg [matMenuTriggerFor]="menu"></navigation-profile-icon-svg>
    </div>

    <mat-menu #menu="matMenu">
      <button mat-menu-item (click)="SignOut()">
        <mat-icon>exit_to_app</mat-icon>
        <span>Sign out</span>
      </button>
    </mat-menu>

    <!-- Spriggy Logo Mobile-->
    <div class="d-block d-lg-none" style="flex: 1 1 auto"></div>
    <span class="d-block d-lg-none">
      <schools-logo-svg class="logo-mobile"></schools-logo-svg>
    </span>
  </mat-toolbar>
  <div>
    <router-outlet></router-outlet>
  </div>
</div>
