import { Component, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject } from '@angular/core';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';

// models
import {
  BaseComponent,
  Menu,
  MenuItem,
  MenuItemAvailability,
  OrderAgainScreenData,
  OrderAgainSheetData,
  ReOrderDailyInfo,
  ReOrderDateAvailability,
  ReOrdersSummaryRequest,
  RefinedOrderItem,
} from 'src/app/sharedModels';

// Ngrx
import * as moment from 'moment';
import { CommonModule, KeyValue } from '@angular/common';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { SchoolsButtonModule } from 'src/app/schools-button/schools-button.module';
import { OrderAgain<PERSON><PERSON><PERSON>boxComponent } from '../order-again-checkbox/order-again-checkbox.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { OrderApiService } from 'src/app/sharedServices';
import {
  GetMenuItemsThatMatchOrderItemMenuId,
  UpdateOrderItemsWithMenuData,
} from '../../functions/menu-item-sort-helper';
import { AddTimeToDate, DateHasPassed, formatDateToUniversal } from 'src/app/utility';
import { MatExpansionModule } from '@angular/material/expansion';
import { CalculateOrderItemsPricePipe, OrderOptionsStringPipe } from 'src/app/sharedPipes';

export type OrderAgainDate = {
  value: string;
  title: string;
};

@Component({
  selector: 'reorder-filter',
  standalone: true,
  imports: [
    MatCheckboxModule,
    MatFormFieldModule,
    SchoolsButtonModule,
    ReactiveFormsModule,
    FormsModule,
    CommonModule,
    OrderAgainCheckboxComponent,
    SharedModule,
    MatExpansionModule,
    CalculateOrderItemsPricePipe,
    OrderOptionsStringPipe,
  ],
  templateUrl: './reorder-filter.component.html',
  styleUrls: ['./reorder-filter.component.scss'],
})
export class ReorderFilterComponent extends BaseComponent implements OnInit, OnDestroy {
  AMOUNT_OF_WEEKS = 3;
  dates = [];
  loading: boolean = true;
  dateValues: KeyValue<string, string>[] = [];
  form: FormGroup;
  dateList: ReOrderDateAvailability[] = null;
  priceUpdate: boolean = false;
  updatedOrderItems: RefinedOrderItem[] = null;

  constructor(
    private _bottomSheetRef: MatBottomSheetRef<ReorderFilterComponent>,
    @Inject(MAT_BOTTOM_SHEET_DATA)
    public data: OrderAgainSheetData,
    private formBuilder: FormBuilder,
    private orderApiService: OrderApiService
  ) {
    super();
  }

  ngOnInit() {
    const request = this.data.dateRequest;
    this.getOrderAgainDataInfo(request);
  }

  getOrderAgainDataInfo(request: ReOrdersSummaryRequest): void {
    this.orderApiService.getReOrderInfo(request).subscribe({
      next: (response: OrderAgainScreenData) => {
        if (response) {
          this.processResponse(response);
          this.generateDateForm();
        }
        this.loading = false;
      },
      error: error => {
        this.loading = false;
        this.handleErrorFromService(error);
      },
    });
  }

  processResponse(response: OrderAgainScreenData): void {
    this.updatedOrderItems = UpdateOrderItemsWithMenuData(response.Menu.MenuJSON, this.data.OrderItems);
    this.priceUpdate = this.orderPiceUpdated(this.updatedOrderItems);
    this.processDateAvailability(response);
  }

  processDateAvailability(response: OrderAgainScreenData): void {
    const dateInfo = response?.ReOrderDailyInfos || [];
    const menu = response?.Menu || null;

    this.dateList = dateInfo?.map((day: ReOrderDailyInfo) => {
      return {
        Date: day.OrderDate,
        Title: moment(day.OrderDate).format('ddd, DD/MM'),
        AllItemsAvailable: this.allItemsAvailable(day.OrderDate, menu),
        OrderDateAvailable: this.isOrderDateAvailable(day),
      };
    });
  }

  orderPiceUpdated(updatedOrderItems: RefinedOrderItem[]): boolean {
    const originalOrderPrice = this.getOrderItemsPrice(this.data.OrderItems);
    const newOrderPrice = this.getOrderItemsPrice(this.updatedOrderItems);

    return originalOrderPrice !== newOrderPrice;
  }

  getOrderItemsPrice(orderItems: RefinedOrderItem[]): number {
    return orderItems.reduce(
      (accumlator: number, value: RefinedOrderItem) => accumlator + +value.ItemPriceIncGst,
      0
    );
  }

  isOrderDateAvailable(day: ReOrderDailyInfo): boolean {
    return day.IsSchoolOpen && !day.IsOrderPlaced && !this.cutOffTimePassed(day.OrderDate);
  }

  cutOffTimePassed(date: Date): boolean {
    const time = moment(this.data.EarlyCutOffTime).format('HH:mm:ss');
    const cutOffDateTime = AddTimeToDate(date, time);

    return DateHasPassed(cutOffDateTime);
  }

  allItemsAvailableOnDate(menuItems: MenuItem[], date: Date): boolean {
    return menuItems.every(item => {
      return (
        !item.Availabilities?.length || this.isItemAvailableOnOrderDate(item.Availabilities, date.toString())
      );
    });
  }

  isItemAvailableOnOrderDate(availableDates: MenuItemAvailability[], orderDate: string): boolean {
    return availableDates.some(availability => {
      return (
        formatDateToUniversal(orderDate) >= formatDateToUniversal(availability.StartDate) &&
        formatDateToUniversal(orderDate) <= formatDateToUniversal(availability.EndDate)
      );
    });
  }

  allItemsAvailable(date: Date, menu: Menu): boolean {
    const orderItemIdList = this.data.OrderItems.map(item => item.MenuItemId);
    const matchingMenuItems = GetMenuItemsThatMatchOrderItemMenuId(menu.MenuJSON, orderItemIdList);
    const availableWeekDays = this.getOrderDayAvailability(matchingMenuItems);

    const currentDay = moment(date).day();
    const isAvailableOnWeekDay = availableWeekDays.includes(currentDay);

    return this.allItemsAvailableOnDate(matchingMenuItems, date) && isAvailableOnWeekDay;
  }

  getOrderDayAvailability(itemList: MenuItem[]) {
    let availabilities: string[] = [];
    itemList.forEach(item => {
      if (!item.AvailabilityDays) return;
      if (availabilities.length === 0) {
        availabilities = item.AvailabilityDays.split(',');
        return;
      }
      availabilities = this.getTwoArrayIntersection(availabilities, item.AvailabilityDays.split(','));
    });

    return this.shortWeekNameToIndexConverter(availabilities);
  }

  shortWeekNameToIndexConverter(array: string[]): number[] {
    const weekToIndex = {
      M: 1,
      T: 2,
      W: 3,
      Th: 4,
      F: 5,
    };
    return array.map(item => weekToIndex[item]);
  }

  getTwoArrayIntersection(firstArray: string[], secondArray: string[]): string[] {
    const intersection = [];
    firstArray.forEach(item => {
      if (secondArray.find(item2 => item === item2)) {
        intersection.push(item);
      }
    });
    return intersection;
  }

  generateDateForm(): void {
    // Generate dynamic date form
    this.form = this.formBuilder.group({
      dates: new FormArray([]),
    });

    // Create form control for each date checkbox value
    this.dateList.forEach(date => {
      const cannotPlaceOrder = !date.OrderDateAvailable || !date.AllItemsAvailable;
      this.dateFormArray.push(new FormControl({ value: false, disabled: cannotPlaceOrder }));
    });
  }

  get dateFormArray() {
    if (this.form) {
      return this.form?.get('dates') as FormArray;
    }
  }

  closeSheet(): void {
    const selectedDateArray = this.getSelectedDatesFromForm();
    this._bottomSheetRef.dismiss(selectedDateArray);
  }

  anyDatesSelected = (): boolean => {
    const anyDatesSelected = this.form?.controls?.dates?.value.some(dateIsSelected => dateIsSelected);
    return !anyDatesSelected;
  };

  getSelectedDatesFromForm(): Date[] {
    let dateArray = [];
    this.dateFormArray.controls.forEach((formControl, index: number) => {
      if (formControl.value) {
        const dateKeyValue = this.dateList[index];
        dateArray.push(dateKeyValue.Date);
      }
    });
    return dateArray;
  }

  ngOnDestroy(): void {}
}
