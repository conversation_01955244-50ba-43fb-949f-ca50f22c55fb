@import '../../../../styles/cashless-theme.scss';
@import 'src/styles/schools-theme';

.sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 65px;
  z-index: 999;
}
.user-header {
  background-color: $text-light-strong;
  box-shadow: inset 0px -1px 0px $neutral-grey-2;

  & img {
    display: inline-block;
    cursor: pointer;
  }

  & .button-align{
    text-align: right;
    align-content: center;

    & active-indicator {
      display: inline-block;
      margin-right: 20px;
    }
  }
}


.row-container {
  background-color: white;
  border-radius: 20px;
  padding: 20px;
  border: 1px $grey-2 solid;

  h3 {
    margin: 0;
  }
}