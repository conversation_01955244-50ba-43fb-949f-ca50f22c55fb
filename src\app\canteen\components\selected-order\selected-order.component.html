<div class="divOrderDetails mat-elevation-z2">
  <div class="header">
    <h3>Selected Order</h3>
    <basic-button
      *ngIf="showOrder"
      text="Print this order"
      (onPress)="printButtonPressed()"
      [buttonStyle]="1"
    ></basic-button>
  </div>

  <div *ngIf="showOrder">
    <div class="dataline">
      <strong>{{ order.SchoolName }}</strong>
    </div>
    <div class="dataline">
      <span>{{ order.ClassName }}</span>
      <span *ngIf="order.Allergies"> - ({{ order.Allergies }})</span>
    </div>
    <strong>{{ order.StudentName }}</strong>
    <ul>
      <li *ngFor="let item of itemsSelectedOrder">
        {{ item.Quantity }} x {{ item.Name }} {{ GetItemOptions(item) }}
      </li>
    </ul>
    <div *ngIf="order">Order ID: {{ order.LocalRunNumber }}</div>
  </div>
</div>
