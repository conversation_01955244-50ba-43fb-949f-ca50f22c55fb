import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

// models
import { PaymentMethod, BaseComponent } from 'src/app/sharedModels';

// services
import { PayService, UserService, SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'payment-list-payment-methods',
  templateUrl: './list-payment-methods.component.html',
  styleUrls: ['./list-payment-methods.component.scss'],
})
export class ListPaymentMethodsComponent extends BaseComponent implements OnInit {
  @Input() showDelete: boolean;
  @Input() canSelect: boolean = true;
  @Output() cardSelected: EventEmitter<string> = new EventEmitter<string>();

  noPaymentMethod: boolean = false;
  listPaymentMethod: PaymentMethod[] = [];
  loading: boolean = true;
  private selectedMethodId: string;

  constructor(private payService: PayService) {
    super();
  }

  ngOnInit(): void {
    this.payService.GetPaymentMethodsAPI().subscribe({
      next: (res: PaymentMethod[]) => {
        this.listPaymentMethod = res;
        this.checkForNoPaymentMethods();

        this.loading = false;
      },
      error: error => {
        this.loading = false;
        this.handleErrorFromService(error);
      },
    });
  }

  checkForNoPaymentMethods(): void {
    if (!this.listPaymentMethod || this.listPaymentMethod.length == 0) {
      this.noPaymentMethod = true;
    }
  }

  setSelectedMethod(methodId: string): void {
    if (!this.canSelect) {
      return;
    }
    this.selectedMethodId = methodId;
    this.cardSelected.emit(methodId);
  }

  isCurrentMethod(methodId: string): boolean {
    return this.selectedMethodId === methodId;
  }

  deleteClick(methodId: string): void {
    this.loading = true;

    this.payService.DeletePaymentMethodAPI(methodId).subscribe({
      next: (res: boolean) => {
        let index = this.listPaymentMethod.findIndex(i => i.id === methodId);

        if (index >= 0) {
          this.listPaymentMethod.splice(index, 1);
        }
        this.checkForNoPaymentMethods();
        this.loading = false;
      },
      error: error => {
        this.loading = false;
        this.handleErrorFromService(error);
      },
    });
  }
}
