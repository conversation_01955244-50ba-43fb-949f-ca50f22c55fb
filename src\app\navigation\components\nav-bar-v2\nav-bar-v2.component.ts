import { Component, OnInit, OnDestroy } from '@angular/core';

// models
import { UserCashless, Roles } from '../../../sharedModels';

// services
import { UserService, SpinnerService, MerchantService } from '../../../sharedServices';

// ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { CanteenState } from '../../../states';
import { canteenStateSelector } from '../../../states/canteen/canteen.selectors';
import { NavBarData } from 'src/app/sharedModels/navBar/navBar';

const adminNavData: NavBarData[] = [
  { Name: 'Schools', Link: '/admin/schools' },
  { Name: 'Merchants', Link: '/admin/merchants' },
  { Name: 'Users', Link: '/admin/users' },
  { Name: 'Noticeboard', Link: '/admin/notice' },
  { Name: 'Events', Link: '/admin/events' },
  { Name: 'Dashboard', Link: '/admin/dashboard' },
];

@Component({
  selector: 'nav-bar-v2',
  templateUrl: './nav-bar-v2.component.html',
  styleUrls: ['./nav-bar-v2.component.scss'],
})
export class NavBarV2Component implements OnInit, OnDestroy {
  user: UserCashless;
  canteenRole: Roles;
  adminRole: Roles;
  displayData: NavBarData[] = adminNavData;
  isUniformCanteen: boolean = false;
  isMenuEditorRole: boolean = false;
  disableMode: boolean = false;
  private subscription: Subscription;
  private editSubscription: Subscription;

  constructor(
    private userService: UserService,
    private spinnerService: SpinnerService,
    private merchantService: MerchantService,
    private store: Store<{ canteen: CanteenState }>
  ) {}

  ngOnInit() {
    this.canteenRole = Roles.Canteen;
    this.adminRole = Roles.Admin;

    this.spinnerService.stop();
    this.userService.IdentifyUser();

    this.subscription = this.store.pipe(select(canteenStateSelector)).subscribe((state: CanteenState) => {
      if (state.selected) {
        this.isUniformCanteen = state.selected.CanteenType == 'Uniform';
        this.isMenuEditorRole = state.selected.IsMenuEditorAvailable;
      } else {
        this.isUniformCanteen = false;
        this.isMenuEditorRole = false;
      }
    });

    this.editSubscription = this.merchantService.editMode$.subscribe(status => {
      this.disableMode = status;
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    if (this.editSubscription) {
      this.editSubscription.unsubscribe();
    }
  }

  ShowMenu(role: Roles): boolean {
    this.user = this.userService.GetUserConnected();

    if (this.user) {
      return this.user.Role == role;
    } else {
      return false;
    }
  }

  SignOut(): void {
    this.userService.logout();
  }
}
