<div class="col-lg-6 col-md-8 col-sm-12">
  <ng-container *ngIf="loading; else displayOrders" class="row justify-content-center">
    <div class="col-3 col-sm-3 col-lg-1 justify-content-center">
      <app-spinner [manual]="true"></app-spinner>
    </div>
  </ng-container>

  <ng-template #displayOrders>
    <ng-container *ngIf="listOrders?.length > 0; else noOrders">
      <div *ngFor="let groupedOrders of listOrders" class="pb-2">
        <p>{{ groupedOrders.date }}</p>
        <div *ngFor="let order of groupedOrders.items" class="pb-2">
          <order-history-row [order]="order" [student]="getOrderChild(order.StudentId)"></order-history-row>
        </div>
      </div>
      <p class="message">That's all of your history</p>
    </ng-container>
    <ng-template #noOrders>
      <p class="message">You have no order history</p>
    </ng-template>
  </ng-template>
</div>
