import { KeyValue } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';

@Component({
  selector: 'editor-checkbox-list',
  templateUrl: './editor-checkbox-list.component.html',
  styleUrls: ['./editor-checkbox-list.component.scss'],
})
export class EditorCheckboxListComponent implements OnInit {
  @Input() formGroup: FormGroup;
  @Input() title: string;
  @Input() values: KeyValue<string, string>[];
  @Input() newTheme: boolean = false;

  constructor() {}

  ngOnInit(): void {
  }
}
