@import '../../../../styles/cashless-theme.scss';

// h2 {
//   color: $orange-3;
// }

mat-form-field {
  width: 100%;
}

// .divButton {
//   margin-top: 20px;
//   display: flex;
//   justify-content: space-between;
// }

// .submitButton {
//   width: 55px;
//   height: 35px;
//   font-size: 16px;
//   padding: 10px;
// }

// .deleteButton {
//   font-size: 16px;
//   width: 100px;
//   height: 35px;
//   padding: 0px;
// }

// /* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
// mat-radio-button {
//   margin: 0 10px 30px 0;
// }

.title {
  margin: 0;
  margin-bottom: 3px;
  margin-top: 40px;
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  color: #000000;
}

.ruleTitle {
  margin: 0;
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 20px;
  color: #000000;
}

.ruleDescription {
  margin: 0;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 15px;
  color: #6d7681;
}

.checkBoxWrapper {
  margin-bottom: 3px;
}

.textWrapper {
  margin-top: 15px;
}

.div-button {
    text-align: right;
    margin-top: 50px;
  }