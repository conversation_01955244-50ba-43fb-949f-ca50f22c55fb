@import '../../../../styles/cashless-breakpoints.scss';
@import '../../../../styles/cashless-theme.scss';
@import '../../../../styles/cashless-font.scss';

.childrenListCol {
  padding-top: 10px;
  padding-bottom: 10px;
}

.titleCreateOrders {
  padding-right: 15px;
  display: inline-block;
}

.SuppliesOrder {
  background-color: $grey-4;
  border: none;
  box-shadow: none;
  padding: 0;
  padding-left: 10px;

  @media (max-width: $breakpoint-md) {
    margin-top: 20px;
  }
}

.table {
  margin: auto;
  width: 50% !important;

  &.newOrderTable {
    padding-top: 10px;
  }
}

.tableHeader {
  margin: auto;
  text-align: center;
}

.headerText {
  font-family: 'bariol_bold';
  font-size: 18px;
}

.blocSupplies {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  margin: 5px;
  text-align: center;
  color: $charcoal-1;
  cursor: pointer;
  background-color: white;
  display: flex;
  flex-flow: row wrap;

  -webkit-box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);

  & span {
    margin-top: 15px;
    margin-left: 10px;
  }

  & p {
    opacity: 0.5;
  }

  & .icon {
    display: inline-block;
    width: 26px;
    height: 26px;
    background-size: cover;

    &.icon-canteen {
      background-image: url('../../../../assets/images/canteen-order-icon.svg');
    }
    &.icon-uniform {
      width: 28px;
      background-image: url('../../../../assets/images/uniform-shop-icon.png');
    }
  }

  &.selected {
    background: linear-gradient(96.62deg, $orange-1 0%, $orange-2 100%);
    color: white;
    cursor: pointer;

    & p {
      opacity: 1;
    }

    & .icon {
      display: inline-block;
      width: 26px;
      height: 26px;
      background-size: cover;

      &.icon-canteen {
        background-image: url('../../../../assets/images/canteen-order-icon-active.svg');
      }

      &.icon-uniform {
        width: 28px;
        background-image: url('../../../../assets/images/uniform-shop-icon-active.png');
      }
    }
  }
}

.comingSoon {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  margin: 5px;
  text-align: center;
  opacity: 0.5;
  color: #333b44;
  cursor: pointer;
  //background-color: white;
  display: flex;
  flex-flow: row wrap;
  font-size: 12px;
  @media (min-width: $breakpoint-md) {
  }

  &.selected {
    background: linear-gradient(96.62deg, $orange-1 0%, $orange-2 100%);
    color: white;
    cursor: pointer;
  }
}

.suppliesTextMain {
  font-family: 'bariol_bold';
  font-size: 18px;
  margin: auto; /* Magic! */
  white-space: normal;

  &.hasImg {
    text-align: left;
    margin-top: 0;
    margin-left: 10px;
  }
}

.suppliesTextSecondary {
  font-family: 'bariol_bold';
  margin: auto; /* Magic! */
}

.suppliesTextComingSoon {
  font-family: 'bariol_bold';
  font-size: 16px;
  margin: auto; /* Magic! */
  white-space: normal;
}

.hidden {
  display: none;
}

.backgroundEvents {
  background-color: white;
}

.tdDesktop {
  display: none;

  @media (min-width: $breakpoint-md) {
    display: table-cell;
  }
}
