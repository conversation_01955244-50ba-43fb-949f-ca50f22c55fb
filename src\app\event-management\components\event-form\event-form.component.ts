import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
  EventEmitter,
  Output,
  SimpleChanges,
  OnChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { SchoolsButtonModule } from 'src/app/schools-button/schools-button.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_NATIVE_DATE_FORMATS,
  MatNativeDateModule,
  NativeDateAdapter,
} from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { EventTemplate, Roles, SchoolClass, SchoolEvent, SchoolEventInputValues } from 'src/app/sharedModels';
import moment from 'moment';
import { ConvertToUniversalDateFormat, DateHasPassed, UNIVERSAL_DATE_TIME_FORMAT } from 'src/app/utility';
import { MatMomentDateModule } from '@angular/material-moment-adapter';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { UserService } from 'src/app/sharedServices';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'event-form',
  standalone: true,
  templateUrl: './event-form.component.html',
  styleUrls: ['./event-form.component.scss'],
  imports: [
    CommonModule,
    SchoolsButtonModule,
    MatFormFieldModule,
    MatNativeDateModule,
    ReactiveFormsModule,
    FormsModule,
    MatInputModule,
    MatDatepickerModule,
    MatSelectModule,
    EventFormComponent,
    MatMomentDateModule,
    MatCheckboxModule,
    MatIconModule,
  ],
  providers: [
    { provide: DateAdapter, useClass: NativeDateAdapter },
    { provide: MAT_DATE_FORMATS, useValue: MAT_NATIVE_DATE_FORMATS },
    UserService,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EventFormComponent implements OnInit, OnChanges {
  @Input() eventToEdit: SchoolEvent;
  @Input() schoolClasses: SchoolClass[];
  @Input() selectedTemplate: EventTemplate;
  @Output() saveEvent = new EventEmitter<any>();
  form: FormGroup;
  todaysOrEventDate: Date = new Date();
  buttonText: string;
  isAdmin: boolean = false;
  isEdit: boolean = false;
  showWarning: boolean = false;
  eventDateHasPassed: boolean = false;

  constructor(private userService: UserService) {}

  ngOnInit() {
    const user = this.userService.GetUserConnected();
    this.isAdmin = user.Role == Roles.Admin;

    if (this.eventToEdit && this.eventToEdit.SchoolEventId > 0) {
      this.isEdit = true;

      const comparisonDate = moment(this.eventToEdit.EventDate)
        .set({ hour: 23, minute: 59 })
        .format(UNIVERSAL_DATE_TIME_FORMAT);
      this.eventDateHasPassed = DateHasPassed(comparisonDate);

      if (moment(this.todaysOrEventDate).toDate() >= moment(this.eventToEdit.EventDate).toDate())
        this.todaysOrEventDate = moment(this.eventToEdit.EventDate).toDate();
    }

    this.buttonText = this.eventToEdit ? 'Save' : 'Create event';
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges) {
    for (const propName in changes) {
      switch (propName) {
        case 'selectedTemplate':
          if (this.selectedTemplate) {
            this.title.setValue(this.selectedTemplate.title);
            this.description.setValue(this.selectedTemplate.description);
          }

          break;

        default:
          break;
      }
    }
  }

  createForm(): void {
    const inputValues = this.getInputValues();
    const disableClassSelector: boolean = !this.schoolClasses || this.schoolClasses?.length <= 0;
    const disabledDatePicker: boolean = this.eventDateHasPassed && this.isEdit && !this.isAdmin;

    this.form = new FormGroup({
      title: new FormControl(inputValues.Name, [Validators.required, Validators.maxLength(40)]),
      description: new FormControl(inputValues.Description, [Validators.required, Validators.maxLength(500)]),
      eventDate: new FormControl({ value: inputValues.EventDate, disabled: disabledDatePicker }, [
        Validators.required,
      ]),
      cutOffDate: new FormControl({ value: inputValues.CutOffDate, disabled: disabledDatePicker }, [
        Validators.required,
      ]),
      eventTime: new FormControl({ value: inputValues.EventTime, disabled: disabledDatePicker }),
      cutOffTime: new FormControl({ value: inputValues.CutOffTime, disabled: disabledDatePicker }, [
        Validators.required,
      ]),
      waiveEventOrderFee: new FormControl(inputValues.WaiveEventOrderFee),
      selectedClasses: new FormControl({
        value: inputValues.SpecificClasses,
        disabled: disableClassSelector,
      }),
    });

    this.eventDate.valueChanges.subscribe(value => {
      this.showWarning = this.eventToEdit.IsActive && this.eventDate != value;
    });
  }

  getInputValues(): SchoolEventInputValues {
    const title = this.eventToEdit ? this.eventToEdit.Name : '';
    const description = this.eventToEdit ? this.eventToEdit.Description : '';
    const eventDate = this.eventToEdit ? this.eventToEdit.EventDate : '';
    const cutOffDate = this.eventToEdit ? this.eventToEdit.CutOffDate : '';
    const selectedClasses: number[] = this.getSchoolClassInputValue();
    const waiveEventOrderFee = this.eventToEdit ? this.eventToEdit.WaiveEventOrderFee : false;

    //Time picker values
    const eventTime = this.eventToEdit ? this.eventToEdit.EventDate.slice(11) : '';
    const eventCutOffTime = this.eventToEdit ? this.eventToEdit.CutOffDate.slice(11) : '';

    return {
      Name: title,
      Description: description,
      EventDate: ConvertToUniversalDateFormat(eventDate),
      CutOffDate: ConvertToUniversalDateFormat(cutOffDate),
      SpecificClasses: selectedClasses,
      EventTime: eventTime,
      CutOffTime: eventCutOffTime,
      WaiveEventOrderFee: waiveEventOrderFee,
    };
  }

  getSchoolClassInputValue(): number[] {
    return this.eventToEdit && this.eventToEdit.SpecificClasses
      ? this.convertClassesToInt(this.eventToEdit.SpecificClasses.split(','))
      : [];
  }

  convertClassesToInt(classArray: string[]): number[] {
    return classArray?.map(c => parseInt(c)) || [];
  }

  combineDateTime(date: Date, time: string): string {
    if (time) {
      date.setHours(parseInt(time.split(':')[0]));
      date.setMinutes(parseInt(time.split(':')[1]));
    }
    return this.formatDateTime(date);
  }

  formatEvent(): void {
    if (!this.form.valid) {
      return;
    }
    const eventDate = this.combineDateTime(new Date(this.eventDate.value), this.eventTime.value);
    const cutOffDate = this.combineDateTime(new Date(this.cutOffDate.value), this.cutOffTime.value);

    const data = new SchoolEvent();
    data.Name = this.title.value;
    data.MerchantId = 0;
    data.Description = this.description.value;
    data.EventDate = eventDate;
    data.CutOffDate = cutOffDate;
    data.SpecificClasses = this.selectedClasses.value?.toString();
    data.SchoolEventId = null;
    data.WaiveEventOrderFee = this.waiveEventOrderFee.value;

    this.saveEvent.emit(data);
  }

  formatDateTime(dateTime: Date): string {
    return moment.parseZone(dateTime).utc(true).format();
  }

  disableSaveButton(): boolean {
    // if (this.eventToEdit) {
    //   return !this.form.dirty || !this.form.valid;
    // }
    return !this.form.valid;
  }

  get eventDate() {
    return this.form.get('eventDate');
  }

  get cutOffDate() {
    return this.form.get('cutOffDate');
  }

  get eventTime() {
    return this.form.get('eventTime');
  }

  get cutOffTime() {
    return this.form.get('cutOffTime');
  }

  get title() {
    return this.form.get('title');
  }

  get waiveEventOrderFee() {
    return this.form.get('waiveEventOrderFee');
  }

  get description() {
    return this.form.get('description');
  }

  get descriptionFill() {
    return this.form.get('description').value ? true : false;
  }

  get selectedClasses() {
    return this.form.get('selectedClasses');
  }
}
