<div class="row">
  <ng-container *ngIf="invoiceForSelectedWeek; else noInvoice">
    <p class="col-12 subtitle">Export as .csv</p>
    <div class="col-6">
      <basic-button
        text="Settlement"
        [buttonStyle]="1"
        [fullWidth]="true"
        (click)="buttonPressed(InvoiceType.Settlement)"
      ></basic-button>
    </div>
    <div class="col-6">
      <basic-button
        text="Invoice"
        [buttonStyle]="1"
        [fullWidth]="true"
        (click)="buttonPressed(InvoiceType.Invoice)"
      ></basic-button>
    </div>
  </ng-container>

  <ng-template #noInvoice>
    <div class="col-6">
      <basic-button
        text="Generate report"
        [buttonStyle]="1"
        [fullWidth]="true"
        (click)="buttonPressed(InvoiceType.Generate)"
      ></basic-button>
    </div>
  </ng-template>
</div>
