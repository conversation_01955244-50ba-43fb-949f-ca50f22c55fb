<div *ngIf="isLoading; else notLoading" class="row justify-content-center">
  <div class="col-4 col-sm-4 col-lg-2 spinnerBlock">
    <app-spinner [manual]="true"></app-spinner>
  </div>
</div>

<ng-template #notLoading>
  <div *ngIf="showOrderTable(); else noCanteen" class="row justify-content-center">
    <div class="col-12 noPadding">
      <div class="cardDefaultParent scrolling-horizontal-wrapper">
        <table class="table newOrderTable">
          <tr>
            <td class="tdDesktop"></td>
            <td class="tableHeader">
              <span class="mobile headerText">Mon</span>
              <span class="desktop headerText">Monday</span>
            </td>
            <td class="tableHeader">
              <span class="mobile headerText">Tue</span>
              <span class="desktop headerText">Tuesday</span>
            </td>
            <td class="tableHeader">
              <span class="mobile headerText">Wed</span>
              <span class="desktop headerText">Wednesday</span>
            </td>
            <td class="tableHeader">
              <span class="mobile headerText">Thu</span>
              <span class="desktop headerText">Thursday</span>
            </td>
            <td class="tableHeader">
              <span class="mobile headerText">Fri</span>
              <span class="desktop headerText">Friday</span>
            </td>
          </tr>
          <tr class="trMobile">
            <td colspan="5" class="daysRow"></td>
          </tr>
          <tr *ngIf="recessName" class="familyBlockLine">
            <td class="tdDesktop tableHeader headerText">{{ recessName }}</td>
            <td *ngFor="let data of recessDayData">
              <family-menu-block [dayData]="data" [child]="selectChild"></family-menu-block>
            </td>
          </tr>
          <tr *ngIf="recessName" class="trMobile">
            <td colspan="5" class="recessType">{{ recessName }}</td>
          </tr>
          <tr *ngIf="lunchName" class="familyBlockLine">
            <td class="tdDesktop tableHeader headerText">{{ lunchName }}</td>
            <td *ngFor="let data of lunchDayData">
              <family-menu-block [dayData]="data" [child]="selectChild"></family-menu-block>
            </td>
          </tr>
          <tr *ngIf="lunchName" class="trMobile">
            <td colspan="5">{{ lunchName }}</td>
          </tr>
        </table>
      </div>
    </div>
  </div>

  <ng-template #noCanteen>
    <div class="row justify-content-center background">
      <!-- No food menu found -->
      <div class="col-12 col-sm-6">
        <p class="description">There is no active canteen for your school</p>
        <p class="description">
          Would you like to see your school's canteen here?
          <span style="cursor: pointer" (click)="triggerIntercom()">Get in touch</span>, we'd love to help!
        </p>
      </div>
    </div>
  </ng-template>
</ng-template>
