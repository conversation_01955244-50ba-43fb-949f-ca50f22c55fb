<nav-back-button
  smallText="true"
  (navBack)="goBackClick()"
  text="Go Back"
  class="backButton"
  noPadding="true"
></nav-back-button>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <school-header title="Finance Reports v2"></school-header>
    </div>
  </div>
  <div class="row">
    <div class="col-lg-6 col-md-9 col-sm-12">
      <div class="pt-4">
        <weekly-report [generatedInvoiceList]="generatedInvoiceList"></weekly-report>
      </div>

      <div class="pt-4">
        <school-panel>
          <p class="mb-0 panelTitle">Export settlement information for all active schools</p>
          <form *ngIf="formGroupDates" [formGroup]="formGroupDates" class="pb-4">
            <div class="row pt-4">
              <div class="col-6">
                <input-date
                  placeholder="Start Date"
                  formControlName="startDate"
                  [error]="startDate.invalid ? invalidValueError : null"
                ></input-date>
              </div>
              <div class="col-6">
                <input-date
                  placeholder="End Date"
                  formControlName="endDate"
                  [error]="endDate.invalid ? invalidValueError : null"
                ></input-date>
              </div>
              <p class="col-12 subtitle">Export as .csv</p>
              <div class="col-6">
                <basic-button
                  text="Export"
                  [buttonStyle]="1"
                  [fullWidth]="true"
                  (click)="getRevenue()"
                ></basic-button>
              </div>
            </div>
          </form>
        </school-panel>
      </div>
    </div>
  </div>
</div>
