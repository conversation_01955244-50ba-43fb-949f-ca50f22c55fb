@import '../../../../styles/cashless-theme.scss';

.StatusOrder {
  font-size: 20px;
  text-align: center;
  margin-top: 15px;
  margin-bottom: 15px;

  & .completedOrder {
    color: $green-1;
  }

  & .processingOrder {
    color: $orange-3;
  }

  & .errorOrder {
    color: red;
  }
}

.SelectItems {
  font-size: 18px;
}

.itemRow {
  background-color: white;
  border-bottom: 1px solid $grey-3;
  padding-top: 16px;
  padding-bottom: 16px;
  color: #333b44;

  -webkit-box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);

  .colQuantity {
    text-align: center;
    padding-right: 0;

    span {
      display: block;
      font-size: 20px;
    }
  }

  .itemName {
    font-size: 20px;
    margin: 0;
  }

  .spacerDescription {
    font-size: 30px;
    line-height: 16px;
    font-weight: bold;
  }
}

.totalRow {
  margin-top: 24px;
  background-color: white;
  border-bottom: 1px solid $grey-3;
  text-align: right;

  -webkit-box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);

  h5 {
    margin-top: 14px;
    margin-bottom: 14px;
    font-size: 20px;
  }
}

.buttonCol {
  margin-top: 24px;
}

.processingOrder {
  text-align: center;
  color: $orange-3;
  font-size: 18px;
}

.errorOrder {
  text-align: center;
  color: red;
  font-size: 18px;
}

::ng-deep .mat-bottom-sheet-container {
  padding: 8px 16px;
  min-width: 100vw;
  box-sizing: border-box;
  display: block;
  outline: 0;
  max-height: 80vh;
  overflow: auto;
  height: 80vh;
}
