<ng-container *ngIf="menuItemId > 0">
  <h2>Images</h2>
  <div class="cardDefaultCanteen">
    <div class="row">
      <div class="col-12">
        <div *ngFor="let img of itemImages" class="previewImage">
          <img [src]="GetUrlImage(img)" alt="image item" width="100" height="85" />
          <div>
            <a (click)="DeleteImage(img)">Delete</a>
          </div>
        </div>
      </div>
    </div>

    <editor-image-upload
      [menuItemId]="menuItemId"
      [schoolCode]="schoolCode"
      (newImage)="NewImage($event)"
      [showError]="showError"
    ></editor-image-upload>
    <div class="divButton">
      <button class="PrimaryButton deleteButton" (click)="onSubmit()">Save Image</button>
    </div>
  </div>
</ng-container>
