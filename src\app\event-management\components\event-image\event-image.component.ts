import { NgIf } from '@angular/common';
import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { SchoolsButtonModule } from "../../../schools-button/schools-button.module";

// services
import { SchoolEventManagerService, SpinnerService } from 'src/app/sharedServices';
import { SharedModule } from 'src/app/shared/shared.module';
import { ImagePlaceholderComponent } from "../image-placeholder/image-placeholder.component";

@Component({
  selector: 'event-image',
  templateUrl: './event-image.component.html',
  styleUrls: ['./event-image.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    SchoolsButtonModule,
    SharedModule,
    ImagePlaceholderComponent
]
})
export class EventImageComponent {
  @Input() imageUrl: string;
  @Input() eventId: number;
  fileToUpload: File = null;
  url: string = '';

  constructor(private spinnerService: SpinnerService,
    private eventService: SchoolEventManagerService,
    private cd: ChangeDetectorRef
  ){

  }
  onSelectFile(files: FileList) {
    if (files.length === 0) return;

    this.fileToUpload = files.item(0);
    let file = new File([this.fileToUpload], this.fileToUpload.name.slice());
    this.fileToUpload = file;
    const fileReader: FileReader = new FileReader();
    fileReader.readAsDataURL(this.fileToUpload);

    fileReader.onload = (event: any) => {
      this.url = event.target.result;
      this.cd.detectChanges();
    };
  }

  Upload() {
    this.spinnerService.start();
    let file: any = { data: this.fileToUpload, fileName: this.fileToUpload.name }

    // not using schoolcode anymore, use canteenid now
    this.eventService
      .UploadEventImage(file, this.eventId).subscribe({
        next: (response: any) => {
          this.imageUrl = response.body;
          this.url = '';
          this.spinnerService.stop();
          this.cd.detectChanges();
        },
        error: error => {
          this.spinnerService.stop();
        },
      });
  }

  

  DeleteImage() {
    this.spinnerService.start();

    this.eventService.DeleteEventImage(this.eventId).subscribe({
      next: () => {
        this.imageUrl = '';
        this.url = '';
        this.spinnerService.stop();
        this.cd.detectChanges();
      },
      error: error => {
        this.spinnerService.stop();
      },
    });
  }

}
