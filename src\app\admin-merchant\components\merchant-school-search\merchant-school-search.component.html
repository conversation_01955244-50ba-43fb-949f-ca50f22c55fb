<div>
  <search-panel
    (triggerSearch)="fetchData($event)"
    (triggerClear)="clearFilter()"
    [searchInput]="listfilters.Filter"
    placeholder="Search the name of a school..."
  ></search-panel>
  <div>
    <h3 *ngIf="noResultsMessage">{{ noResultsMessage }}</h3>

    <div *ngIf="showResultsTable">
      <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 tableau schoolTable">
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef>school ID</th>
          <td mat-cell *matCellDef="let element">{{ element.SchoolId }}</td>
        </ng-container>

        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Name</th>
          <td mat-cell *matCellDef="let element">{{ element.Name }}</td>
        </ng-container>

        <ng-container matColumnDef="cutOffTime">
          <th mat-header-cell *matHeaderCellDef>Cut off time</th>
          <td mat-cell *matCellDef="let element">
            <span *ngIf="ShowCutOffTime(element.CutOffTime)"
              >{{ element.CutOffTime | date : 'shortTime' }}
            </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="deactivatedFilters">
          <th mat-header-cell *matHeaderCellDef>Phone</th>
          <td mat-cell *matCellDef="let element">{{ element.PhoneNumber }}</td>
        </ng-container>

        <ng-container matColumnDef="schoolCode">
          <th mat-header-cell *matHeaderCellDef>School code</th>
          <td mat-cell *matCellDef="let element">{{ element.SchoolCode }}</td>
        </ng-container>

        <ng-container matColumnDef="pricingModel">
          <th mat-header-cell *matHeaderCellDef>Pricing model</th>
          <td mat-cell *matCellDef="let element">{{ element.PricingModel }}</td>
        </ng-container>

        <ng-container matColumnDef="pricingAmount">
          <th mat-header-cell *matHeaderCellDef>Pricing amount</th>
          <td mat-cell *matCellDef="let element">{{ element.PricingAmount | currency }}</td>
        </ng-container>

        <ng-container matColumnDef="pricingCap">
          <th mat-header-cell *matHeaderCellDef>Pricing Cap</th>
          <td mat-cell *matCellDef="let element">{{ element.PricingCap }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns" (click)="schoolSelected(row)"></tr>
      </table>
    </div>
  </div>

  <!-- spacer under table -->
  <div style="height: 70px"></div>
</div>
