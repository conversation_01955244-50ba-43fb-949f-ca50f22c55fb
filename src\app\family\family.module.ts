import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

import { FamilyRoutingModule } from './family-routing.module';

// google material
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';

// Components
import { FamilyComponent } from './components/family/family.component';
import { SharedModule } from '../shared/shared.module';
import { FamilyNavBarComponent } from './components/nav-bar/nav-bar.component';
import { FamilyMenuBlockComponent } from './components/family-menu-block/family-menu-block.component';
import { ListEventsComponent } from './components/list-events/list-events.component';
import { EventComponent } from './components/event/event.component';
import { TableWeekOrderComponent } from './components/table-week-order/table-week-order.component';
import { FamilyUniformComponent } from './components/family-uniform/family-uniform.component';
import { FamilyArticlesComponent, ArticleModalComponent } from './components/articles/articles.component';
import { FamilyCanteenDisplayComponent } from './components/family-canteen-display/family-canteen-display.component';
import { FamilyAnnouncementsComponent } from './components/family-announcements/family-announcements.component';
import { WeekdayOrderStatusPipe } from './components';

@NgModule({
  declarations: [
    FamilyComponent,
    FamilyNavBarComponent,
    FamilyMenuBlockComponent,
    ListEventsComponent,
    EventComponent,
    TableWeekOrderComponent,
    FamilyUniformComponent,
    FamilyArticlesComponent,
    ArticleModalComponent,
    FamilyCanteenDisplayComponent,
    FamilyAnnouncementsComponent,
    WeekdayOrderStatusPipe,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    FamilyRoutingModule,
    SharedModule,
    // material
    MatFormFieldModule,
    MatCardModule,
    MatToolbarModule,
    MatSidenavModule,
    MatListModule,
    MatMenuModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
    MatTooltipModule,
  ],
})
export class FamilyModule {}
