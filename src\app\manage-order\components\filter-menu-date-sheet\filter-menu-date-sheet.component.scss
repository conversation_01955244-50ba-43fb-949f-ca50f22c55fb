@import '../../../../styles/cashless-theme.scss';

h2 {
  color: $orange-3;
  margin-bottom: 40px;
  margin-top: 5px;
}
.rowSheet {
  margin-bottom: 20px;
}
.sheetButton {
  margin-bottom: 200px;
}

.errorContainer {
  display: flex;
  justify-content: center;
  flex-direction: row;
  gap: 10px;
  margin-bottom: 20px;
  margin-top: 25px;
}

.errorText {
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 24px;
  text-align: center;
  color: #4f4f4f;
  margin: 0;
  margin-top: 9px;
}

.spinner {
  justify-content: center;
  padding-top: 100px;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background-color: rgb(79, 79, 79, 0.4);
  align-self: center;
  display: flex;
}
