import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { select, Store } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { BaseFormComponent } from 'src/app/schools-form/components';
import { ArrayFilter, UserCashless } from 'src/app/sharedModels';
import { UserManagementState } from 'src/app/states/user-management/user-management-state.interface';
import {
  UsersSearchValueSelector,
  UsersSearchResultsSelector,
  LoadingSelector,
} from 'src/app/states/user-management/user-management.selectors';
import {
  ResetSelectedUserData,
  SetUsersSearchValue,
} from 'src/app/states/user-management/user-management.actions';
import { PaginatorChange } from 'src/app/sharedModels/base/paginatorChange';

@Component({
  selector: 'admin-search-users',
  templateUrl: './search-users.component.html',
  styleUrls: ['./search-users.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchUsersComponent extends BaseFormComponent implements OnInit, OnDestroy {
  users: UserCashless[];
  searchValue: ArrayFilter;
  private searchSubscription: Subscription;
  private searchResultsSubscription: Subscription;
  private loadingSubscription: Subscription;
  showSpinner: boolean = true;

  constructor(private cd: ChangeDetectorRef, private store: Store<{ userManagement: UserManagementState }>) {
    super();
  }

  ngOnInit(): void {
    this._createForm();
    this.store.dispatch(ResetSelectedUserData());

    this.searchSubscription = this.store.pipe(select(UsersSearchValueSelector)).subscribe(search => {
      if (!search) {
        this.searchValue = new ArrayFilter();
        this.searchValue.Filter = '';
        this.searchValue.NumberRows = 100;
        this.searchValue.PageIndex = 0;

        this.store.dispatch(SetUsersSearchValue({ search: this.searchValue }));
      } else {
        this.searchValue = { ...search };
      }

      if (this.searchForm?.value != this.searchValue.Filter) {
        this.searchForm.setValue(this.searchValue.Filter);
      }
    });

    this.searchResultsSubscription = this.store
      .pipe(select(UsersSearchResultsSelector))
      .subscribe(results => {
        this.users = results;
        this.cd.markForCheck();
      });

    this.loadingSubscription = this.store.pipe(select(LoadingSelector)).subscribe((loading: boolean) => {
      this.showSpinner = loading;
    });
  }

  ngOnDestroy(): void {
    if (this.searchSubscription) {
      this.searchSubscription.unsubscribe();
    }

    if (this.searchResultsSubscription) {
      this.searchResultsSubscription.unsubscribe();
    }
    if (this.loadingSubscription) {
      this.loadingSubscription.unsubscribe();
    }
  }

  private _createForm() {
    this.formGroup = new FormGroup({
      search: new FormControl(''),
    });
  }

  pageChanged(page: PaginatorChange) {
    var newSearch = { ...this.searchValue };
    newSearch.PageIndex = page.pageIndex;

    if (page.numberRows != newSearch.NumberRows) {
      newSearch.PageIndex = 0;
    }
    newSearch.NumberRows = page.numberRows;

    this._triggerSearch(newSearch);
  }

  searchClicked() {
    var newSearch = { ...this.searchValue };
    newSearch.Filter = this.searchForm.value;

    this._triggerSearch(newSearch);
  }

  private _triggerSearch(searchFilters: ArrayFilter) {
    this.store.dispatch(SetUsersSearchValue({ search: searchFilters }));
  }

  get searchForm() {
    return this.formGroup.get('search');
  }
}
