import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import * as moment from 'moment';

// ngrx
import { select, Store } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { OrderError, SchoolRecord } from 'src/app/sharedModels/admin/dashboard';
import { DashboardState } from 'src/app/states/admin/dashboardFeature/dashboard-state.interface';
import { SetSelectedDate, RefreshDashboard } from 'src/app/states/admin/dashboardFeature/dashboard.actions';
import { dashboardComponentSelector } from 'src/app/states/admin/dashboardFeature/dashboard.selectors';

@Component({
  selector: 'engineer-dashboard',
  templateUrl: './engineer-dashboard.component.html',
  styleUrls: ['./engineer-dashboard.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EngineerDashboardComponent implements OnInit, OnD<PERSON>roy {
  form: FormGroup;
  totalOrders: number = 0;
  ordersSchool: SchoolRecord[];
  ordersWithError: OrderError[];
  ordersProcessing: OrderError[];
  private dashSubscription: Subscription;

  constructor(private store: Store<{ dashboard: DashboardState }>, private cd: ChangeDetectorRef) {}

  ngOnInit(): void {
    // setup form
    this.CreateForm();

    this.dashSubscription = this.store.pipe(select(dashboardComponentSelector)).subscribe(res => {
      if (res && res.schoolRecords && res.schoolRecords.length > 0) {
        this.totalOrders = res.schoolRecords.reduce((accumulator, current) => {
          return accumulator + current.orders;
        }, 0);

        this.ordersSchool = Object.assign([], res.schoolRecords);
      } else {
        this.ordersSchool = [];
        this.totalOrders = 0;
      }

      if (res && res.orderErrors && res.orderErrors.length > 0) {
        this.ordersWithError = res.orderErrors.filter(x => x.orderStatusId == 5);
        this.ordersProcessing = res.orderErrors.filter(x => x.orderStatusId == 3);
      } else {
        this.ordersWithError = [];
        this.ordersProcessing = [];
      }
      this.cd.markForCheck();
    });
  }

  ngOnDestroy(): void {
    if (this.dashSubscription) {
      this.dashSubscription.unsubscribe();
    }
  }

  CreateForm() {
    let defaultDate = moment().format('YYYY-MM-DD');

    this.form = new FormGroup({
      selectedDate: new FormControl(defaultDate),
    });

    // first dispatch
    this.store.dispatch(SetSelectedDate({ date: defaultDate }));

    // when date change
    this.selectedDate.valueChanges.subscribe(res => {
      let date = moment(this.selectedDate.value).format('YYYY-MM-DD');

      // dispatch everytime the value change
      this.store.dispatch(SetSelectedDate({ date: date }));
    });
  }

  RefreshDashboard() {
    this.store.dispatch(RefreshDashboard());
  }

  get selectedDate() {
    return this.form.get('selectedDate');
  }
}
