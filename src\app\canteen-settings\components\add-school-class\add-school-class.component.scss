@import '../../../../styles/cashless-theme.scss';

.buttonsWrapper {
  margin: 0;
  padding-top: 20px;
  padding-right: 10px;
  text-align: right;
}

.modalTitle {
  font-size: 30px;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 15px;
}

.button {
  background: $orange-3;
  color: #ffffff;
  font-style: normal;
  font-weight: bold;
  font-size: 16px;
  line-height: 18px;
  text-align: center;
  border-radius: 10px;
  padding-right: 22px;
  padding-left: 22px;
  height: 34px;
}

.button:disabled {
  background: #e0e0e0;
}

.cancelButton {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 18px;
  text-align: center;
  border: 1px solid$orange-3;
  border-radius: 10px;
  height: 34px;
  margin-right: 10px;
  color: #333b44;
}

input {
  background-color: #ffffff;
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  color: #000000;
}

input::placeholder {
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 20px;
  color: #dadada;
}

.image {
  width: 28px;
}

.emptyBlock {
  height: 64px;
}

.archiveDiv {
  text-align: right;
}

.archiveButton {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  padding-right: 22px;
  padding-left: 22px;
  width: 90px;
  height: 44px;
  text-align: center;
  background-color: #e0e0e0;
  color: red;
}
