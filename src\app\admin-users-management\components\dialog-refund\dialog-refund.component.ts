import { Component, Inject, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import {
  AdminFullRefundRequest,
  AdminRefundType,
  BaseComponent,
  EditOrderRequest,
  MakePaymentRequest,
  MenuItem,
  RefinedOrder,
  RefinedOrderItem,
  RefundDialogData,
} from 'src/app/sharedModels';
import { AdminService, OrderApiService } from 'src/app/sharedServices';
import * as _ from 'lodash';
import { GetOrderItemsPrice } from 'src/app/manage-order/functions/calculate-price';

interface RefundFormResult {
  refund: boolean;
  quantity: number;
}

@Component({
  selector: 'dialog-refund',
  templateUrl: './dialog-refund.component.html',
  styleUrls: ['./dialog-refund.component.scss'],
})
export class DialogRefundComponent extends BaseComponent implements OnInit {
  form: FormGroup;
  orderItemsForm: FormGroup;
  selectedRefundType: number = AdminRefundType.FullRefund;
  AdminRefundType = AdminRefundType;
  originalOrder: RefinedOrder;
  refundAmount: number = 0;
  buttonLoading: boolean = false;

  showError: boolean = false;
  selectItemsErrorMessage: boolean = false;
  partialRefundErrorMessage: string;

  API_ERROR_MESSAGE: string = 'Order could not be refunded. Please try again.';
  NO_ITEMS_SELECTED_ERROR_MESSAGE: string = 'Please select an order item to complete a partial refund';
  ALL_ITEMS_SELECTED_ERROR_MESSAGE: string = 'Cannot complete a partial refund with all order items selected';

  constructor(
    public dialogRef: MatDialogRef<DialogRefundComponent>,
    @Inject(MAT_DIALOG_DATA) public data: RefundDialogData,
    private formBuilder: FormBuilder,
    private orderApiService: OrderApiService,
    private adminService: AdminService
  ) {
    super();
  }

  ngOnInit() {
    this.originalOrder = _.cloneDeep(this.data.order);
    this.createForm();
  }

  createForm() {
    this.form = new FormGroup({
      refundType: new FormControl(this.selectedRefundType, [Validators.required]),
      description: new FormControl(null, [Validators.required, Validators.maxLength(30)]),
    });
    this.generateOrderItemsForm();
  }

  generateOrderItemsForm() {
    //generate dynamic order item form
    this.orderItemsForm = this.formBuilder.group({
      orderItems: new FormArray([]),
      orderItemsQuantity: new FormArray([]),
    });

    // Create form control for order item checkbox value
    this.data.order.Items.forEach(item => {
      this.orderItemsFormArray.push(new FormControl(false));
      this.orderItemsQuantityFormArray.push(new FormControl(item.Quantity));
    });
  }

  GetPriceItem(
    index: number,
    useQuantity: boolean = false,
    menuItemList: RefinedOrderItem[] = this.originalOrder.Items
  ): number {
    const quantity = useQuantity ? this.orderItemsQuantityFormArray.value[index] : 1;
    return GetOrderItemsPrice([menuItemList[index]], true) * quantity;
  }

  getRefundPriceToDisplay(
    menuItemList: RefinedOrderItem[] = this.originalOrder.Items,
    orderPrice: number = this.originalOrder.Price
  ): number {
    if (this.refundType.value === AdminRefundType.FullRefund) {
      return orderPrice + this.originalOrder.OrderFee;
    }

    let selectedItemsPrice = this.orderItemsFormArray.value.reduce(
      (accumulator: number, value: boolean, index: number) => {
        const addValue = value ? this.GetPriceItem(index, true, menuItemList) : 0;
        return (accumulator += addValue);
      },
      0
    );
    return selectedItemsPrice;
  }

  getNewOrderPrice(menuItems: RefinedOrderItem[]) {
    return menuItems.reduce((accumulator: number, value: RefinedOrderItem, index: number) => {
      let temp = Object.assign(new MenuItem(), menuItems[index]);
      const addValue = temp.GetPriceItemWithOption() * temp.Quantity;
      return (accumulator += addValue);
    }, 0);
  }

  isFormValid() {
    this.partialRefundErrorMessage = '';
    if (this.refundType.value === this.AdminRefundType.PartialRefund) {
      if (this.noOrderItemsSelected()) {
        this.setPartialOrderErrorMessage(this.NO_ITEMS_SELECTED_ERROR_MESSAGE);
      }
      if (this.AllPartialRefundItemsSelected()) {
        this.setPartialOrderErrorMessage(this.ALL_ITEMS_SELECTED_ERROR_MESSAGE);
      }
    }
    return this.partialRefundErrorMessage === '' && this.form.valid;
  }

  setPartialOrderErrorMessage(message: string): void {
    this.selectItemsErrorMessage = true;
    this.partialRefundErrorMessage = message;
  }

  closeModal(result: boolean = false): void {
    this.dialogRef.close(result);
  }

  confirmClick() {
    if (this.buttonLoading) {
      return;
    }
    if (!this.isFormValid()) {
      this.form.markAllAsTouched();
      return;
    }
    this.showError = false;
    this.buttonLoading = true;

    if (this.refundType.value === AdminRefundType.FullRefund) {
      this.fullRefund();
      return;
    }
    this.partialRefund();
  }

  getPaymentRequest(price: number) {
    let parent = this.adminService.GetParent();
    let makePaymentRequest = new MakePaymentRequest();
    makePaymentRequest.nonce = 'null';
    makePaymentRequest.chargeAmount = price;
    makePaymentRequest.userId = parent.UserId;
    makePaymentRequest.toExternalCustomerId = parent.ExternalUserId;
    makePaymentRequest.canteenId = this.data.order.CanteenId;
    makePaymentRequest.message = this.description.value;

    return makePaymentRequest;
  }

  /** Edit order json to remove refunded items  */
  getUpdateOrderJSON(): RefinedOrderItem[] {
    let formResults: RefundFormResult[] = this.orderItemsFormArray.value.map((val: boolean, i: number) => ({
      refund: val,
      quantity: this.orderItemsQuantityFormArray.value[i],
    }));
    const orderItems = _.cloneDeep(this.originalOrder.Items);
    const updatedQuantities = this.updateMenuItemQuantity(orderItems, formResults);
    const removeItemsToRefund = updatedQuantities.filter((menuItem: RefinedOrderItem, i: number) =>
      this.removeItemFromOrder(i, formResults)
    );

    return removeItemsToRefund;
  }

  updateMenuItemQuantity(orderItems: RefinedOrderItem[], formResults: RefundFormResult[]) {
    return orderItems.map((menuItem: RefinedOrderItem, i: number) => {
      if (formResults[i].refund && menuItem.Quantity !== formResults[i].quantity) {
        menuItem.Quantity -= formResults[i].quantity;
        formResults[i].refund = false;
      }
      return menuItem;
    });
  }

  removeItemFromOrder(index: number, formResults: RefundFormResult[]): boolean {
    return !formResults[index].refund;
  }

  noOrderItemsSelected(): boolean {
    return !this.orderItemsFormArray.value.includes(true);
  }

  /** Check if all selected items are selected in a partial refund */
  AllPartialRefundItemsSelected(): boolean {
    const allItemsSelected = this.orderItemsFormArray.value.every((x: boolean) => x === true);
    const maxItemQuantitySelected = this.orderItemsQuantityFormArray.value.every(
      (x: number, index: number) => this.originalOrder.Items[index].Quantity === x
    );
    return allItemsSelected && maxItemQuantitySelected;
  }

  apiSuccessResponse(): void {
    this.closeModal(true);
    this.buttonLoading = false;
  }

  apiErrorResponse(error): void {
    this.buttonLoading = false;
    this.showError = true;
    this.handleErrorFromService(error);
  }
  ////////////////////
  // API CALLS
  ////////////////////

  fullRefund(): void {
    const request: AdminFullRefundRequest = {
      orderId: this.data.order.OrderId,
      message: this.description.value,
    };

    this.orderApiService.adminRefundFullOrder(request).subscribe({
      next: response => {
        this.apiSuccessResponse();
      },
      error: error => {
        this.apiErrorResponse(error);
      },
    });
  }

  partialRefund(): void {
    let orderData = _.cloneDeep(this.originalOrder);
    orderData.Items = this.getUpdateOrderJSON();

    const request: EditOrderRequest = {
      OrderId: orderData.OrderId,
      Items: orderData.Items.map((item: RefinedOrderItem) => {
        return {
          MenuItemId: item.MenuItemId,
          Quantity: item.Quantity,
          MenuItemOptionIds: item.SelectedOptions.map(option => option.MenuItemOptionId),
        };
      }),
    };

    this.orderApiService.adminPartialRefundOrder(request).subscribe({
      next: response => {
        this.apiSuccessResponse();
      },
      error: error => {
        this.apiErrorResponse(error);
      },
    });
  }

  get orderItemsFormArray() {
    return this.orderItemsForm.get('orderItems') as FormArray;
  }

  get orderItemsQuantityFormArray() {
    return this.orderItemsForm.get('orderItemsQuantity') as FormArray;
  }

  get refundType() {
    return this.form.get('refundType');
  }
  get description() {
    return this.form.get('description');
  }
}
