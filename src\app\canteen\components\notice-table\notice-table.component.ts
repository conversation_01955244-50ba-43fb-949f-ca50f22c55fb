import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

//Models
import { Notice, NoticeStatusEnum, NoticeType } from 'src/app/sharedModels';

@Component({
  selector: 'notice-table',
  templateUrl: './notice-table.component.html',
  styleUrls: ['./notice-table.component.scss'],
})
export class NoticeTableComponent implements OnInit {
  @Input() tableData: Notice[];
  @Input() type: NoticeType;
  @Output() selectRow: EventEmitter<Notice> = new EventEmitter();

  displayedColumns: string[] = ['title', 'body', 'IsActive', 'Status'];
  NoticeStatusEnum = NoticeStatusEnum;

  constructor() {}

  ngOnInit(): void {}

  noticeRowClick(event: Notice) {
    this.selectRow.emit(event);
  }
}
