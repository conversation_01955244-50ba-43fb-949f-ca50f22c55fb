import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  Output,
  EventEmitter,
} from '@angular/core';
import * as _ from 'lodash';

//dialog imports
import { MatDialog } from '@angular/material/dialog';
import { DialogResultV2Component } from 'src/app/shared/components/';
import { DialogCancelOrderComponent } from 'src/app/shared/components/';

// models
import {
  BasePaginatorComponent,
  Order,
  MenuTypeEnum,
  OrderStatusEnum,
  ResultDialogData,
  UserCashless,
  PaginatorChange,
  ConfirmModal,
  RefundDialogData,
  RefinedOrder,
} from 'src/app/sharedModels';

// Services
import {
  SpinnerService,
  OrderApiService,
  AdminService,
  MenuCustomNameService,
  FeatureFlagService,
} from '../../../sharedServices';

// Ngrx
import { CreateOrderService } from 'src/app/sharedServices/order/create-order.service';
import { ConvertStringToDate, FormatDateToWords, TransformDateFromUtcToLocal } from 'src/app/utility';
import { DialogRefundComponent } from '../dialog-refund/dialog-refund.component';
import { FeatureFlags } from 'src/constants';
import {
  GetOrderStatusDialogData,
  GetParent,
  ProcessOrderDataForTable,
} from 'src/app/utility/order-table-helper';

const _adminColumns = [
  'id',
  'child',
  'orderDate',
  'dateCreated',
  'menuType',
  'menuName',
  'price',
  'items',
  'pickedUp',
  'refunded',
  'status',
  'message',
  'actions',
];

@Component({
  selector: 'user-management-orders-table',
  templateUrl: './user-orders-table.component.html',
  styleUrls: ['./user-orders-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserOrdersTableComponent
  extends BasePaginatorComponent<RefinedOrder>
  implements OnInit, OnChanges
{
  @Input() data: RefinedOrder[];
  @Input() selectedUser: UserCashless;
  @Input() selectedPage: number = 0;
  @Input() numberRows: number = 25;
  @Output() requestData: EventEmitter<PaginatorChange> = new EventEmitter();
  orderStatus = OrderStatusEnum;
  useNewRefundFlow: boolean;

  constructor(
    private spinnerService: SpinnerService,
    private orderService: OrderApiService,
    public dialog: MatDialog,
    private adminService: AdminService,
    private menuCustomNameService: MenuCustomNameService,
    private createOrderService: CreateOrderService,
    private featureFlagService: FeatureFlagService
  ) {
    super(_adminColumns);
  }

  ngOnInit(): void {
    this.featureFlagService.getFlag(FeatureFlags.useUpdatedAdminRefundFlow, false).then(res => {
      this.useNewRefundFlow = res;
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    for (const propName in changes) {
      switch (propName) {
        case 'data':
          this.updateTableData(this.data);
          break;

        default:
          break;
      }
    }
  }

  updateTableData(orders: RefinedOrder[]): void {
    const orderData = ProcessOrderDataForTable(orders);
    this.totalRows = orderData.totalRows;
    this.dataSource.data = orderData.orderList;
  }

  ////////// Actions Orders
  ConfirmRefund(orderData: RefinedOrder): void {
    this.openRefundDialog(orderData);
  }

  openRefundDialog(orderData: RefinedOrder): void {
    this.setUpParentInAdminService();

    const data: RefundDialogData = {
      title: `${orderData.StudentName} ${orderData.MenuName} Order, ${FormatDateToWords(
        orderData.OrderDate
      )} (Order Id ${orderData.OrderId})`,
      order: orderData,
    };

    const dialogRef = this.dialog.open(DialogRefundComponent, {
      width: '500px',
      disableClose: false,
      data: data,
    });

    dialogRef.afterClosed().subscribe(successfulRefund => {
      if (successfulRefund) {
        this.requestData.emit(null);
      }
    });
  }

  ClickValid(orderId: number): void {
    if (orderId) {
      this.orderService.ValidOrder(orderId).subscribe({
        next: (res: any) => {
          this.requestData.emit(null);
        },
        error: error => {
          this.spinnerService.stop();
          this.handleErrorFromService(error);
        },
      });
    }
  }

  setUpParentInAdminService(): void {
    const parent = GetParent(this.selectedUser);
    this.adminService.SetParent(parent);
  }

  CancelOrder(order: RefinedOrder): void {
    this.setUpParentInAdminService();

    const dialogRef = this.dialog.open(DialogCancelOrderComponent, {
      width: '500px',
      disableClose: true,
      data: order,
    });

    dialogRef.afterClosed().subscribe((isAdminOrCanteen: boolean) => {
      if (isAdminOrCanteen) {
        this.requestData.emit(null);
      }
    });
  }

  EditOrder(order: RefinedOrder): void {
    const { parent, student } = this.createOrderService.getParentAndStudent(
      this.selectedUser,
      order.StudentId
    );

    this.createOrderService.getAndSetDayDetail(
      order.MenuType,
      order.MenuName,
      order.MenuId,
      ConvertStringToDate(order.OrderDate),
      student,
      null, //cut off time not needed by canteen/admin
      order
    );
    this.createOrderService.adminMerchantCreateOrder(true, student, parent);
  }

  GetTextChange(order: Order) {
    const menuType = order.MenuType == MenuTypeEnum.Recess ? MenuTypeEnum.Lunch : MenuTypeEnum.Recess;
    const menuName = this.menuCustomNameService.GetMerchantMenuName(menuType);
    return `Change to ${menuName}`;
  }

  ChangeMenuType(order: Order): void {
    this.spinnerService.start();
    this.orderService.ChangeMenuTypeOrder(order).subscribe({
      next: () => {
        this.spinnerService.stop();
        this.requestData.emit(null);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  getLocalDateTime(dateTimeString: string): string {
    return TransformDateFromUtcToLocal(dateTimeString);
  }

  clickPickedUp(e, selectedElement: Order): void {
    e.preventDefault();

    if (selectedElement.PickedUp) {
      this.RemovePickUpTime(selectedElement);
    } else {
      this.submitPickupChange(selectedElement);
    }
  }

  submitPickupChange(selectedElement: Order): void {
    this.spinnerService.start();

    this.orderService.GetOrderPickedUpStatus(selectedElement.OrderId, !selectedElement.PickedUp).subscribe({
      next: (res: string) => {
        this.requestData.emit(null);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  RemovePickUpTime(selectedElement: Order): void {
    let data: ResultDialogData = GetOrderStatusDialogData(selectedElement.PickedUpDateUtc);

    const dialogRef = this.dialog.open(DialogResultV2Component, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.submitPickupChange(selectedElement);
      }
    });
  }

  pageChangedEvent(page: PaginatorChange): void {
    this.requestData.emit(page);
  }
}
