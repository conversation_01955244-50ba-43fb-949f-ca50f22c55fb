import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { BaseFormComponent } from 'src/app/schools-form/components';

// component
import { DialogConfirmComponent } from 'src/app/shared/components';

// models
import { ConfirmModal, Roles, UserCashless } from 'src/app/sharedModels';

// services
import { SpinnerService, StudentService } from 'src/app/sharedServices';

//dialog
import { MatDialog } from '@angular/material/dialog';
import { DialogUserDetailsFormComponent } from '../dialog-user-details-form/dialog-user-details-form.component';

@Component({
  selector: 'user-management-child-details',
  templateUrl: './child-details.component.html',
  styleUrls: ['./child-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChildDetailsComponent extends BaseFormComponent implements OnInit {
  @Input() child: UserCashless;

  constructor(
    private spinnerService: SpinnerService,
    private studentService: StudentService,
    public dialog: MatDialog
  ) {
    super();
  }

  ngOnInit(): void {}

  ////////////////////////// Archive child
  canArchiveChild() {
    return this.child.Role == Roles.Child && this.child.IsActive;
  }

  archiveClicked() {
    let data = new ConfirmModal();
    data.Title = 'Archive Child';
    data.Text =
      'Archiving will inactivate the child profile permanently and cannot be undone. Do you want to proceed?';
    data.CancelButton = 'No';
    data.ConfirmButton = 'Yes';

    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.archiveClickConfirmed();
      }
    });
  }

  private archiveClickConfirmed() {
    this.spinnerService.start();

    this.studentService.ArchiveStudentAPI(this.child.UserId).subscribe({
      next: (response: any) => {
        window.location.reload();
      },
      error: error => {
        this.spinnerService.stop();
        this.ErrorModal('Archive Student', error);
      },
    });
  }

  editUser() {
    this.dialog.open(DialogUserDetailsFormComponent, {
      width: '500px',
      disableClose: false,
      data: this.child,
    });
  }
}
