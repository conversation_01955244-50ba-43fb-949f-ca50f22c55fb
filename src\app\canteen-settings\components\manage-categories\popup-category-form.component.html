<mat-dialog-content>
  <modal-header [title]="title" (close)="closeDialog(false)"></modal-header>

  <div class="row">
    <div class="col-6">
      <form [formGroup]="form" class="cashlessForm">
        <mat-form-field appearance="outline">
          <mat-label>Category</mat-label>
          <input matInput placeholder="Enter category name" formControlName="name" type="text" />
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Display Order</mat-label>
          <input matInput formControlName="sortOrder" type="number" min="1" />
        </mat-form-field>
      </form>
    </div>
  </div>

  <div class="row">
    <div
      *ngFor="let name of listImages"
      class="imageBlock"
      [ngClass]="{ selected: isSelected(name) }"
      (click)="imageSelection(name)"
    >
      <category-icon [iconName]="name"></category-icon>
    </div>
  </div>

  <div class="row pt-4">
    <div class="col-12">
      <basic-form-buttons
        (saveEvent)="onSubmit()"
        (cancelEvent)="closeDialog(false)"
        [disableSaveButton]="disableSave()"
        [loading]="buttonLoading"
      ></basic-form-buttons>
    </div>
  </div>
</mat-dialog-content>
