@import '../../../../styles/cashless-theme.scss';

.backButton {
  color: $orange-3;
}

.header {
  margin-bottom: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    font-size: 28px;
    color: $grey-12;
    text-align: center;
    height: 100%;
    margin: 0;
  }
}

.user-card {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 8px;
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  padding-left: 10px;
}

.mat-mdc-form-field {
  width: 100%;
}

.user-details {
  padding: 10px;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

li {
  padding: 6px;
  font-size: 14px;
  margin: 0;
  color: #bbbbbb;
}

.editBtn {
  background-color: transparent;
  border: 0;
  outline: 0;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 20px;
}

.merchant-btn {
  background-color: $grey-14;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  outline: none;
  border: none;
  color: #ffffff;
  font-size: 18px;
  line-height: 24px;
  padding: 6px 16px;
  border-radius: 14px;
  font-weight: 700;
  cursor: pointer;

  img {
    width: 16px;
    height: 16px;
  }
}

.merchant-btn:disabled {
  background-color: $grey-7;
  cursor: default;
}

.relative-section {
  position: relative;
}

.radioButtons {
  display: flex;
  gap: 15px;
}

/* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
.radioButtons ::ng-deep .mat-mdc-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element {
  background-color: $orange-3;
}

/* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
.radioButtons ::ng-deep .mat-mdc-radio-button.mat-accent .mat-radio-inner-circle {
  background-color: $orange-3;
}

/* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
.radioButtons ::ng-deep.mat-mdc-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border-color: $orange-3;
}

#type-label {
  font-size: 12px;
  color: $grey-11;
  padding-bottom: 10px;
}

/* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
mat-radio-group {
  padding-top: 8px;
  padding-bottom: 8px;
}

.details-divider {
  border: none;
  height: 0.1px;
  background-color: #989494;
  margin: 0 0 5px 0;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.read-only-wrapper
  ::ng-deep
  .mat-mdc-form-field
  .mat-form-field-wrapper
  .mat-form-field-flex
  .mat-form-field-infix
  .mat-input-element {
  color: $grey-17;
}
