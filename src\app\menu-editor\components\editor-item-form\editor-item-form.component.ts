import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CommonModule, KeyValue } from '@angular/common';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';

// Service
import { SchoolEventApiService, SchoolEventManagerService, SpinnerService } from 'src/app/sharedServices';

// Models
import {
  BaseComponent,
  MenuItem,
  Canteen,
  Days,
  MenuTypeEnum,
  NutritionalValue,
  WeekEndLongForm,
  CategoryEditor,
} from 'src/app/sharedModels';
import {
  convertEnumToKeyValue,
  getDietaryKeyValue,
  getWeekDayKeyValue,
} from 'src/app/sharedModels/base/KeyValueConversion';

// ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { CanteenState } from '../../../states';
import { canteenStateSelector } from '../../../states/canteen/canteen.selectors';
import { SchoolsFormModule } from "../../../schools-form/schools-form.module";
import { MenuEditorModule } from '../../menu-editor.module';
import { SchoolsButtonModule } from 'src/app/schools-button/schools-button.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatNativeDateModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';


@Component({
  selector: 'editor-item-form',
  templateUrl: './editor-item-form.component.html',
  styleUrls: ['./editor-item-form.component.scss'],
  standalone: true,
  imports: [
    SchoolsFormModule,
    MenuEditorModule,
    CommonModule,
    SchoolsButtonModule,
    MatFormFieldModule,
    MatNativeDateModule,
    ReactiveFormsModule,
    FormsModule,
    MatInputModule,
    MatDatepickerModule,
    MatSelectModule,
    MatCheckboxModule,
]
})
export class EditorItemFormComponent extends BaseComponent implements OnInit, OnDestroy 
{
  @Output() saveItem = new EventEmitter<MenuItem>();
  private subscription: Subscription;
  private _currentSelectedCanteen: Canteen;
  item: MenuItem;
  form: FormGroup;
  merchantId: number;
  daysHelper: Days;
  selectedNutritional: NutritionalValue = new NutritionalValue();
  nutritionalValues: NutritionalValue[] = [];
  merchantCategories: KeyValue<string, string>[] = [];

  dietaryCheckBoxValues: KeyValue<string, string>[] = getDietaryKeyValue();

  constructor(
    private store: Store<{ canteen: CanteenState }>
  ) {
    super();
  }

  ngOnInit() {
    this.subscription = this.store.pipe(select(canteenStateSelector)).subscribe((state: CanteenState) => {
      if (state.selected) {
        this._currentSelectedCanteen = state.selected;
        this.merchantId = this._currentSelectedCanteen.CanteenId;
        this.getCategoryData(state.menuCategories);

        this.item = new MenuItem();
        this._createForm();
      }

      if (state.selected && state.nutritionalValues) {
        this.nutritionalValues = state.nutritionalValues;

        if (this.item.NutritionalValue) {
          this.selectedNutritional = this.nutritionalValues.find(n => n.Name == this.item.NutritionalValue);

          if (!this.selectedNutritional) {
            this.selectedNutritional = new NutritionalValue();
          }
        }
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  getCategoryData(categories: CategoryEditor[]) {
    if (!categories) {
      return;
    }
    categories.forEach(category =>
      this.merchantCategories.push({ key: category.MenuCategoryId.toString(), value: category.CategoryName })
    );
  }

  IsUniformCanteen() {
    return this._currentSelectedCanteen && this._currentSelectedCanteen.CanteenType == MenuTypeEnum.Uniform;
  }

  ///////////////////////////////////
  // Form
  ///////////////////////////////////

  get name() {
    return this.form.get('name');
  }
  get categoryData() {
    return this.form.get('categoryData');
  }
  get description() {
    return this.form.get('description');
  }
  get sortOrder() {
    return this.form.get('sortOrder');
  }
  get price() {
    return this.form.get('price');
  }
  get available() {
    return this.form.get('available');
  }
  get hasGst() {
    return this.form.get('hasGst');
  }
  get hasCutOffTime() {
    return this.form.get('hasCutOffTime');
  }
  get cutOffTimeType() {
    return this.form.get('cutOffTimeType');
  }
  get cutOffTime() {
    return this.form.get('cutOffTime');
  }
  get Vegetarian() {
    return this.form.get('Vegetarian');
  }
  get Vegan() {
    return this.form.get('Vegan');
  }
  get GlutenFree() {
    return this.form.get('GlutenFree');
  }
  get Halal() {
    return this.form.get('Halal');
  }
  get LactoseFree() {
    return this.form.get('LactoseFree');
  }
  get NutsFree() {
    return this.form.get('NutsFree');
  }
  get FastingFriendly() {
    return this.form.get('FastingFriendly');
  }
  get DairyFree() {
    return this.form.get('DairyFree');
  }

  get Monday() {
    return this.form.get('Monday');
  }
  get Tuesday() {
    return this.form.get('Tuesday');
  }
  get Wednesday() {
    return this.form.get('Wednesday');
  }
  get Thursday() {
    return this.form.get('Thursday');
  }
  get Friday() {
    return this.form.get('Friday');
  }
  get Saturday() {
    return this.form.get('Saturday');
  }
  get Sunday() {
    return this.form.get('Sunday');
  }

  private _createForm() {
    if (!this.item || !this.item.MenuItemId) {
      this.item = new MenuItem();
      this.item.IsActive = true;
      this.item.Images = [];
      this.item.AvailabilityDays = 'M,T,W,Th,F,S,Su';
    }

    this.daysHelper = new Days(this.item.AvailabilityDays);
    const categoryKeyValue = this.merchantCategories.find(x => parseInt(x.key) === this.item.CategoryId);
    const selectedCategoryId = categoryKeyValue?.key ? categoryKeyValue.key : null;

    this.form = new FormGroup({
      name: new FormControl(this.item.Name, [Validators.required]),
      categoryData: new FormControl(selectedCategoryId, [Validators.required]),
      description: new FormControl(this.item.Desc),
      sortOrder: new FormControl(this.item.SortOrder, [Validators.min(1)]),
      price: new FormControl(this.item.Price, [Validators.required]),
      available: new FormControl(this.item.IsActive),
      hasGst: new FormControl(this.item.IsGstApplied),

      Vegetarian: new FormControl(this.item.IsVeg),
      Vegan: new FormControl(this.item.IsVegan),
      GlutenFree: new FormControl(this.item.IsGF),
      Halal: new FormControl(this.item.IsHalal),
      LactoseFree: new FormControl(this.item.IsLactoseFree),
      NutsFree: new FormControl(this.item.IsNutsFree),
      FastingFriendly: new FormControl(this.item.IsFastingFriendly),
      DairyFree: new FormControl(this.item.IsDairyFree),

      Monday: new FormControl(this.daysHelper.IsAvailableMonday()),
      Tuesday: new FormControl(this.daysHelper.IsAvailableTuesday()),
      Wednesday: new FormControl(this.daysHelper.IsAvailableWednesday()),
      Thursday: new FormControl(this.daysHelper.IsAvailableThursday()),
      Friday: new FormControl(this.daysHelper.IsAvailableFriday()),
      Saturday: new FormControl(this.daysHelper.IsAvailableSaturday()),
      Sunday: new FormControl(this.daysHelper.IsAvailableSunday()),

      itemStartDate: new FormControl(),
      itemEndDate: new FormControl(),
    });
  }

  onSubmit() {
    if (this.form.invalid) {
      return;
    }

    this._convertToItemObject(); 

    this.saveItem.emit(this.item);
  }

  /** Put the values of the form into the MenuItem */
  private _convertToItemObject(): void {
    this.item.Name = this.name.value;
    this.item.CanteenId = this._currentSelectedCanteen.CanteenId;
    this.item.CategoryId = this.categoryData.value;

    this.item.Desc = this.description.value;
    this.item.SortOrder = this.sortOrder.value;
    this.item.Price = this.price.value;
    this.item.IsActive = this.available.value;
    this.item.IsGstApplied = this.hasGst.value;
    this.item.Availabilities = []

    if (this.IsUniformCanteen()) {
      return;
    }

    this.processCutOffTime();
    this.processDietaryLabels();
    this.processNutritionalInformation();
    this.processItemWeekdayAvailability();
  }

  processDietaryLabels() {
    this.item.IsVeg = this.Vegetarian.value;
    this.item.IsVegan = this.Vegan.value;
    this.item.IsGF = this.GlutenFree.value;
    this.item.IsHalal = this.Halal.value;
    this.item.IsLactoseFree = this.LactoseFree.value;
    this.item.IsNutsFree = this.NutsFree.value;
    this.item.IsFastingFriendly = this.FastingFriendly.value;
    this.item.IsDairyFree = this.DairyFree.value;
  }

  processCutOffTime() {
    if (this.hasCutOffTime.value) {
      this.item.CutOffTime = +this.cutOffTime.value;
      this.item.CutOffTimeType = this.cutOffTimeType.value;
    } else {
      this.item.CutOffTime = null;
      this.item.CutOffTimeType = null;
    }
  }

  processNutritionalInformation() {
    this.item.NutritionalValue = this.selectedNutritional.Name;
    this.item.NutritionalColor = this.selectedNutritional.Color;
  }

  processItemWeekdayAvailability() {
    let availableDayString = '';

    if (this.Monday.value == true) {
      availableDayString += 'M,';
    }

    if (this.Tuesday.value == true) {
      availableDayString += 'T,';
    }

    if (this.Wednesday.value == true) {
      availableDayString += 'W,';
    }

    if (this.Thursday.value == true) {
      availableDayString += 'Th,';
    }

    if (this.Friday.value == true) {
      availableDayString += 'F,';
    }
    if (this.Saturday.value == true) {
      availableDayString += 'S,';
    }
    if (this.Sunday.value == true) {
      availableDayString += 'Su,';
    }

    if (availableDayString.length > 0) {
      this.item.AvailabilityDays = availableDayString.substring(0, availableDayString.length - 1);
    } else {
      this.item.AvailabilityDays = '';
    }
  }

  ///////////////////////////////////
  // view functions
  ///////////////////////////////////
  getErrorMessage() {
    return 'Required';
  }

  GetTextSubmitButton() {
    return this.item.MenuItemId ? 'Save' : 'Add';
  }

  onNutritionalSelect(item: NutritionalValue) {
    if (item.Name === this.selectedNutritional.Name) {
      this.selectedNutritional = new NutritionalValue();
    } else {
      this.selectedNutritional = item;
    }
  }
}
