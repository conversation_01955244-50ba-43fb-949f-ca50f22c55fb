<div class="justify-content-center">
  <div class="container">
    <form [formGroup]="form" (ngSubmit)="onSubmit()" class="cashlessForm">
      <mat-form-field *ngIf="!loginMode" appearance="outline">
        <mat-label>First name</mat-label>
        <input matInput placeholder="First name" formControlName="firstname" type="text" required />
        <mat-error *ngIf="firstname.invalid">{{ getErrorMessageFirstname() }}</mat-error>
      </mat-form-field>
      <mat-form-field *ngIf="!loginMode" appearance="outline">
        <mat-label>Last name</mat-label>
        <input matInput placeholder="Last name" formControlName="lastname" type="text" required />
        <mat-error *ngIf="lastname.invalid">{{ getErrorMessageLastname() }}</mat-error>
      </mat-form-field>
      <mat-form-field *ngIf="!loginMode" appearance="outline">
        <mat-label>Mobile number</mat-label>
        <input
          matInput
          placeholder="Mobile number"
          formControlName="mobile"
          type="text"
          (keyup)="formatMobileInput()"
          required
        />
        <mat-error *ngIf="mobile.invalid">{{ getErrorMessageMobile() }}</mat-error>
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label>Email</mat-label>
        <input matInput placeholder="Email" formControlName="email" type="email" required />
        <mat-error *ngIf="email.invalid">{{ getErrorMessageEmail() }}</mat-error>
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label>Password</mat-label>
        <input matInput placeholder="Password" formControlName="password" type="password" required />
        <mat-error *ngIf="password.invalid">{{ getErrorMessagePassword() }}</mat-error>
      </mat-form-field>
      <div *ngIf="!loginMode" class="tcWrapper">
        <div class="subtext" matPrefix>
          <mat-checkbox formControlName="termsConditions"></mat-checkbox>
          <span>
            I agree to the Spriggy Schools
            <a target="_blank" [href]="termsLink">Terms & Conditions</a>,
            <a target="_blank" [href]="termsLink">Privacy Policy</a>
            and
            <a target="_blank" [href]="termsLink">Financial Services Guide</a>.
          </span>
        </div>
        <div class="subtext" matPrefix>
          <mat-checkbox formControlName="ageConditions"></mat-checkbox>
          <span>
            I agree that I am 18 years of age or older, or that I am between 15 and 18 years of age and my
            parent or legal guardian authorises me entering into the
            <a target="_blank" [href]="termsLink">Terms & Conditions</a>.
          </span>
        </div>
        <mat-error *ngIf="termsConditions.invalid || ageConditions.invalid">{{
          getErrorMessageTerms()
        }}</mat-error>
      </div>

      <button [@bounce]="bounce" class="submitButton" type="submit">{{ GetTextSubmit() }}</button>

      <div *ngIf="loginMode" class="cashlessLink forgotPwd">
        <a class="resetLink" routerLink="/reset">Forgot password?</a>
      </div>

      <mat-error *ngIf="errorMessage">
        <br />
        {{ errorMessage }}
      </mat-error>
    </form>
  </div>
</div>

<div class="containerBottom">
  <div class="cashlessLink bottomLink">
    <span class="registerLabel" *ngIf="!loginMode"
      >Already have an account?
      <p><a routerLink="/login">Log in</a></p></span
    >
    <span class="registerLabel" *ngIf="loginMode">No account? Register today! </span>
    <button [@bounce]="bounce" *ngIf="loginMode" routerLink="/register" class="register">Register</button>
  </div>
</div>
<!-- <div *ngIf="isOffline" class="justify-content-center">
    <p class="offlineMode">
        Offline Mode. Please connect to the internet to continue.
    </p>
</div> -->
