<table
  mat-table
  [dataSource]="dataSource"
  matSort
  (matSortChange)="sortData($event)"
  class="mat-elevation-z8 tableau classesTable"
>
  <ng-container matColumnDef="name">
    <th mat-sort-header mat-header-cell *matHeaderCellDef class="title">Class Name</th>
    <td mat-cell *matCellDef="let element">{{ element.Name }}</td>
  </ng-container>

  <ng-container matColumnDef="teacher">
    <th mat-sort-header mat-header-cell *matHeaderCellDef class="title">Teacher</th>
    <td mat-cell *matCellDef="let element">{{ element.Teacher }}</td>
  </ng-container>

  <ng-container matColumnDef="Actions">
    <th mat-sort-header mat-header-cell *matHeaderCellDef class="title"></th>
    <td mat-cell *matCellDef="let element" class="actions">
      <mat-icon matTooltip="Delete" (click)="ArchiveClicked(element)">delete_outline</mat-icon>
      <mat-icon matTooltip="Edit" (click)="onEdit(element)">mode_edit</mat-icon>
    </td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns" class="rowElement"></tr>
</table>
