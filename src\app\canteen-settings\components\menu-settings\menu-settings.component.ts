import { Component } from '@angular/core';
import { Location } from '@angular/common';

//Models
import { Canteen, BaseComponent, School, ListMenu, SchoolPrintSettings, MerchantTypeEnum } from 'src/app/sharedModels';

//Services
import { SpinnerService, SchoolService } from 'src/app/sharedServices';

@Component({
  selector: 'menu-settings',
  templateUrl: './menu-settings.component.html',
  styleUrls: ['./menu-settings.component.scss'],
})
export class MenuSettingsComponent extends BaseComponent {
  listCanteens: Canteen[] = [];
  selectedSchool: School = new School();
  selectedMerchant: Canteen = new Canteen();
  canteenListVisible: boolean = true;
  menuList: ListMenu;
  inactiveMenuList: ListMenu;
  isEventMerchant: boolean;
  //isUniformMerchant: boolean;
  isCanteenMerchant: boolean;
  printSettings: SchoolPrintSettings;

  constructor(
    private _location: Location,
    private spinnerService: SpinnerService,
    private schoolService: SchoolService
  ) {
    super();
  }

  GoBackClick() {
    this._location.back();
  }

  onMerchantSelect(merchant: Canteen) {
    this.selectedMerchant = merchant;
    this.isEventMerchant = this.selectedMerchant.CanteenType === MerchantTypeEnum.Event;
    //this.isUniformMerchant = this.selectedMerchant.CanteenType === MerchantTypeEnum.Uniform;
    this.isCanteenMerchant = this.selectedMerchant.CanteenType === MerchantTypeEnum.Canteen;
  }

  onSchoolSelect(schoolId: number) {
    this.spinnerService.start();

    this.schoolService.GetSchoolByIdAPI(schoolId).subscribe(school => {
      this.selectedSchool = school;
      this.getPrintSettings(school);
      this.spinnerService.stop();
    });
  }

  getPrintSettings(school: School) {
    this.printSettings = {
      SchoolId: school.SchoolId,
      LabelPrintChoice: school.LabelPrintChoice,
      LabelTypeId: school.LabelTypeId,
      UsePrintingApp: school.UsePrintingApp,
    };
  }

  updateWeeksPreOrder(weeksPreOrder: number) {
    this.selectedSchool.WeeksPreOrder = weeksPreOrder;
    this.updateSchool(this.selectedSchool);
  }

  updateSchool(data: School) {
    this.spinnerService.start();
    this.schoolService.UpsertSchoolApi(data).subscribe({
      next: () => {
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }
}
