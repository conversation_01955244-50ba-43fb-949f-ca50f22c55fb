import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

// services
import { UserDetailsResolver } from './resolvers/user-details.resolver';

// components
import { SelectedUserComponent, SearchUsersComponent, UserProfileComponent } from './components';
import { UserTransactionsResolver } from './resolvers/user-transactions.resolver';
import { UserOrdersResolver } from './resolvers/user-orders.resolver';

const routes: Routes = [
  {
    path: '',
    component: SearchUsersComponent,
  },
  {
    path: ':id',
    component: SelectedUserComponent,
    resolve: { user: UserDetailsResolver },
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'profile',
      },
      {
        path: 'profile',
        component: UserProfileComponent,
        resolve: {
          orders: UserOrdersResolver,
          transactions: UserTransactionsResolver,
          user: UserDetailsResolver,
        },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminUsersManagementRoutingModule {}
