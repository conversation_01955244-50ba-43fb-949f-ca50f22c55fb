import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SchoolsButtonModule } from 'src/app/schools-button/schools-button.module';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { AddEventItemData, EventItem, ResultDialogData } from 'src/app/sharedModels';
import { MatDialog } from '@angular/material/dialog';
import { ExistingItemPickerDialog } from '../existing-item-picker-dialog/existing-item-picker-dialog.component';
import { MenuEditorRoutingModule } from 'src/app/menu-editor/menu-editor-routing.module';
import { OptionChipListComponent } from 'src/app/menu-editor/components/option-chip-list/option-chip-list.component';
import { GroupedOptionChipListComponent } from 'src/app/menu-editor/components/grouped-option-chip-list/grouped-option-chip-list.component';
import { DialogResultV2Component } from 'src/app/shared/components';
import { MenuEditorApiService, SchoolEventManagerService, SpinnerService } from 'src/app/sharedServices';
import { CreateItemDialogComponent } from '../create-item-dialog/create-item-dialog.component';
import { ItemImageComponent } from 'src/app/menu-editor/components/item-image/item-image.component';
import { OptionFormDialogComponent } from '../option-form-dialog/option-form-dialog.component';

@Component({
  selector: 'event-items',
  standalone: true,
  imports: [
    CommonModule,
    SchoolsButtonModule,
    MatTableModule,
    ExistingItemPickerDialog,
    CreateItemDialogComponent,
    MenuEditorRoutingModule,
    OptionChipListComponent,
    GroupedOptionChipListComponent,
    ItemImageComponent
  ],
  templateUrl: './event-items.component.html',
  styleUrls: ['./event-items.component.scss'],
})
export class EventItemsComponent implements OnChanges {
  @Input() items: EventItem[];
  @Input() merchantId: number;
  @Input() eventId: number;
  @Input() eventIsPublish: boolean;
  @Input() isEditFlow: boolean = true;
  displayedColumns: string[] = ['Image','Item', 'Price', 'Option', 'Remaining Quantity', 'Edit'];
  dataSource: MatTableDataSource<EventItem> = new MatTableDataSource<EventItem>();

  constructor(public dialog: MatDialog,
    private schoolEventManagerService: SchoolEventManagerService, private menuEditorApiService: MenuEditorApiService,
    private spinnerService: SpinnerService) {}

  ngOnChanges(changes: SimpleChanges) {
    this.dataSource.data = this.items;
  }

  addExistingItemDialog(): void {

    let data : AddEventItemData = {
      EventId: this.eventId,
      MerchantId: this.merchantId,
      SelectedItemIds: []
    }

    this.items.forEach(i => data.SelectedItemIds.push(i.menuItemId));

    const dialogRef = this.dialog.open(ExistingItemPickerDialog, {
      width: '800px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
        this.RefreshListItems();
    });
  }

  CreateNewItemDialog(): void {
    let data : AddEventItemData = {
      EventId: this.eventId,
      MerchantId: this.merchantId,
      SelectedItemIds: []
    }

    const dialogRef = this.dialog.open(CreateItemDialogComponent, {
      width: '1000px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
        this.RefreshListItems();
    });
  }

  UnlinkItemPrompt(item: EventItem): void {

    let data = new ResultDialogData();

    if(this.eventIsPublish){
      data.TitleLine1 = 'Event is Published';
      data.TextLine1 = 'Items can\'t be removed when the Event is active.';
      data.TextLine2 = 'Do you want to mark this item as out of stock?';
      data.CancelButton = 'Cancel';
      data.ConfirmButton = 'Yes';
    }else{
      data.TitleLine1 = 'Are you sure?';
      data.TextLine1 = 'Are you sure you want to remove this item:';
      data.TextLine2 = `'${item.name}'?`;
      data.CancelButton = 'Cancel';
      data.ConfirmButton = 'Yes, remove';
    }

    const dialogRef = this.dialog.open(DialogResultV2Component, {
      width: '450px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe((cancelResult: boolean) => {
      if (!cancelResult) {

        if(this.eventIsPublish){
          // put item stock to zero
          this.markItemAsOutOfStock(item.menuItemId);
        }else{
          this.removeItem(item.menuItemId);
        }
        
      }
    });
  }

  removeItem(menuItemId: number): void {
    this.spinnerService.start();

    this.schoolEventManagerService.RemoveItemFromEvent(this.eventId, menuItemId).subscribe({
      next: res => {
        this.RefreshListItems();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
      },
    });
  }

  markItemAsOutOfStock(menuItemId: number): void {
    this.spinnerService.start();

    this.menuEditorApiService.MarkItemAsOutOfStock(menuItemId).subscribe({
      next: res => {
        this.RefreshListItems();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
      },
    });
  }

  RefreshListItems(): void {

    this.schoolEventManagerService.GetEventItems(this.eventId).subscribe({
      next: res => {
        this.items = res;
        this.dataSource.data = this.items;
      },
      error: error => {
        this.spinnerService.stop();
      },
    });
  }

}
