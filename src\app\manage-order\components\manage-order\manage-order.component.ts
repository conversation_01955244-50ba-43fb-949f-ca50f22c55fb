import { Component, OnInit, On<PERSON><PERSON>roy, signal } from '@angular/core';
import { Location } from '@angular/common';
import { environment } from '../../../../environments/environment';
import * as moment from 'moment';
import { MatBottomSheet } from '@angular/material/bottom-sheet';

// Ngrx
import { Store, select } from '@ngrx/store';
import { Subscription, combineLatest } from 'rxjs';
import * as cartSelectors from '../../../states/shoppingCart/shopping-cart.selectors';
import {
  MenuNameSelect,
  MenuPickerSelect,
  datePickerSelect,
  dayDetail,
} from 'src/app/states/family/family.selectors';
import { FamilyState } from '../../../states';
import * as cartActions from '../../../states/shoppingCart/shopping-cart.actions';

// Models
import {
  MenuItem,
  Menu,
  BaseComponent,
  Category,
  MenuTypeEnum,
  ImageUrlEnum,
  UserCashless,
  FilterMenuDate,
  WeekDayAbbreviation,
  OrderFilterRequest,
  CheckIfOrderExist,
  FamilyDayOrders,
  RefinedOrderItem,
  RefinedOrder,
  MenuItemStock,
  MenuFilterCheckBox,
  CartItem,
  CartOption,
} from 'src/app/sharedModels';

// services
import {
  ItemsFilterService,
  PayService,
  OrderApiService,
  DebounceService,
  MenuService,
  UserService,
} from 'src/app/sharedServices';

// Components
import { FilterMenuDateSheetComponent } from '../filter-menu-date-sheet/filter-menu-date-sheet.component';
import { AddTimeToDate, ConvertToUniversalDateFormat } from 'src/app/utility';
import { selectedChild } from 'src/app/states/children/children.selectors';
import { GetMenuItemsThatMatchOrderItemMenuId } from '../../functions/menu-item-sort-helper';
import { GetCartItemsPrice } from '../../functions/calculate-price';
import { ConvertOrderItemToCartType } from '../../functions/convert-to-cart-items';
import { convertSchoolDateTimeToLocalDateTime } from 'src/app/utility/timezone-helper';

@Component({
  selector: 'family-manage-order',
  templateUrl: './manage-order.component.html',
  styleUrls: ['./manage-order.component.scss'],
})
export class ManageOrderComponent extends BaseComponent implements OnInit, OnDestroy {
  private subscriptionShoppingCart$: Subscription;
  private subscriptionItemsFilters$: Subscription;
  private subscriptionDayDetail$: Subscription;
  private subscriptionMenuName$: Subscription;
  private orderFilterSubscription$: Subscription;

  selectedMenuType: string;
  selectedStudent: UserCashless;
  selectedOrderDate: Date;
  menuName: string;
  menuId: number;
  menuLoading = signal<boolean>(true);
  GENERIC_ERROR_MESSAGE = 'Something went wrong, Please try again';
  orderToEdit: RefinedOrder;
  eventCutOffTime: string;
  IsAdminOrMerchant: boolean;

  titlePage = signal<string>('');
  titlePageMobile = signal<string>('');
  currentMenu: Menu;
  private currentCategory: Category;
  currentCategoryToDisplay: Category;
  private itemsFilters: MenuFilterCheckBox[] = [];
  showMobilePlaceOrder: boolean = false;
  priceCart = signal<number>(0);
  menuTypeEnum = MenuTypeEnum;
  deactivatedFilters: string;
  menuCutOffTime: string;
  noMenuMessage = null;
  shoppingCart: CartItem[] = [];

  constructor(
    private store: Store<{ family: FamilyState }>,
    private location: Location,
    private _bottomSheet: MatBottomSheet,
    private itemsFiltersService: ItemsFilterService,
    private payService: PayService,
    private orderApiService: OrderApiService,
    private debounceService: DebounceService,
    private menuService: MenuService,
    private userService: UserService
  ) {
    super();
  }

  ngOnInit() {
    this.IsAdminOrMerchant = this.userService.IsCanteenOrAdmin();
    this.itemsFiltersService.SetSelectedFilterOptions(null); //removed selected menu filters from last order

    this.orderFilterSubscription$ = combineLatest([
      this.store.pipe(select(MenuPickerSelect)),
      this.store.pipe(select(selectedChild)),
      this.store.pipe(select(datePickerSelect)),
    ]).subscribe(([menuType, student, date]) => {
      this.orderFilterChange(menuType, student, date);
    });

    this.subscriptionMenuName$ = this.store.pipe(select(MenuNameSelect)).subscribe((menuName: string) => {
      this.menuName = menuName;
      this.setPageTitle();
    });

    this.subscriptionDayDetail$ = this.store
      .pipe(select(dayDetail))
      .subscribe((dayDetail: FamilyDayOrders) => {
        this.menuId = dayDetail.MenuId;
        this.orderToEdit = dayDetail?.OrderToEdit ? dayDetail.OrderToEdit : null;
        this.menuCutOffTime = dayDetail?.CutOffDate ? dayDetail.CutOffDate.toString() : null;
      });

    // filters management
    this.itemsFilters = this.itemsFiltersService.GetSelectedFilterOptions();
    this.subscriptionItemsFilters$ = this.itemsFiltersService.filtersUpdatedEvent$.subscribe(filters => {
      this.itemsFilters = filters;
      this.FilterItems();
    });

    this.subscriptionShoppingCart$ = this.store
      .pipe(select(cartSelectors.getCartItems))
      .subscribe((cartItems: CartItem[]) => {
        this.showMobilePlaceOrder = cartItems.length > 0;
        this.priceCart.set(GetCartItemsPrice(cartItems));
      });

    // refresh balance
    this.payService.UpdateBalance();
  }

  orderFilterChange(menuType: string, student: UserCashless, date: Date): void {
    if (!this.selectedStudent?.UserId || student?.UserId !== this.selectedStudent?.UserId) {
      this.deactivatedFilters = student?.SchoolDeactivatedFilters || null;
    }
    this.selectedStudent = student;
    this.selectedOrderDate = date;
    this.selectedMenuType = menuType;
    this.loadMenuDebounce();
    this.setPageTitle();
  }

  convertWeekDays(openingDays: string): string[] {
    const days = openingDays?.split(',');
    return days?.map(day => WeekDayAbbreviation[day]) || [];
  }

  loadMenuDebounce = this.debounceService.callDebounce(this.refreshMenu, 350, false, true);

  refreshMenu(): void {
    if (!this.selectedMenuType || !this.selectedStudent || !this.selectedOrderDate) {
      return;
    }
    this.menuLoading.set(true);
    this.currentMenu = null;

    if (this.orderToEdit) {
      this.loadMenu();
      return;
    }
    this.canteenMenuAvailableCheck();
  }

  canteenMenuAvailableCheck(): void {
    const request: OrderFilterRequest = {
      studentId: this.selectedStudent.UserId,
      orderDate: ConvertToUniversalDateFormat(this.selectedOrderDate),
      menuType: this.selectedMenuType,
    };

    this.orderApiService.GetOrderByStudentOrderDateAndMenuType(request).subscribe({
      next: (res: CheckIfOrderExist) => {
        this.processPreMenuCheck(res);
      },
      error: error => {
        this.menuLoading.set(false);
        this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;
        this.handleErrorFromService(error);
      },
    });
  }

  processPreMenuCheck(res: CheckIfOrderExist): void {
    if (!res) {
      this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;
    }

    if (this.isSchoolClosedForCanteenOrders(res)) {
      this.setNoMenuMessage(`Sorry, the canteen's closed right now`);
      return;
    }

    if (this.orderAlreadyPlaced(res)) {
      const message = `You have already placed an Order for: ${this.menuName} - ${this.getFormattedDate()}`;
      this.setNoMenuMessage(message);
      return;
    }
    this.loadMenu();
  }

  orderAlreadyPlaced(res: CheckIfOrderExist): boolean {
    if (this.selectedMenuType === MenuTypeEnum.Event) {
      return this.eventOrderAlreadyPlaced(res);
    }
    const isUniformOrder = this.selectedMenuType === MenuTypeEnum.Uniform;
    return !isUniformOrder && Boolean(res?.order);
  }

  eventOrderAlreadyPlaced(res: CheckIfOrderExist): boolean {
    //check existing order matches current menuId - this is to cover same day school event cases
    return res?.order?.some(order => order.MenuId === this.menuId);
  }

  isSchoolClosedForCanteenOrders(res: CheckIfOrderExist): boolean {
    const isDayClosed = this.isSchoolDayClosed(this.selectedStudent.SchoolOpeningDays);
    const schoolIsClosed = isDayClosed || res?.isSchoolClosed;
    return this.isCanteenOrder() && schoolIsClosed;
  }

  isCanteenOrder(): boolean {
    return this.selectedMenuType === MenuTypeEnum.Recess || this.selectedMenuType === MenuTypeEnum.Lunch;
  }

  getFormattedDate(): string {
    return moment(this.selectedOrderDate).format('dddd Do MMMM');
  }

  setNoMenuMessage(message: string) {
    this.noMenuMessage = message;
    this.menuLoading.set(false);
  }

  isSchoolDayClosed(openDays: string) {
    if (!openDays) {
      return false;
    }
    const schoolDays = this.convertWeekDays(this.selectedStudent.SchoolOpeningDays);
    return schoolDays.findIndex(openDay => openDay === moment(this.selectedOrderDate).format('ddd')) < 0;
  }

  private loadMenu(): void {
    if (!this.selectedMenuType || !this.selectedStudent) {
      return;
    }

    this.menuService.GetMenuBySchoolAndType(this.selectedStudent.SchoolId, this.selectedMenuType).subscribe({
      next: (res: Menu[]) => {
        this.menuLoading.set(false);
        const menuToDisplay = this.menuDataExists(res) ? this.getMenuToDisplay(res) : null;
        if (menuToDisplay && menuToDisplay?.MenuJSON) {
          this.processMenuResult(menuToDisplay);
          return;
        }
        this.noMenuMessage = `No ${this.menuName} Menu Available`;
      },
      error: error => {
        this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;
        this.menuLoading.set(false);
        this.handleErrorFromService(error);
      },
    });
  }

  processMenuResult(menuResult: Menu): void {
    this.noMenuMessage = null;
    this.currentMenu = this.addStockToMenu(menuResult);
    this.menuCutOffTime = this.getMenuCutOffTime();
    this.SetCategory(this.currentMenu.MenuJSON[0]);
    this.FilterItems();
    if (this.selectedMenuType == this.menuTypeEnum.Uniform) {
      this.itemsFiltersService.SetSelectedFilterOptions(null);
    }

    if (this.orderToEdit) {
      this._InitEditCartInShoppingCartState();
    }
  }

  addStockToMenu(menuResult: Menu): Menu {
    menuResult.MenuJSON = menuResult.MenuJSON.map((category: Category) => {
      return { ...category, item: this.addStockToCategoryItems(category, menuResult.StocksJSON) };
    });
    return menuResult;
  }

  addStockToCategoryItems(category: Category, menuStockData: MenuItemStock[]): MenuItem[] {
    return category.item.map((item: MenuItem) => {
      return this.addStockToMenuItem(item, menuStockData);
    });
  }

  addStockToMenuItem(item: MenuItem, menuStockData: MenuItemStock[]) {
    const stockIndex = menuStockData?.findIndex(stock => stock.MenuItemId === item.MenuItemId);
    if (stockIndex >= 0) {
      const stock: MenuItemStock = menuStockData[stockIndex];
      item.Stocks = stock.Stocks;
    }
    return item;
  }

  menuDataExists(menuRes: Menu[]): boolean {
    return menuRes && menuRes?.length > 0;
  }

  getMenuToDisplay(menuData: Menu[]): Menu {
    //If we know exactly what menu we want, find it, if not use the default
    return menuData.find(menu => menu.MenuId === this.menuId) || menuData[0];
  }

  setPageTitle(): void {
    this.titlePageMobile.set(`${this.menuName} - ${moment(this.selectedOrderDate).format('ddd - D/MM')}`);
    this.titlePage.set(`${this.selectedStudent.FirstName} - ${this.titlePageMobile()}`);
  }

  getMenuCutOffTime(): string {
    return this.selectedMenuType === MenuTypeEnum.Event
      ? moment(this.menuCutOffTime).format() // the event cut off has already been converted to local time in selected-order-history
      : this.getCanteenCutOffTime();
  }

  getCanteenCutOffTime(): string {
    const dateTime = AddTimeToDate(this.selectedOrderDate, this.currentMenu.CutOffTime);
    return convertSchoolDateTimeToLocalDateTime(dateTime, this.selectedStudent.SchoolTimeZoneOffSetHours);
  }

  /** Filter the items for the current Category */
  private FilterItems() {
    this.currentCategoryToDisplay = new Category();
    this.currentCategoryToDisplay.item = [];

    const selectedFiltersList = this.itemsFilters?.filter((filter: MenuFilterCheckBox) => filter.selected);
    // filter items only if the filters are activated
    if (this.currentCategory && selectedFiltersList?.length) {
      this.filterCurrentMenuCategory(selectedFiltersList);
    } else {
      // if no filter display the complete items list
      this.currentCategoryToDisplay = this.currentCategory;
    }
  }

  filterCurrentMenuCategory(selectedFiltersList: MenuFilterCheckBox[]): void {
    this.currentCategory.item.forEach((menuItem: MenuItem) => {
      if (this.hasFilters(selectedFiltersList, menuItem)) {
        this.currentCategoryToDisplay.item.push(menuItem);
      }
    });
  }

  hasFilters(selectedFiltersList: MenuFilterCheckBox[], menuItem: MenuItem): boolean {
    let shouldInclude = true;
    selectedFiltersList.map((filter: MenuFilterCheckBox) => {
      const itemDoesNotHaveSelectedFilter = !menuItem[filter.code];
      if (itemDoesNotHaveSelectedFilter) {
        shouldInclude = false;
      }
    });
    return shouldInclude;
  }

  ngOnDestroy(): void {
    this.subscriptionShoppingCart$?.unsubscribe();
    this.subscriptionDayDetail$?.unsubscribe();
    this.subscriptionItemsFilters$?.unsubscribe();
    this.subscriptionMenuName$?.unsubscribe();
    this.orderFilterSubscription$?.unsubscribe();
  }

  //////////////////////////////////////////////
  // Functions
  /////////////////////////////////////////////

  GetUrlCategory(large: boolean): string {
    const imageUrl = large ? ImageUrlEnum.MenusLG : ImageUrlEnum.MenusSM;
    return environment.blobStorage + imageUrl + this.currentCategory.CatUrl;
  }

  UniformCategoriesChanged(catName: string): void {
    let cat = this.currentMenu.MenuJSON.find(x => x.CatName == catName);
    this.SetCategory(cat);
  }

  SetCategory(category: Category): void {
    this.currentCategory = category;
    this.FilterItems();
  }

  IsCurrentCategory(cat: Category): boolean {
    return this.currentCategory?.CatName == cat.CatName;
  }

  AddToCart(item: RefinedOrderItem): void {
    const cartItem: CartItem = this.convertMenuItemToCartItem(item);
    this.store.dispatch(cartActions.addToCart({ cartItem }));
  }

  convertMenuItemToCartItem(item: RefinedOrderItem): CartItem {
    const options: CartOption[] = item?.SelectedOptions.map(option => {
      return {
        menuItemOptionId: option.MenuItemOptionId,
        optionName: option.OptionName,
        optionCost: option.OptionCost,
        parentOptionId: option.MenuItemOptionsCategoryId,
      };
    });

    return {
      date: this.selectedOrderDate,
      studentId: this.selectedStudent.UserId,
      studentName: this.selectedStudent.FirstName,
      schoolId: this.selectedStudent.SchoolId,
      menuType: this.selectedMenuType,
      menuName: this.menuName,
      menuId: this.currentMenu.MenuId,
      menuCutOffDateTime: this.menuCutOffTime,
      canteenId: this.currentMenu.CanteenId,
      itemCartId: moment().unix(),
      menuItemId: item.MenuItemId,
      name: item.Name,
      itemPriceIncGst: item.ItemPriceIncGst,
      selectedOptions: options || [],
      quantity: item.Quantity,
    };
  }

  GoBackClick(): void {
    this.location.back();
  }

  /** Show Menu & Date Filters */
  ShowMenusFilters(): void {
    let dataSheet: FilterMenuDate = {
      menuType: this.selectedMenuType,
      orderDate: this.selectedOrderDate,
    };

    this._bottomSheet.open(FilterMenuDateSheetComponent, {
      data: dataSheet,
    });
  }

  private _InitEditCartInShoppingCartState() {
    //match edited orders items with menu items
    let updatedOrder: RefinedOrder = this.updateOrderItemsWithMenuData(this.currentMenu.MenuJSON);

    const cartItems = updatedOrder.Items.forEach((item: RefinedOrderItem, index: number) => {
      const cartItemToAdd = ConvertOrderItemToCartType(
        this.menuCutOffTime,
        updatedOrder,
        index,
        this.selectedStudent.FirstName
      );
      this.store.dispatch(cartActions.addToEditCart({ cartItem: cartItemToAdd }));
      this.store.dispatch(cartActions.addToCart({ cartItem: cartItemToAdd }));
    });

    return cartItems;
  }

  updateOrderItemsWithMenuData(menuJSON: Category[]): RefinedOrder {
    const order: RefinedOrder = this.orderToEdit;
    const originalOrderItems: RefinedOrderItem[] = order.Items;
    const orderItemIdList = originalOrderItems.map(item => item.MenuItemId);

    const matchingMenuItems: MenuItem[] = GetMenuItemsThatMatchOrderItemMenuId(menuJSON, orderItemIdList);

    const updatedOrderItems = originalOrderItems.map(x => {
      const match = matchingMenuItems.find(menuItem => menuItem.MenuItemId === x.MenuItemId);
      if (match) {
        return { ...x, Name: match.Name, ItemPriceIncGst: match.Price };
      }
    });

    return { ...order, Items: updatedOrderItems };
  }

  disableOrderFilters(): boolean {
    const isEventOrUniformOrder =
      this.selectedMenuType == MenuTypeEnum.Event || this.selectedMenuType == MenuTypeEnum.Uniform;
    return isEventOrUniformOrder || Boolean(this.orderToEdit);
  }
}
