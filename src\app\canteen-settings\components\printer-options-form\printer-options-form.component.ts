import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

//Models
import {
  BaseComponent,
  LabelPrintChoiceEnum,
  SchoolPrintSettings,
  ThermalPrinterOptionEnum,
  UpdatePrintSettingsRequest,
} from 'src/app/sharedModels';

//Services
import { PrintingApiService, SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'printer-options-form',
  templateUrl: './printer-options-form.component.html',
  styleUrls: ['./printer-options-form.component.scss'],
})
export class PrinterOptionsFormComponent extends BaseComponent implements OnInit {
  @Input() printSettings: SchoolPrintSettings;
  LabelPrintChoiceEnum = LabelPrintChoiceEnum;
  thermalPrinterOptionEnum = ThermalPrinterOptionEnum;
  form: FormGroup;

  A4_PRINTER_ID = 5;
  THERMAL_PRINTER_ID = 2;
  THERMAL_2023_PRINTER_ID = 4;

  constructor(private printingService: PrintingApiService, private spinnerService: SpinnerService) {
    super();
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.printSettings?.currentValue) {
      this._createForm();
    }
  }

  private _createForm() {
    this.form = new FormGroup({
      labelType: new FormControl(this.printSettings.LabelTypeId),
      labelPrintChoice: new FormControl(
        this.printSettings.LabelPrintChoice || this.LabelPrintChoiceEnum.Order
      ),
    });
  }

  submitForm() {
    let data: UpdatePrintSettingsRequest = {
      SchoolId: this.printSettings.SchoolId,
      LabelTypeId: this.labelType.value,
      LabelPrintChoice: this.labelPrintChoice.value,
    };

    this.spinnerService.start();
    this.printingService.UpdateSchoolPrintOptionsAPI(data).subscribe({
      next: () => {
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this._createForm();
        this.handleErrorFromService(error);
      },
    });
  }

  get labelType() {
    return this.form.get('labelType');
  }
  get labelPrintChoice() {
    return this.form.get('labelPrintChoice');
  }
  get printerOption() {
    return this.form.get('printerOption');
  }
}
