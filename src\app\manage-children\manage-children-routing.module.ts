import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

// Components
import { AddChildComponent, ListChildrenComponent } from './components';

// Services
import { ListStudentsResolver, ListSchoolsResolver } from '../sharedServices';

const routes: Routes = [
  {
    path: '',
    component: ListChildrenComponent,
    resolve: { students: ListStudentsResolver },
  },
  {
    path: 'add',
    component: AddChildComponent,
    resolve: { schools: ListSchoolsResolver },
  },
  {
    path: 'edit',
    component: AddChildComponent,
    resolve: { schools: ListSchoolsResolver },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ManageChildrenRoutingModule {}
