<div class="col-12 pt-4">
  <merchant-school-picker
    (schoolChanged)="onSchoolSelect($event)"
    (merchantChanged)="onMerchantChange($event)"
    [merchantTypeFilter]="merchantFilter"
  ></merchant-school-picker>

  <div class="button-container mt-3 mb-3">
    <icon-button text=" New Event" (onPress)="navToNewEvent()" [leftText]="true" buttonStyle="primaryOrange">
      <img src="assets/icons/white-cross-circle.svg" alt="transfer symbol" />
    </icon-button>
  </div>

  <event-list [eventList]="eventList" (onPress)="navToSelectedEvent($event)" [loading]="loading"></event-list>
</div>
