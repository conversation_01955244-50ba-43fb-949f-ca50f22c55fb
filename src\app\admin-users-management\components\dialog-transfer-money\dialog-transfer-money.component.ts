import { Component, OnInit, Inject } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { KeyValue } from '@angular/common';

// dialog
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { UserCashless } from 'src/app/sharedModels';
import { MatDialog } from '@angular/material/dialog';

import { UserService } from 'src/app/sharedServices';

// Models
import {
  MoneyTransferType,
  RefundRequest,
  RefundResponse,
  BaseComponent,
  School,
} from 'src/app/sharedModels';
import { TransferUserBalanceRequest, TransferUserBalanceResponse } from 'src/app/sharedModels/user/transfer';

@Component({
  selector: 'dialog-transfer-money',
  templateUrl: './dialog-transfer-money.component.html',
  styleUrls: ['./dialog-transfer-money.component.scss'],
})
export class DialogTransferMoneyComponent extends BaseComponent implements OnInit {
  invalidValueError: string = 'Invalid value entered';
  form: FormGroup;
  selectedTransferType: string = MoneyTransferType.Transfer;
  transferTypeEnum = MoneyTransferType;
  listSchools: KeyValue<string, string>[] = [];
  spinner: boolean = false;
  showForm: boolean = true;
  transferComplete: boolean = false;
  selectedSchoolName: string;
  priceToUpdate: number = null;
  hasError: boolean = false;

  // Confirmation modal data
  title = 'Transfer Money';
  message: string;
  confirmButtonText: string;

  //const hint text
  transferToHintText = 'Transfer from the selected user to a user ID';

  constructor(
    public dialogRef: MatDialogRef<DialogTransferMoneyComponent>,
    @Inject(MAT_DIALOG_DATA) public user: UserCashless,
    private userService: UserService,
    public dialog: MatDialog
  ) {
    super();
  }

  ngOnInit() {
    this._GetListSchools();
    this.CreateForm();
  }

  CreateForm() {
    let defaultSchool: number;
    if (this.listSchools && this.listSchools.length > 0) {
      defaultSchool = Number(this.listSchools[0].key);
    }

    if (this.selectedTransferType === this.transferTypeEnum.Credit) {
      //credit form
      this.form = new FormGroup({
        transferType: new FormControl(this.selectedTransferType, [Validators.required]),
        creditAmount: new FormControl(null, [Validators.required, Validators.min(0), Validators.max(999)]),
        schoolId: new FormControl(defaultSchool, [Validators.required]),
        creditDescription: new FormControl(null, [Validators.required, Validators.maxLength(30)]),
      });
    } else {
      //transfer form
      this.form = new FormGroup({
        transferType: new FormControl(this.selectedTransferType, [Validators.required]),
        transferAmount: new FormControl(null, [Validators.required, Validators.min(0), Validators.max(999)]),
        transferTo: new FormControl(null, [Validators.required, Validators.min(0)]),
        transferDescription: new FormControl(null, [Validators.required, Validators.maxLength(30)]),
      });
    }

    this.transferType.valueChanges.subscribe(val => {
      if (this.selectedTransferType != val) {
        this.selectedTransferType = val;
        this.CreateForm();
      }
    });
  }

  private _GetListSchools() {
    let schoolsArray = [];
    if (this.user?.Children) {
      this.user.Children.forEach(c => {
        let index = schoolsArray.findIndex(i => i.Name == c.SchoolName);
        if (index < 0) {
          let newSchool = new School();
          newSchool.SchoolId = c.SchoolId;
          newSchool.Name = c.SchoolName;
          schoolsArray.push(newSchool);
        }
      });
    }
    if (schoolsArray) {
      schoolsArray.forEach((school: School) => {
        this.listSchools.push({ key: school.SchoolId.toString(), value: school.Name });
      });
    }
  }

  CloseModal(): void {
    this.dialogRef.close(this.priceToUpdate);
  }

  ConfirmClick() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    if (this.selectedTransferType === this.transferTypeEnum.Credit) {
      this.selectedSchoolName = this.listSchools.find(x => x.key == this.schoolId.value).value;
      this.title = 'Confirm Credit';
      this.message = `Credit ${this.user.FirstName} ${this.user.Lastname} $${this.creditAmount.value} from ${this.selectedSchoolName}. Do you want to proceed?`;
      this.confirmButtonText = 'Yes, credit';
      this.showForm = false;
      return;
    }
    this.title = 'Confirm Transfer';
    this.message = `Transfer $${this.transferAmount.value} from ${this.user.FirstName} ${this.user.Lastname} to user ID: ${this.transferTo.value}`;
    this.confirmButtonText = 'Yes, transfer';
    this.showForm = false;
  }

  ConfirmButtonPress() {
    if (this.transferComplete) {
      this.CloseModal();
      return;
    }
    if (this.hasError) {
      this.hasError = false;
      this.transferComplete = false;
      this.CreateForm();
      this.showForm = true;

      return;
    }
    if (this.selectedTransferType === this.transferTypeEnum.Credit) {
      this.ProcessCredit();
      return;
    }
    this.ProcessTransfer();
  }

  ProcessCredit() {
    this.spinner = true;

    let request: RefundRequest = new RefundRequest();
    request.Amount = this.creditAmount.value;
    request.SchoolId = this.schoolId.value;
    request.UserId = this.user.UserId;
    request.Message = this.creditDescription.value;

    this.userService.RefundUser(request).subscribe({
      next: (result: RefundResponse) => {
        this.priceToUpdate = result.UpdatedBalance;
        this.spinner = false;
        this.title = 'Credit Success!';
        this.message = `You have successfully credited ${this.user.FirstName} ${this.user.Lastname} $${this.creditAmount.value} from ${this.selectedSchoolName}`;
        this.confirmButtonText = 'Done';
        this.showForm = false;
        this.transferComplete = true;
      },
      error: error => {
        this.displayError(error);
        //show modal
        this.spinner = false;
        this.handleErrorFromService(error);
      },
    });
  }

  displayError(error: string) {
    this.title = 'Error';
    this.message = this.GetErrorMessage(error)[0];
    this.confirmButtonText = 'Try again';
    this.showForm = false;
    this.hasError = true;
    this.confirmButtonText = 'Go back';
  }

  ProcessTransfer() {
    this.spinner = true;
    let request: TransferUserBalanceRequest = new TransferUserBalanceRequest();
    request.Amount = this.transferAmount.value;
    request.ToUserId = this.transferTo.value;
    request.FromUserId = this.user.UserId;
    request.Message = this.transferDescription.value;

    this.userService.TransferUserBalance(request).subscribe({
      next: (result: TransferUserBalanceResponse) => {
        this.priceToUpdate = result.FromUserUpdatedBalance;
        this.spinner = false;
        this.title = 'Transfer Success!';
        this.message = `You have successfully transferred $${this.transferAmount.value} from ${this.user.FirstName} ${this.user.Lastname} to user ID: ${this.transferTo.value}`;
        this.confirmButtonText = 'Done';
        this.showForm = false;
        this.transferComplete = true;
      },
      error: error => {
        this.displayError(error);
        //show modal
        this.spinner = false;
        this.handleErrorFromService(error);
      },
    });
  }

  get transferType() {
    return this.form.get('transferType');
  }

  get creditAmount() {
    return this.form.get('creditAmount');
  }

  get transferAmount() {
    return this.form.get('transferAmount');
  }

  get transferTo() {
    return this.form.get('transferTo');
  }

  get creditDescription() {
    return this.form.get('creditDescription');
  }

  get transferDescription() {
    return this.form.get('transferDescription');
  }

  get schoolId() {
    return this.form.get('schoolId');
  }
}
