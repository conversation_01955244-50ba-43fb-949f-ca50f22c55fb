@import '../../../../styles/cashless-font.scss';
@import '../../../../styles/cashless-theme.scss';
@import '../../../../styles/cashless-breakpoints.scss';

/* TODO(mdc-migration): The following rule targets internal classes of dialog that may no longer apply for the MDC version. */
mat-dialog-content {
  overflow: inherit;
  padding: 0;
  margin: 0;
}

h4 {
  font-family: 'bariol_bold';

  &.totalOrder {
    margin-top: 30px;
  }

  & .subHeader {
    color: $orange-3;
  }
}

.headerContainer {
  height: auto;
}

.closeButton {
  margin-top: 5px;
  margin-left: 5px;
}

.closeButtonFake {
  height: 60px;
}

.paddingLine {
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 12px;
  padding-right: 12px;
}

.titleDialog {
  text-align: center;
  font-size: 24px;
  margin-bottom: 15px;
  margin-top: 0px;
}

.subHeader {
  font-family: 'bariol_bold';
}

.itemList {
  font-style: italic;
}

.chargeDesc {
  font-style: italic;
  margin-top: 5px;
}

.warning {
  padding-bottom: 24px;
}

.cancelButton {
  margin-bottom: 10px;
}

.securityLink {
  padding-top: 10px;

  @media (max-width: $breakpoint-md) {
    padding-bottom: 70px;
  }
}

.feesList {
  & span {
    display: inline-block;
  }
}

.detailList {
  list-style: none;
  margin-top: 0;

  &.remainingList {
    padding-left: 0px;
    margin-top: 20px;
    margin-bottom: 0;

    & a {
      font-size: 12px;
      color: $orange-3;
    }
  }
}

.walletBalance {
  margin-top: 30px;
  margin-bottom: 5px;
  font-family: 'bariol_bold';

  & .subHeader {
    color: $orange-3;
  }
}
