<div class="container-fluid">
  <nav-back-button (navBack)="GoBack()" text="Go back"></nav-back-button>

  <div class="row">
    <div class="col-12 col-md-6">
      <h2 *ngIf="!IsEdit">Add a child</h2>
      <h2 *ngIf="IsEdit">Edit child details</h2>
      <div class="cardDefaultCanteen">
        <form *ngIf="form" [formGroup]="form" (ngSubmit)="onSubmit()" class="cashlessForm">
          <mat-form-field appearance="outline">
            <mat-label>First name</mat-label>
            <input matInput placeholder="First name" formControlName="firstName" type="text" required />
            <mat-error *ngIf="firstName.invalid">{{ getErrorMessageFirstName() }}</mat-error>
          </mat-form-field>
          <mat-form-field appearance="outline">
            <mat-label>Last name</mat-label>
            <input matInput placeholder="Last name" formControlName="lastName" type="text" required />
            <mat-error *ngIf="lastName.invalid">{{ getErrorMessageLastName() }}</mat-error>
          </mat-form-field>
          <mat-form-field appearance="outline" *ngIf="listSchools">
            <mat-label>School name</mat-label>
            <mat-select placeholder="School" formControlName="school" required>
              <mat-option *ngFor="let school of listSchools" [value]="school.SchoolId">{{
                school.Name
              }}</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Class</mat-label>
            <mat-select placeholder="Class" formControlName="class" required>
              <mat-option *ngIf="!listClass" [value]="">No class</mat-option>
              <mat-option *ngFor="let class of listClass" [value]="class.ClassId">{{
                class.Name
              }}</mat-option>
            </mat-select>
          </mat-form-field>
        </form>
      </div>
    </div>
    <div *ngIf="HasPayAtCanteenFeature()" class="col-12 col-md-6">
      <h2>Canteen order</h2>
      <div class="cardDefaultCanteen">
        <p>
          Allow the canteen to place orders on your behalf when your child wants to order directly at the
          counter. To confirm the order, the canteen will ask your child their favourite colour.
        </p>
        <p>
          The order amount will be automatically deducted from your balance. There are no automatic top ups,
          so if your balance is low, the order will be declined.
        </p>
        <form *ngIf="form" [formGroup]="form" (ngSubmit)="onSubmit()" class="cashlessForm">
          <div class="authorizeCheckbox">
            <mat-checkbox formControlName="allowCanteen">Authorize Canteen Order</mat-checkbox>
          </div>

          <colour-picker
            *ngIf="allowCanteen.value == true"
            [favouriteColour]="favouriteColour.value"
            (favouriteColourChanged)="favouriteColour.setValue($event)"
          ></colour-picker>
        </form>
      </div>
    </div>

    <div *ngIf="HasAllergyAlertFeature()" class="col-12 col-md-6">
      <h2>Allergies</h2>
      <div class="cardDefaultCanteen">
        <p>
          Let us know about any allergies, so they can be printed on your child’s lunch bag labels by the
          canteen.
        </p>
        <form *ngIf="form" [formGroup]="allergyForm" (ngSubmit)="onSubmit()" class="cashlessForm">
          <div class="pb-2" formArrayName="allergies">
            <mat-checkbox
              *ngFor="let alias of allergyFormArray.controls; let i = index"
              class="allergyCheckBox"
              [formControlName]="i"
              >{{ ALLERGY_DATA[i].title }}</mat-checkbox
            >
          </div>
        </form>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-12 col-md-6">
      <div class="cardDefaultCanteen">
        <div class="row">
          <div class="col-12">
            <button class="PrimaryButton" type="button" (click)="onSubmit()" [disabled]="!form.valid">
              <span *ngIf="!IsEdit">Add child</span>
              <span *ngIf="IsEdit">Save</span>
            </button>
          </div>
          <div class="col-12">
            <button mat-flat-button type="button" class="SecondaryButton cancelButton" (click)="CancelForm()">
              Cancel
            </button>
          </div>

          <div *ngIf="CanArchive()" class="col-12 pt-2">
            <button mat-stroked-button color="warn" class="archiveButton" (click)="ArchiveClicked()">
              Delete child
            </button>
          </div>
          <div class="col-12">
            <mat-error *ngIf="errorAPI">{{ WriteError() }}</mat-error>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
