import { ChangeDetectionStrategy, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CANTEEN_CATEGORY_ICON_ARRAY, UNIFORM_CATEGORY_ICON_ARRAY } from 'src/app/sharedModels';

@Component({
  selector: 'category-icon',
  standalone: true,
  templateUrl: './category-icon.component.html',
  styleUrls: ['./category-icon.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CategoryIconComponent implements OnChanges {
  @Input() iconName: string;

  ICON_PATH = 'assets/menuIcons';
  DEFAULT_ICON = 'default.svg';
  iconSource: string = `${this.ICON_PATH}/${this.DEFAULT_ICON}`;

  ngOnChanges(simpleChanges: SimpleChanges): void {
    const iconFileName = this.getIconName(simpleChanges.iconName.currentValue);
    this.iconSource = `${this.ICON_PATH}/${iconFileName}`;
  }

  getIconName(imageName: string): string {
    const iconFileName = this.getImageNameFromFileName(imageName);
    return this.validCategoryName(iconFileName) ? `${iconFileName}.svg` : this.DEFAULT_ICON;
  }

  validCategoryName(imageName: string): boolean {
    const allCategories = [...CANTEEN_CATEGORY_ICON_ARRAY, ...UNIFORM_CATEGORY_ICON_ARRAY];
    return allCategories.includes(imageName);
  }

  public getImageNameFromFileName(imageName: string): string {
    return imageName ? imageName.replace('.jpg', '') : null;
  }
}
