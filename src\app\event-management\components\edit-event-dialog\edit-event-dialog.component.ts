import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { SharedToolsModule } from 'src/app/shared-tools/shared-tools.module';
import { EventFormComponent } from '../event-form/event-form.component';
import { BaseComponent, ListClasses, SchoolClass, SchoolEvent } from 'src/app/sharedModels';
import { SchoolClassesService } from 'src/app/sharedServices';
import { SharedModule } from 'src/app/shared/shared.module';

@Component({
  selector: 'app-edit-event-dialog',
  standalone: true,
  imports: [CommonModule, SharedToolsModule, MatDialogModule, EventFormComponent, SharedModule],
  templateUrl: './edit-event-dialog.component.html',
  styleUrls: ['./edit-event-dialog.component.scss'],
})
export class EditEventDialog extends BaseComponent implements OnInit {
  loading: boolean = true;
  schoolClasses: SchoolClass[];
  constructor(
    public dialogRef: MatDialogRef<EditEventDialog>,
    @Inject(MAT_DIALOG_DATA) public selectedEvent: SchoolEvent,
    private classesService: SchoolClassesService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getSchoolClasses();
  }

  closeModal(): void {
    this.dialogRef.close();
  }

  saveEvent(schoolEvent: SchoolEvent): void {
    this.dialogRef.close(schoolEvent);
  }

  getSchoolClasses(): void {
    this.classesService.GetClassesBySchoolAPI(this.selectedEvent.SchoolId, true).subscribe({
      next: (res: ListClasses) => {
        if (res?.Classes) {
          this.schoolClasses = res.Classes;
        }
        this.loading = false;
      },
      error: error => {
        this.loading = false;
        this.handleErrorFromService(error);
      },
    });
  }
}
