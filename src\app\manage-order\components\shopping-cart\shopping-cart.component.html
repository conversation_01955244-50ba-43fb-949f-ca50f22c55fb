<div [ngClass]="{ 'container-fluid': isMobile }">
  <div *ngIf="isMobile" class="row">
    <div class="col-8">
      <nav-back-button (navBack)="GoBackClick()" text="Shopping Cart"></nav-back-button>
    </div>
    <div class="col-4">
      <clear-cart-button [showButton]="showPlaceOrder && !orderToEdit" (pressed)="confirmClearCart()" />
    </div>
  </div>

  <div *ngIf="!isMobile" class="row">
    <div class="col-8">
      <h4 class="title">Shopping Cart</h4>
    </div>
    <div class="col-4">
      <clear-cart-button [showButton]="showPlaceOrder && !orderToEdit" (pressed)="confirmClearCart()" />
    </div>
  </div>

  <ng-container *ngFor="let cart of shoppingCartForDisplay">
    <div class="row">
      <div class="col-12">
        <h5 class="cartTitle">
          {{ cart[0].studentName }} - {{ cart[0].menuType | customMenuName : cart[0].menuName }} -
          {{ cart[0].date | date : 'EE dd/LL' }}
        </h5>
      </div>
    </div>

    <div *ngFor="let item of cart" class="row itemRow pt-2">
      <div class="col-3 col-sm-5 col-md-4 col-lg-3">
        <mat-form-field appearance="outline">
          <select
            matNativeControl
            [ngModel]="item.quantity"
            (change)="InputChanged(item.itemCartId, $event)"
            id="cart-item-quantity-picker"
          >
            <option *ngIf="!item.MaxQuantity || item.MaxQuantity >= 1" value="1">1</option>
            <option *ngIf="!item.MaxQuantity || item.MaxQuantity >= 2" value="2">2</option>
            <option *ngIf="!item.MaxQuantity || item.MaxQuantity >= 3" value="3">3</option>
            <option *ngIf="!item.MaxQuantity || item.MaxQuantity >= 4" value="4">4</option>
            <option *ngIf="!item.MaxQuantity || item.MaxQuantity >= 5" value="5">5</option>
          </select>
        </mat-form-field>
      </div>
      <div class="col-7 col-sm-5 col-md-6 col-lg-7">
        <div class="row">
          <div class="col-12">
            <h6 class="itemName">{{ item.name }}</h6>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <p>
              <span *ngFor="let option of item.selectedOptions">
                {{ option.optionName }}
                <strong class="spacerDescription">. </strong>
              </span>
              {{ [item] | calculateCartItemsPrice : true }}
            </p>
          </div>
        </div>
      </div>
      <div class="col-1">
        <div class="removeFromCart">
          <mat-icon
            aria-hidden="false"
            aria-label="Remove from cart"
            (click)="RemoveFromCart(item.itemCartId)"
            id="remove-cart-item-button"
            >clear</mat-icon
          >
        </div>
      </div>
    </div>

    <div class="row totalRow">
      <div class="col-12">
        <h5>{{ cart | calculateCartItemsPrice }}</h5>
      </div>
    </div>
  </ng-container>

  <div *ngIf="!showPlaceOrder && !shoppingCartForDisplay.length" class="row totalRow">
    <div class="col-12">
      <h5>{{ 0 | currency }}</h5>
    </div>
  </div>

  <div>
    <div class="row">
      <div *ngIf="showPlaceOrder" class="col-12">
        <primary-button
          text="{{ textPlaceOrder }} ( {{ cartPrice | currency }} )"
          (onPress)="OrderClick()"
          [disabled]="disableConfirmButton()"
        ></primary-button>
      </div>
    </div>
    <div *ngIf="canCancelChanges()" class="row">
      <div class="col-12 pt-2">
        <button *ngIf="orderToEdit" mat-button color="warn" class="WarnLink" (click)="ClickCancelChanges()">
          Cancel Changes
        </button>
      </div>
    </div>
    <div *ngIf="isAdminCanteenUser && studentParent" class="row">
      <div class="col-12">
        <p>
          Remaining balance in parent wallet : {{ parentBalanceRemaining - cartPrice | currency }}
          <span *ngIf="cartPrice > parentBalanceRemaining" class="noFunds"> - Not enough funds</span>
        </p>
      </div>
    </div>
  </div>
</div>
