<mat-dialog-content>
  <ng-container>
    <div class="row no-gutters">
      <div class="col-12">
        <div *ngIf="!orderPlaced" class="modalClose noBackground closeButton" (click)="closeDialog()">
          <mat-icon matTooltip="Close modal">close</mat-icon>
        </div>
      </div>
    </div>
    <ng-container *ngIf="!isProcessing && !isTopUp">
      <div *ngIf="orderPlaced" class="row no-gutters">
        <div class="col-12">
          <div class="closeButtonFake"></div>
        </div>
      </div>
      <div class="row no-gutters">
        <div class="col-12 headerContainer">
          <h3 *ngIf="!orderPlaced" class="titleDialog">Order Details</h3>
          <h3 *ngIf="orderPlaced" class="titleDialog">We are processing your order</h3>
        </div>
      </div>

      <div class="row no-gutters paddingLine">
        <div class="col-12">
          <!-- Confirm order -->
          <div *ngIf="!orderPlaced && !summaryLoading">
            <app-orders-details
              [createOrderSummary]="createOrderSummary"
              [editOrderSummary]="editOrderSummary"
              [totalFees]="totalFees"
              [accountBalance]="accountBalance"
            ></app-orders-details>

            <!-- Top Up Choice -->
            <payment-top-up-choices
              *ngIf="needToTopUp()"
              (choiceChanged)="TopUpAmountChanged($event)"
              [chargeAmount]="topUpMinimumAmount()"
              [isNestedTopUp]="true"
            ></payment-top-up-choices>

            <p *ngIf="errorMessage">
              <mat-error>{{ errorMessage }}</mat-error>
            </p>

            <!-- buttons -->
            <button
              mat-flat-button
              type="button"
              class="SecondaryButton cancelButton"
              (click)="closeDialog()"
            >
              Go back
            </button>

            <ng-container *ngIf="needToTopUp(); else confirmOrderButton">
              <button
                type="button"
                class="PrimaryButton"
                (click)="TopUpClick()"
                [disabled]="!topUpAmount"
                id="top-up-click-button"
              >
                {{ topUpAmount | moneyButtonDisplay : 'Top up' }}
              </button>
            </ng-container>

            <ng-template #confirmOrderButton>
              <primary-button
                id="create-purchase-button"
                [text]="editOrderId > 0 | placeOrderButtonText : totalPrice"
                (onPress)="confirmOrder()"
                [disabled]="canteenOrAdminInsufficientWalletBalance"
                [loading]="buttonLoading"
              >
              </primary-button
            ></ng-template>
          </div>
        </div>
      </div>

      <!-- Order Placed -->
      <app-orders-placed *ngIf="orderPlaced" (goToOrders)="GotToOrders()"></app-orders-placed>
    </ng-container>

    <!-- Top Up -->
    <ng-container *ngIf="!isProcessing && isTopUp">
      <div class="row no-gutters paddingLine">
        <div class="col-12">
          <top-up-form
            (PaymentSucceed)="closeTopUp()"
            [topUpAmount]="topUpAmount"
            [isNestedTopUp]="true"
            [userBalance]="accountBalance"
          ></top-up-form>
          <p class="securityLink">
            Your security is important to us.
            <a
              href="https://intercom.help/spriggyschools/en/articles/3297670-is-spriggy-schools-safe"
              target="_blank"
              >Find out more</a
            >
          </p>
        </div>
      </div>
    </ng-container>

    <div *ngIf="isProcessing || summaryLoading" class="spinnerDialog">
      <app-spinner [manual]="true"></app-spinner>
    </div>
  </ng-container>
</mat-dialog-content>
