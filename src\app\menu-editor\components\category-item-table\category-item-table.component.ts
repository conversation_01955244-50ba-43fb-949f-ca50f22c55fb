import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CategoryEditor, EventItem, MenuItem } from 'src/app/sharedModels';
@Component({
  selector: 'category-item-table',
  templateUrl: './category-item-table.component.html',
  styleUrls: ['./category-item-table.component.scss'],
})
export class CategoryItemTableComponent implements OnInit {
  @Input() tableData: MenuItem[] = [];
  @Input() selectedCategory: CategoryEditor;
  @Input() selectedItems: EventItem[];
  @Output() addItem: EventEmitter<MenuItem> = new EventEmitter();

  constructor() {}

  ngOnInit(): void {}

  clickRow(row: MenuItem) {
    this.addItem.emit(row);
  }

  ItemIsSelected(menuItemId: number) {
    if(this.selectedItems != null && this.selectedItems.length > 0){
      return this.selectedItems.findIndex(m => m.menuItemId == menuItemId) > -1;
    }

    return false;
  }
}
