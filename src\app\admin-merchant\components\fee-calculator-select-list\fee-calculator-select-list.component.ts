import { KeyValue } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

//Models
import { FeeChangedEvent } from 'src/app/sharedModels/fee/FeeCalculator';

@Component({
  selector: 'fee-calculator-select-list',
  templateUrl: './fee-calculator-select-list.component.html',
  styleUrls: ['./fee-calculator-select-list.component.scss'],
})
export class FeeCalculatorSelectListComponent implements OnInit {
  @Input() values: KeyValue<string, string>[] = [];
  @Input() placeholder: string;
  @Input() initialVal: number;
  @Output() saveFee: EventEmitter<FeeChangedEvent> = new EventEmitter();
  form: FormGroup;
  constructor() {}

  ngOnInit(): void {
    this._createForm();
  }

  _createForm(): void {
    this.form = new FormGroup({
      menuOption: new FormControl(this.initialVal),
    });
  }

  saveFeeCalculator(): void {
    if (this.menuOption.value === this.initialVal) {
      return;
    }
    let feeInfo: FeeChangedEvent = {
      newFeeId: this.menuOption.value,
      initialFeeId: this.initialVal,
      menuType: this.placeholder,
    };
    this.saveFee.emit(feeInfo);
  }

  get menuOption() {
    return this.form.get('menuOption');
  }
}
