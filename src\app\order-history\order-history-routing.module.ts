import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

// components
import {
  OrderHistoryComponent,
  UniformHistoryComponent,
  EventHistoryComponent,
  CanteenHistoryComponent,
} from './components';
import { UniformEventHistoryResolver } from './resolvers/uniform-event-history.resolver';

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: '',
      },
      {
        path: '',
        component: OrderHistoryComponent,
      },
      {
        path: 'uniform',
        component: UniformHistoryComponent,
        resolve: {
          orders: UniformEventHistoryResolver,
        },
      },
      {
        path: 'canteen',
        component: CanteenHistoryComponent,
      },
      {
        path: 'event',
        component: EventHistoryComponent,
        resolve: {
          orders: UniformEventHistoryResolver,
        },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OrderHistoryRoutingModule {}
