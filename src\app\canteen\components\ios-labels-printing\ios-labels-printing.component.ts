import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

// Models
import { A4PageLabel } from 'src/app/sharedModels';

// Services
import { CanteenService } from 'src/app/sharedServices';

@Component({
  selector: 'app-ios-labels-printing',
  templateUrl: './ios-labels-printing.component.html',
  styleUrls: ['./ios-labels-printing.component.scss'],
})
export class IosLabelsPrintingComponent implements OnInit {
  pagesLabels: A4PageLabel[] = [];
  custom: boolean = false;
  displaySchoolName: boolean = false;

  constructor(private canteenService: CanteenService, private route: ActivatedRoute) {}

  ngOnInit() {
    this.displaySchoolName = this.route.snapshot.params['displaySchoolName'] === 'true';
    this.pagesLabels = this.canteenService.GetLabels();
  }
}
