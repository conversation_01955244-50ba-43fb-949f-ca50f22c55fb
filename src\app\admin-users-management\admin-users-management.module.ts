import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// modules
import { AdminUsersManagementRoutingModule } from './admin-users-management-routing.module';
import { NavigationModule } from '../navigation/navigation.module';
import { SchoolsFormModule } from '../schools-form/schools-form.module';
import { SchoolsTableModule } from '../schools-table/schools-table.module';
import { SharedToolsModule } from '../shared-tools/shared-tools.module';
import { SchoolsCommonModule } from '../schools-common/schools-common.module';
import { SchoolsButtonModule } from '../schools-button/schools-button.module';
import { SharedModule } from '../shared/shared.module';

// google material
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSortModule } from '@angular/material/sort';
import { MatDialogModule } from '@angular/material/dialog';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';

// components
import {
  SelectedUserComponent,
  UserOrdersComponent,
  UserTransactionsComponent,
  UserDetailsComponent,
  SearchUsersComponent,
  SearchUsersTableComponent,
  ParentDetailsComponent,
  ChildDetailsComponent,
  ParentsListComponent,
  ChildrenListComponent,
  UserTransactionsTableComponent,
  TransactionAmountComponent,
  UserOrdersTableComponent,
  OrderStatusStringPipe,
  userIsActivePipePipe,
  DialogTransferMoneyComponent,
  DialogUserDetailsFormComponent,
  DialogUserReconciliationComponent,
  DialogWalkUpOrdersComponent,
  DialogRefundComponent,
} from './components';

// state
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { userManagementFeature } from '../states/user-management/user-management.reducer';
import { userManagementFeatureKey } from '../states/user-management/user-management-state.interface';
import { UserManagementEffects } from '../states/user-management/user-management.effects';
import { UserProfileComponent } from './components/user-profile/user-profile.component';

//pipes
import { CalculateOrderItemsPricePipe, MoneyButtonDisplayPipe, OrderOptionsStringPipe } from '../sharedPipes';

@NgModule({
  declarations: [
    SelectedUserComponent,
    UserOrdersComponent,
    UserTransactionsComponent,
    UserDetailsComponent,
    SearchUsersComponent,
    SearchUsersTableComponent,
    ParentDetailsComponent,
    ChildDetailsComponent,
    ParentsListComponent,
    ChildrenListComponent,
    UserTransactionsTableComponent,
    TransactionAmountComponent,
    UserOrdersTableComponent,
    OrderStatusStringPipe,
    userIsActivePipePipe,
    DialogTransferMoneyComponent,
    UserProfileComponent,
    DialogUserDetailsFormComponent,
    DialogUserReconciliationComponent,
    DialogWalkUpOrdersComponent,
    DialogRefundComponent,
  ],
  imports: [
    CommonModule,
    RouterModule,
    AdminUsersManagementRoutingModule,
    SchoolsTableModule,
    NavigationModule,
    SchoolsFormModule,
    SharedToolsModule,
    SchoolsCommonModule,
    SchoolsButtonModule,
    MatCheckboxModule,
    MatButtonModule,
    MatMenuModule,
    MatTooltipModule,
    MatSortModule,
    MatDialogModule,
    MatRadioModule,
    FormsModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatInputModule,
    MatFormFieldModule,
    SharedModule,
    MatIconModule,
    MoneyButtonDisplayPipe,
    OrderOptionsStringPipe,
    CalculateOrderItemsPricePipe,
    OrderOptionsStringPipe,

    // state
    StoreModule.forFeature(userManagementFeatureKey, userManagementFeature),
    EffectsModule.forFeature([UserManagementEffects]),
  ],
})
export class AdminUsersManagementModule {}
