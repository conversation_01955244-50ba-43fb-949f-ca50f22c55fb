@import '../../../../styles/cashless-theme.scss';

.page-container {
  max-width: 740px;
}

form {
  width: 100%;
  max-width: 470px;
}

.form-container {
  background-color: #ffffff;
  box-shadow: 0px 3px 3px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

.buttonContainer {
  position: relative;
  width: 280px;
}

.disableCoverGrey {
  position: absolute;
  background-color: $grey-4;
  opacity: 0.5;
  width: 100%;
  height: 100%;
  z-index: 100;
}

.activeBox {
  margin-bottom: 20px;
}

////////////////////////////////
// Custom style for input-text
////////////////////////////////

/*TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
.form ::ng-deep.mat-form-field-disabled .mat-form-field-underline {
  //makes the dotted underline for the diabled input appear as a solid line
  background-image: linear-gradient(
    to right,
    rgba(0, 0, 0, 1) 0,
    rgba(0, 0, 0, 0.42) 33%,
    #c2c7cc 0
  ) !important;
  background-size: 1px 100% !important;
  background-repeat: repeat-x !important;
}

.form ::ng-deep.mat-form-field-disabled .mat-mdc-input-element {
  color: $grey-12;
}
