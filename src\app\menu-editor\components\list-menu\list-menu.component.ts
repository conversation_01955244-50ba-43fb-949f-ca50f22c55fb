import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { FormControl, FormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';

// Models
import { School, BaseComponent, ListMenu, Menu, ListItem, MenuItem } from '../../../sharedModels';

// Services
import { MenuService, SpinnerService } from '../../../sharedServices';

@Component({
  selector: 'app-list-menu',
  templateUrl: './list-menu.component.html',
  styleUrls: ['./list-menu.component.scss'],
})
export class ListMenuComponent extends BaseComponent implements OnInit {
  displayedColumns: string[] = ['name', 'friendlyName', 'IsActive', 'options'];
  dataSource = new MatTableDataSource<Menu>();
  listSchools: School[] = [];
  listMenu: Menu[] = [];
  private defaultValueSchools: number;
  form: FormGroup;

  constructor(
    private activatedRoute: ActivatedRoute,
    private spinnerService: SpinnerService,
    private menuService: MenuService
  ) {
    super();
  }

  ngOnInit() {
    this.spinnerService.start();

    let temp = this.activatedRoute.snapshot.data['schools'];
    this.listSchools = temp.schools;

    if (this.listSchools != null) {
      this.defaultValueSchools = this.listSchools[0].SchoolId;
      this.menuService.SetSelectedSchooldId(this.defaultValueSchools);

      this._GetListMenu(this.defaultValueSchools);
      this.CreateForm();
    } else {
      this.spinnerService.stop();
    }
  }

  /** Get list Menu */
  private _GetListMenu(schoolId: number) {
    this.menuService.GetMenusBySchoolAPI(schoolId).subscribe({
      next: (response: ListMenu) => {
        if (response.Menus != null) {
          this.RefreshTable(response.Menus);
        } else {
          this.listMenu = null;
        }
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  ////////////////////////////////////////
  // Form
  ////////////////////////////////////////
  get school() {
    return this.form.get('school');
  }

  CreateForm() {
    this.form = new FormGroup({
      school: new FormControl(this.defaultValueSchools),
    });

    this.school.valueChanges.subscribe(newValue => {
      this.spinnerService.start();
      this._GetListMenu(newValue);
      this.menuService.SetSelectedSchooldId(newValue);
    });
  }

  NoMenu(): boolean {
    if (this.listMenu != null) {
      return this.listMenu.length > 0;
    } else {
      return false;
    }
  }

  private RefreshTable(menus: Menu[]) {
    this.listMenu = menus;
    this.dataSource.data = this.listMenu;
  }
}
