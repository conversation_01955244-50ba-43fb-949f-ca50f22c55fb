@import '../../../../styles/cashless-theme.scss';

.merchant-heading {
  font-size: 28px;
  color: $grey-12;
}

.header {
  color: $grey-12;
  width: 100%;
  display: flex;
  justify-content: space-between;

  h3 {
    margin: 0;
    padding: 0;
  }
}

.backButton {
  color: $orange-3;
}

.user-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  margin-top: 30px;
}

.mat-mdc-form-field {
  width: 200px;
}

.mat-mdc-form-field.email-input {
  width: 270px;
}

.sub-heading-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 8px;
}

.sub-heading {
  font-size: 18px;
  color: $grey-12;
  font-weight: 700;
  padding: 0;
  margin-bottom: 0px;
}
.button-wrapper {
  display: flex;
  gap: 10px;
  padding-right: 15px;

  a {
    color: $orange-3;
    font-size: 14px;
    cursor: pointer;
  }
}

.form-wrapper {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px;
}

.user-form.form-wrapper {
  padding-top: 18px;
  padding-bottom: 5px;
}

.school-form {
  display: flex;
  flex-wrap: wrap;
  flex-basis: 25%;

  .checkbox-wrapper {
    width: calc(100% * (1 / 3));
    margin: 0;
    padding: 0;
  }
}

.school-form.error {
  border: solid $orange-3 1px;
}

.permissions-form {
  display: flex;
  flex-wrap: wrap;
  flex-basis: 25%;

  .mat-mdc-checkbox {
    width: calc(100% * (1 / 4));
    margin: 0;
    padding: 0;
  }
}

.school-error-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-left: 15px;

  img {
    width: 16px;
    height: 16px;
  }
}

.merchant-btn {
  background-color: $grey-14;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  outline: none;
  border: none;
  color: #ffffff;
  font-size: 18px;
  line-height: 24px;
  padding: 6px 16px;
  border-radius: 14px;
  font-weight: 700;
  cursor: pointer;

  img {
    width: 16px;
    height: 16px;
  }
}

.edit-btn {
  background: transparent;
  outline: none;
  border: none;
  cursor: pointer;
  float: right;
  padding-bottom: 8px;
}

.save-btn-wrapper {
  padding-top: 15px;
  padding-bottom: 100px;
  position: relative;
  display: flex;
  justify-content: flex-end;
}

.save-changes-btn {
  background-color: $orange-3;
  gap: 10px;
  outline: none;
  border: none;
  color: #ffffff;
  font-size: 18px;
  line-height: 24px;
  padding: 6px 16px;
  border-radius: 14px;
  font-weight: 700;
  cursor: pointer;
}

.save-changes-btn:disabled {
  background-color: $grey-6;
  color: $grey-8;
  cursor: default;
}

.input-wrapper {
  display: flex;
  gap: 15px;
}

.editBtnContainer {
  display: flex;
  gap: 20px;
  margin: 10px 0 20px 0;
}
.saveBtn {
  border-radius: 10px;
  background-color: $orange-3;
  border: 0;
  font-size: 18px;
  color: white;
  cursor: pointer;
  width: 70px;
  height: 32px;
}
.saveBtn:disabled {
  background-color: $grey-6;
  color: $grey-8;
  cursor: default;
}
.cancelBtn {
  color: $orange-3;
  border: 0;
  border: 0;
  font-size: 18px;
  background-color: white;
  height: 32px;
  cursor: pointer;
  width: 70px;
}

.disableCoverWhite {
  position: absolute;
  background-color: rgb(255, 255, 255);
  opacity: 0.5;
  width: 100%;
  height: 100%;
  z-index: 100;
}

.disableCoverGrey {
  position: absolute;
  background-color: $grey-4;
  opacity: 0.5;
  width: 100%;
  height: 100%;
  z-index: 100;
}
.relative-section {
  position: relative;
}
