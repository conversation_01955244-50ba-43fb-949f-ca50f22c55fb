// import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

// import { AddMerchantUserSearchComponent } from './add-merchant-user-search.component';

// describe('AddMerchantUserSearchComponent', () => {
//   let component: AddMerchantUserSearchComponent;
//   let fixture: ComponentFixture<AddMerchantUserSearchComponent>;

//   beforeEach(waitForAsync(() => {
//     TestBed.configureTestingModule({
//       declarations: [ AddMerchantUserSearchComponent ]
//     })
//     .compileComponents();
//   }));

//   beforeEach(() => {
//     fixture = TestBed.createComponent(AddMerchantUserSearchComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
