@import '../../../../styles/cashless-theme.scss';

.imageCol {
  border-bottom: 1px solid $grey-3;

  @media (max-width: $breakpoint-md) {
    padding-right: 0;
  }

  & h3 {
    font-size: 22px;
    line-height: 24px;
    color: $charcoal-1;
    display: inline-block;
    vertical-align: middle;
    font-size: 18px;
  }
}

.deleteCol {
  text-align: right;
  border-bottom: 1px solid $grey-3;

  & .deleteLink {
    font-size: 22px;
    line-height: 24px;
    text-decoration: none;
    color: $orange-3;
    display: inline-block;
    margin-top: 22px;
    cursor: pointer;
  }

  & .imgDelete {
    color: $orange-3;
    margin-top: 20px;
    cursor: pointer;

    /* Note: If you're using an SVG icon, you should make the class target the `<svg>` element */
    & svg {
      fill: $orange-3;
    }
  }
}

.rowMethodSelected {
  & .imageCol {
    background-color: $grey-4;
  }

  & .deleteCol {
    background-color: $grey-4;
  }
}

.noPointer {
  cursor: pointer;
}
