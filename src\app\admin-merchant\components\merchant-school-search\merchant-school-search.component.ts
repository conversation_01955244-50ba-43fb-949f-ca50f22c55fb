import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { PageEvent } from '@angular/material/paginator';

import { SpinnerService, MerchantService } from '../../../sharedServices';

import { School, BasePaginatorComponent, Merchant } from '../../../sharedModels';

const _columns = [
  'id',
  'name',
  'cutOffTime',
  'deactivatedFilters',
  'schoolCode',
  'pricingModel',
  'pricingAmount',
  'pricingCap',
];

@Component({
  selector: 'merchant-school-search',
  templateUrl: './merchant-school-search.component.html',
  styleUrls: ['./merchant-school-search.component.scss'],
})
export class MerchantSchoolSearchComponent extends BasePaginatorComponent<School> implements OnInit {
  @Output() selectSchool = new EventEmitter<School>();
  @Input() selectedMerchant: Merchant;
  currentRoute: any;
  noResultsMessage: string = '';
  showResultsTable: boolean = false;

  constructor(private spinnerService: SpinnerService, private merchantService: MerchantService) {
    super(_columns);
  }

  ngOnInit() {
    this.initFilters();
  }

  clearFilter() {
    this.clearFiltersAndResults();
  }

  fetchData(searchInput: string) {
    this.listfilters.Filter = searchInput;
    this._requestSchools();
  }

  /** load search data */
  private _requestSchools() {
    this.spinnerService.start();

    this.merchantService
      .GetMerchantSchoolsSearchReuslts(this.selectedMerchant.canteenId, this.listfilters.Filter)
      .subscribe({
        next: (res: School[]) => {
          this._ProcessResponseSchools(res);
        },
        error: error => {
          this.spinnerService.stop();
          this.handleErrorFromService(error);
        },
      });
  }

  /** Process the list of schools to be used in the search results */
  private _ProcessResponseSchools(response: School[]) {
    if (response) {
      this.listObjects = response;

      if (this.listObjects && this.listObjects.length > 0) {
        this.totalRows = this.listObjects[0].TotalRows;
        this.showResults();
      } else {
        this.totalRows = 0;
      }
    } else {
      this.noSearchResults(this.listfilters.Filter);
    }
    this.dataSource.data = this.listObjects;
    this.spinnerService.stop();
  }

  schoolSelected(school: School) {
    this.selectSchool.emit(school);
  }

  ShowCutOffTime(time: Date) {
    let compare = new Date('0001-01-01');
    return new Date(time) > compare;
  }
}
