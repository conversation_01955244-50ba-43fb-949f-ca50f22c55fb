<div class="row">
  <div class="col-12">
    <h4>Add image</h4>
  </div>
</div>

<div class="row">
  <div class="col-12">
    <p style="color: rgb(0, 0, 0)">File name cannot Include any spaces</p>
    <input
      #fileInput
      id="fileInput"
      type="file"
      accept=".png,.jpg,.jpeg"
      (click)="fileInput.value = null"
      value=""
      (change)="onSelectFile($event.target.files)"
    />
  </div>
</div>
<div class="row">
  <div class="col-12">
    <button class="btnUpload" type="button" *ngIf="url" (click)="Upload()">Upload</button>
  </div>
</div>

<div *ngIf="showError" class="row">
  <div class="col-md-12">
    <span class="error">An error occured</span>
  </div>
</div>
