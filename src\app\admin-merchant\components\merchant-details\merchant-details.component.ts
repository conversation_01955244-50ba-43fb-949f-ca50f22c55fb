import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

// services
import { SpinnerService, MerchantService, PhoneNumberService } from '../../../sharedServices';

// models
import { MerchantDetails } from '../../../sharedModels';

//dialog
import { MatDialog } from '@angular/material/dialog';

// components
import { BaseMerchantFormComponent } from '../../models/base-merchant-form';

@Component({
  selector: 'merchant-details',
  templateUrl: './merchant-details.component.html',
  styleUrls: ['../../styles/merchant-form.scss'],
})
export class MerchantDetailsComponent extends BaseMerchantFormComponent implements OnInit, OnDestroy {
  @Input() merchantDetails: MerchantDetails;

  constructor(
    private spinnerService: SpinnerService,
    protected merchantService: MerchantService,
    public dialog: MatDialog,
    private phoneNumberService: PhoneNumberService
  ) {
    super(dialog, merchantService);
  }

  ngOnInit() {
    this._baseOnInit();
  }

  ngOnDestroy(): void {
    this._baseOnDestroy();
  }

  /**
   * Retry popup after having an error when saving
   */
  SomethingWentWrongPopup() {
    const dialogRef = this._somethingWentWrongPopup();

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.saveMerchantDetails();
      }
    });
  }

  /**
   * Save the merchantDetails
   */
  saveMerchantDetails() {
    this.spinnerService.start();

    let merchantData: MerchantDetails = {
      name: this.name.value,
      friendlyName: this.friendlyName.value,
      type: this.type.value,
      phone: this.phoneNumberService.serverMobileNumber(this.phone.value),
      abn: '',
      status: null,
      statusDate: null,
    };

    this.merchantService.UpdateMerchantDetails(this.selectedMerchant.canteenId, merchantData).subscribe({
      next: res => {
        this.spinnerService.stop();
        this.SuccessPopUp();
        //update form values
        this.merchantDetails = merchantData;
        //update merchant sidebar values
        this.merchantService.UpsertMerchantToService(
          this.merchantDetails.name,
          this.selectedMerchant.canteenId,
          this.selectedMerchant.ownerId,
          'Merchant'
        );
      },
      error: error => {
        this.spinnerService.stop();
        this.SomethingWentWrongPopup();
      },
    });
  }

  /**
   * format landline numbers
   * @param phoneNumber
   * @returns
   */
  displayLandLine(phoneNumber) {
    if (phoneNumber && phoneNumber.length > 0) {
      if (phoneNumber.match(/^0[1-9]/) && !phoneNumber.match(/^04/)) {
        let landline = '(' + phoneNumber.slice(0, 2) + ') ' + phoneNumber.slice(2);
        return landline;
      }
    }
    return phoneNumber;
  }

  ///////////////////////////////////
  // Form
  ///////////////////////////////////

  /**
   * Trigger the form creation
   */
  triggerEdit() {
    this.ReminderPopUp();
    this._createForm();
    this.editDetailsMode = true;
    this.merchantService.setDisableMode(true);
  }

  /**
   * Setup the form with all the controls
   */
  private _createForm() {
    this.formGroup = new FormGroup({
      name: new FormControl(this.merchantDetails.name, Validators.required),
      friendlyName: new FormControl(this.merchantDetails.friendlyName, Validators.required),
      type: new FormControl({ value: this.merchantDetails.type, disabled: true }, Validators.required),
      phone: new FormControl(this.merchantDetails.phone, [
        Validators.required,
        Validators.pattern(/(^\+614[0-9]{8}$)|(^614[0-9]{8}$)|(^04[0-9]{8}$)|(^0[0-9]{9}$)/),
      ]),
    });
  }

  /**
   * Form controls accessors
   */
  get name() {
    return this.formGroup.get('name');
  }
  get friendlyName() {
    return this.formGroup.get('friendlyName');
  }
  get type() {
    return this.formGroup.get('type');
  }
  get phone() {
    return this.formGroup.get('phone');
  }
}
