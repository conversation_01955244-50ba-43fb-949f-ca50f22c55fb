import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import moment from 'moment';
import * as _ from 'lodash';

// component
import { DialogConfirmV2Component } from 'src/app/shared/components';
import { ConfirmModal } from 'src/app/sharedModels';

// state
import { Store } from '@ngrx/store';
import {
  MoveOrdersToProcessed,
  MoveOrdersToError,
} from 'src/app/states/admin/dashboardFeature/dashboard.actions';

// models
import { OrderError } from 'src/app/sharedModels/admin/dashboard';
import { DashboardState } from 'src/app/states/admin/dashboardFeature/dashboard-state.interface';

@Component({
  selector: 'admin-dashboard-processing',
  templateUrl: './admin-dashboard-processing.component.html',
  styleUrls: ['./admin-dashboard-processing.component.scss'],
})
export class AdminDashboardProcessingComponent implements OnInit {
  @Input() orders: OrderError[];
  allComplete: boolean = false;

  constructor(public dialog: MatDialog, private store: Store<{ dashboard: DashboardState }>) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.orders?.currentValue) {
      //read only object needs to be cloned to make changes
      this.orders = _.cloneDeep(this.orders);
    }
  }

  LocalDate(utcCreatedDate: Date) {
    return moment.utc(utcCreatedDate).local().format('YYYY-MM-DD HH:mm:ss');
  }

  CheckDate(utcCreatedDate) {
    return moment.utc(utcCreatedDate).local() < moment().local().subtract(10, 'minutes');
  }

  PushToProcessedClicked() {
    let data = new ConfirmModal();
    data.Title = 'Are you sure?';
    data.Text =
      'You are about to put all these orders into processed. This means the orders will be processed, but payment may not be taken. Let the Spriggy Schools team know you are doing this bulk action and make sure the PM or lead engineer is aware as there is a financial impact for Spriggy';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, move all to Processed';

    const dialogRef = this.dialog.open(DialogConfirmV2Component, {
      width: '500px',
      disableClose: false,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        let orderIds: number[] = this.getSelectedOrders();
        this.store.dispatch(MoveOrdersToProcessed({ orderIds: orderIds }));
      }
    });
  }

  PushToErrorClicked() {
    let data = new ConfirmModal();
    data.Title = 'Are you sure?';
    data.Text =
      'You are about to put all these orders into error. This means the orders won’t be processed, but the parents will be able to replace the orders. Let the Spriggy Schools team know you are doing this bulk action.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, move all to Error';

    const dialogRef = this.dialog.open(DialogConfirmV2Component, {
      width: '500px',
      disableClose: false,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        let orderIds: number[] = this.getSelectedOrders();
        this.store.dispatch(MoveOrdersToError({ orderIds: orderIds }));
      }
    });
  }

  getSelectedOrders() {
    return this.orders.filter(x => x.selected === true).map(x => x.orderId);
  }

  updateAllComplete() {
    this.allComplete = this.orders != null && this.orders.every(t => t.selected);
  }

  someComplete(): boolean {
    if (this.orders == null) {
      return false;
    }
    return this.orders.filter(t => t.selected).length > 0 && !this.allComplete;
  }

  setAll(completed: boolean) {
    this.allComplete = completed;
    if (this.orders == null) {
      return;
    }
    this.orders.forEach(t => (t.selected = completed));
  }

  noOrdersSelected() {
    return !this.orders.some(x => x.selected);
  }
}
