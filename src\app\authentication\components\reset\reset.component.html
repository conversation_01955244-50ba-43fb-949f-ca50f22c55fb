<form [formGroup]="form" (ngSubmit)="ResetPassword()" class="cashlessForm">
  <mat-form-field>
    <input matInput placeholder="Email" formControlName="email" type="email" required />
    <mat-error *ngIf="email.invalid">{{ getErrorMessageEmail() }}</mat-error>
  </mat-form-field>

  <mat-error *ngIf="errorReset?.length > 0">{{ errorReset }}</mat-error>
  <p>
    <button mat-flat-button color="primary" type="submit" [disabled]="!form.valid">Reset</button>
  </p>

  <ng-container *ngIf="submitted">
    <p>
      If the email address you entered is valid, instructions to reset your password will be sent there. 
    </p>
    <p style="font-weight: bold;">
      If you cannot locate the email, please check your junk/spam folder.
    </p>
    <p>
      <span>Come back to <a routerLink="/login">sign in</a> once your password has been reset</span>
    </p>
  </ng-container>

</form>

<div *ngIf="!submitted" class="cashlessLink bottomLink">
  <span>Go back to <a routerLink="/login">Sign in</a></span>
</div>
