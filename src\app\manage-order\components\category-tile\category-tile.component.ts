import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { CategoryIconComponent } from '../category-icon/category-icon.component';

@Component({
  selector: 'category-tile',
  standalone: true,
  templateUrl: './category-tile.component.html',
  styleUrls: ['./category-tile.component.scss'],
  imports: [CommonModule, CategoryIconComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CategoryTileComponent {
  @Input() name: string;
  @Input() iconName: string;
  @Input() isSelected: boolean = false;
  @Output() clicked = new EventEmitter();

  onPress() {
    this.clicked.emit(this.iconName);
  }
}
