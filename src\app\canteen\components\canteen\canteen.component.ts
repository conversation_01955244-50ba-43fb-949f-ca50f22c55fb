import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { FormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { PageEvent } from '@angular/material/paginator';
import { MatSort, Sort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';

import * as moment from 'moment';

// ngrx
import { Subscription } from 'rxjs';

import {
  CanteenSchool,
  BasePaginatorComponent,
  Order,
  ListOrder,
  CanteenOrderRequest,
  CanteenFilters,
  CanteenStatusEnum,
  OrderItem,
  SettingChangedEvent,
  LabelPrintChoiceEnum,
} from '../../../sharedModels';
import { SpinnerService, OrderApiService, CanteenService, PrintingApiService } from '../../../sharedServices';

const displayedColumns: string[] = [
  'OrderId',
  'SchoolName',
  'MenuType',
  'StudentName',
  'ClassName',
  'OrderDate',
];
const uniformColumns: string[] = [
  'select',
  'OrderId',
  'CanteenStatus',
  'StudentName',
  'ClassName',
  'OrderDate',
];

@Component({
  selector: 'app-canteen',
  templateUrl: './canteen.component.html',
  styleUrls: ['./canteen.component.scss'],
})
export class CanteenComponent extends BasePaginatorComponent<Order> implements OnInit {
  dataSource = new MatTableDataSource<Order>();
  listOrders: Order[] = [];
  itemsSelectedOrder: OrderItem[] = [];
  selectedOrder: Order;
  oldStatus: string;
  form: FormGroup;
  statusForm: FormGroup;
  isUniformCanteen: boolean = false;
  CanteenStatusEnum = CanteenStatusEnum;
  usePrintingApp: boolean;
  labelPrintChoice: string;
  private canteenFilters: CanteenFilters;
  canteenListVisible: boolean = true;
  listSchools: CanteenSchool[] = [];
  private subscription: Subscription;
  private ordersRequest: CanteenOrderRequest;

  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private router: Router,
    private orderAPIService: OrderApiService,
    private spinnerService: SpinnerService,
    private canteenService: CanteenService,
    private printingApiService: PrintingApiService
  ) {
    super(displayedColumns);
  }

  ngOnInit(): void {
    // get saved filters
    this.canteenFilters = this.canteenService.GetFilters();

    if (!this.canteenFilters) {
      this.canteenFilters = new CanteenFilters();
      this.canteenFilters.Date = moment().toDate();
      this.canteenFilters.Event = false;
      this.canteenFilters.Recess = true;
      this.canteenFilters.Lunch = true;
      this.canteenFilters.Printed = 'all';
      this.canteenFilters.UniNew = true;
      this.canteenFilters.UniProcessing = true;
      this.canteenFilters.UniReady = true;
      this.canteenFilters.UniCompleted = false;

      this.canteenService.SetFilters(this.canteenFilters);
    }
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  FiltersChanged(request: CanteenOrderRequest): void {
    this.ordersRequest = request;

    this.LoadTableData();

    this.clearRowSelection();
  }

  clearRowSelection(): void {
    this.selection.clear();
    this.selectedOrder = null;
    this.itemsSelectedOrder = null;
  }

  getOrdersRequest(getAll: boolean): void {
    this.ordersRequest.NumberRows = this.listfilters.NumberRows;
    this.ordersRequest.PageIndex = this.listfilters.PageIndex;
    this.ordersRequest.SortBy = this.listfilters.SortBy;
    this.ordersRequest.SortDirection = this.listfilters.SortDirection;
    this.ordersRequest.GetAll = getAll;

    if (
      !this.ordersRequest.NumberRows ||
      this.ordersRequest.NumberRows == undefined ||
      this.ordersRequest.NumberRows == 0
    ) {
      this.ordersRequest.NumberRows = 25;
    }
  }

  LoadTableData(): void {
    this.getOrdersRequest(false);
    this.spinnerService.start();
    this.orderAPIService.GetOrdersBySchoolAndMerchantAPI(this.ordersRequest).subscribe({
      next: (response: ListOrder) => {
        this.RefreshTable(response.Orders);
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  private RefreshTable(orders: Order[]): void {
    this.listOrders = orders;
    this.itemsSelectedOrder = [];
    this.selectedOrder = null;

    if (!this.listOrders || this.listOrders.length == 0) {
      this.dataSource.data = [];
      this.totalRows = 0;
    } else {
      this.dataSource.data = this.listOrders;
      this.totalRows = this.listOrders[0].TotalRows;
    }
  }

  get newStatus() {
    return this.statusForm.get('newStatus');
  }

  SettingsChanged(event: SettingChangedEvent): void {
    this.usePrintingApp = event.UsePrintingApp;
    this.labelPrintChoice = event.LabelPrintChoice;
    this.isUniformCanteen = event.IsUniformCanteen;

    if (this.isUniformCanteen) {
      this.displayedColumns = uniformColumns;
    } else {
      this.displayedColumns = displayedColumns;
    }
  }

  GetItemOptions(item: OrderItem): string {
    let optionsText = '(';

    //item stores modifers in Options
    if (item.Options && item.Options.length > 0) {
      item.Options.forEach(opt => {
        // item has selected options
        if (opt.SelectedOptionList && opt.SelectedOptionList.length > 0) {
          opt.SelectedOptionList.forEach(selectedOpt => {
            optionsText += selectedOpt + ', ';
          });
        }
      });
    }

    //item stores modifiers in SelectedOptions
    if (item.SelectedOptions && item.SelectedOptions.length > 0) {
      item.SelectedOptions.forEach(opt => {
        optionsText += opt.OptionName + ', ';
      });
    }

    optionsText = optionsText.slice(0, -2);
    optionsText += ')';
    if (optionsText.length <= 1) {
      optionsText = '';
    }

    return optionsText;
  }

  ShowButtonPrintLabels(): boolean {
    if (!this.usePrintingApp) {
      return true;
    } else {
      return !this.isUniformCanteen;
    }
  }

  EnableMarkAsFulfilledButton(): boolean {
    if (this.selectedOrder) {
      if (this.selectedOrder.IsFulFilled) {
        return false;
      } else {
        return true;
      }
    } else {
      return false;
    }
  }

  /////////////////////////////////////////////////////////
  // Table functions
  //////////////////////////////////////////////////////////
  selection = new SelectionModel<Order>(true, []);

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    let numRows = 0;

    if (this.dataSource.data) {
      numRows = this.dataSource.data.length;
    }
    return numSelected === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle(): void {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach(row => this.selection.select(row));
  }

  /** The label for the checkbox on the passed row */
  checkboxLabel(row?: Order): string {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row`;
  }

  RowClick(row: Order): void {
    if (
      this.selectedOrder &&
      this.selectedOrder.OrderId === row.OrderId &&
      this.selectedOrder.Items[0].MenuItemId === row.Items[0].MenuItemId
    ) {
      this.selectedOrder = null;
      this.itemsSelectedOrder = null;
    } else {
      this.selectedOrder = row;
      this.itemsSelectedOrder = row.Items;
    }

    //row selection for uniforms
    this.selection.toggle(row);
    if (this.isUniformCanteen && this.selection.selected.length === 1) {
      this.selectedOrder = this.selection.selected[0];
      this.itemsSelectedOrder = this.selection.selected[0].Items;
    }
  }

  PageChange(event: PageEvent): void {
    this.clearRowSelection();
    // Update filter
    this.basePageChange(event);
    this.LoadTableData();
  }

  SortChange(event: Sort) {
    this.listfilters.PageIndex = 0;
    this.listfilters.SortBy = event.active;
    this.listfilters.SortDirection = event.direction;
    this.LoadTableData();
  }

  isRowSelected(row: Order): boolean {
    if (this.isUniformCanteen) {
      //multi selected rows
      return this.selection.isSelected(row);
    } else {
      return (
        this.selectedOrder &&
        this.selectedOrder.OrderId === row.OrderId &&
        this.selectedOrder.Items[0].MenuItemId === row.Items[0].MenuItemId
      );
    }
  }

  showSelectedOrder(): boolean {
    //show selected order canteen
    if (!this.isUniformCanteen && this.selectedOrder) {
      return true;
      //show selected order uniform
    } else {
      return Boolean(this.isUniformCanteen && this.selection.selected.length == 1 && this.selectedOrder);
    }
  }

  /////////////////////////////////////////////////////////
  // Printing functions
  //////////////////////////////////////////////////////////
  PrintAllLabels(): void {
    this.getOrdersRequest(true);
    this.spinnerService.start();

    if (this.labelPrintChoice === LabelPrintChoiceEnum.Order) {
      this.printAllOrders();
      return;
    }
    this.printAllItems();
  }

  printAllOrders(): void {
    this.printingApiService.SetOrdersToPrintAPI(this.ordersRequest).subscribe({
      next: (response: any) => {
        if (response?.guid) {
          this.handlePrintResponse(response.guid);
        }
        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      },
    });
  }

  printAllItems(): void {
    this.printingApiService.SetItemsToPrintAPI(this.ordersRequest).subscribe({
      next: (response: any) => {
        if (response?.guid) {
          this.handlePrintResponse(response.guid);
        }
        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      },
    });
  }

  PrintSelectedOrder(): void {
    this.spinnerService.start();
    if (this.labelPrintChoice == LabelPrintChoiceEnum.Item) {
      this.printOneOrderByItem();
    } else {
      this.printOneOrderByOrder();
    }
  }

  printOneOrderByOrder(): void {
    this.printingApiService.SetOrderToPrintByOrderIdAPI(this.selectedOrder.OrderId).subscribe({
      next: (response: any) => {
        if (response?.guid) {
          this.handlePrintResponse(response.guid);
        }
        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      },
    });
  }

  printOneOrderByItem(): void {
    if (!this.selectedOrder.Items || !this.selectedOrder.Items.length) {
      return;
    }
    const itemId: number = this.selectedOrder.Items[0].MenuItemId;

    this.printingApiService.SetItemToPrintByItemIdAPI(this.selectedOrder.OrderId, itemId).subscribe({
      next: (response: any) => {
        if (response?.guid) {
          this.handlePrintResponse(response.guid);
        }
        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      },
    });
  }

  handlePrintResponse(guid: string): void {
    if (this.usePrintingApp) {
      this._triggerThermalPrinter(guid);
    } else {
      this._triggerA4Printer(guid);
    }
  }

  private _triggerThermalPrinter(guid: string): void {
    let canteenPrinter: any = document.getElementById('canteenPrinter');
    canteenPrinter.href = 'print://' + guid;
    canteenPrinter.click();
  }

  private _triggerA4Printer(guid: string): void {
    this.printingApiService.SetPrintingGuid(guid);
    this.router.navigate(['canteen/labels']);
  }
}
