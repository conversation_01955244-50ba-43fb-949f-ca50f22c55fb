import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';

// state
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { UserManagementState } from 'src/app/states/user-management/user-management-state.interface';
import { SearchTransactions } from 'src/app/states/user-management/user-management.actions';

export const UserTransactionsResolver: ResolveFn<any> = (route: ActivatedRouteSnapshot): Observable<any> => {
  const store = inject(Store<{ userManagement: UserManagementState }>);

  let id = route.params['id'];

  if (id == null || id == undefined) {
    id = route.parent.params['id'];
  }

  store.dispatch(SearchTransactions({ userId: +id }));

  return null;
};
