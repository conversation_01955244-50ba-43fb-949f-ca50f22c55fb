<div
  class="blocOrder"
  [ngClass]="{
    disabled: isPassedOrClosed,
    ordered: hasOrder,
    error: hasError
  }"
  (click)="blockPressed()"
>
  <p class="desktop">
    {{ dayData.orderStatus | weekdayOrderStatus }}
  </p>
  <p class="mobile">
    <span *ngIf="mobileShowText">{{ dayData.orderStatus | weekdayOrderStatus }}</span>
    <mat-icon *ngIf="hasOrder" matTooltip="Ordered">done</mat-icon>
    <mat-icon *ngIf="hasError" matTooltip="Error">error_outline</mat-icon>
    <mat-icon *ngIf="isProcessing" matTooltip="Processing">access_time</mat-icon>
  </p>
</div>
