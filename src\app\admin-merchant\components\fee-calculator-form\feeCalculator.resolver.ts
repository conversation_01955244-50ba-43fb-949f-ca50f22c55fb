import { Injectable } from '@angular/core';

import { ActivatedRouteSnapshot } from '@angular/router';
import { FeeCalculatorService } from 'src/app/sharedServices/fee/feeCalculator.service';

@Injectable({
  providedIn: 'root',
})
export class FeeCalculatorResolver  {
  constructor(private feeCalculatorService: FeeCalculatorService) {}

  resolve(route: ActivatedRouteSnapshot) {
    let schoolId = route.params['schoolId'];
    let merchantId = route.params['merchantId'];

    return this.feeCalculatorService.requestDataForAdminFeeForm(merchantId, schoolId);
  }
}
