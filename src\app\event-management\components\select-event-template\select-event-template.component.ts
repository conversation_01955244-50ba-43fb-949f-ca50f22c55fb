import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { SharedToolsModule } from 'src/app/shared-tools/shared-tools.module';
import { EventTemplate, EventType } from 'src/app/sharedModels';

@Component({
  selector: 'select-event-template',
  templateUrl: './select-event-template.component.html',
  styleUrls: ['./select-event-template.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    SharedToolsModule,
    MatInputModule,
    MatSelectModule,
    ReactiveFormsModule,
  ]
})
export class SelectEventTemplateComponent implements OnInit {
  @Input() eventTypes: EventType[];
  @Output() templateChanged: EventEmitter<EventTemplate> = new EventEmitter();
  form: FormGroup;

  ngOnInit(): void {
    this.createForm();
  }

  createForm(): void {

    this.form = new FormGroup({
      template: new FormControl(0)
    });

    this.template.valueChanges.subscribe(value => {

      if(value == 0){
        this.templateChanged.emit(null);
      }

      this.eventTypes.forEach(e => {
        var template = e.templates.find(t => t.eventTemplateId == value)

        if(template){
          this.templateChanged.emit(template);
          return;
        }
      })
    });
  }

  get template() {
    return this.form.get('template');
  }

}
