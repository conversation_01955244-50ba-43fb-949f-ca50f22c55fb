<div class="p-4 pt-0">
  <h3 class="m-0 pb-4">Fee Calculators</h3>

  <div *ngIf="loading; else form" class="col-12 d-flex justify-content-center">
    <app-spinner [manual]="true"></app-spinner>
  </div>
  <ng-template #form>
    <ng-container *ngIf="merchantType == merchantTypeEnum.Canteen">
      <fee-calculator-select-list
        [values]="recessFeeList"
        placeholder="Recess"
        [initialVal]="initialValues.Recess"
        (saveFee)="saveFeeCalculator($event)"
      ></fee-calculator-select-list>

      <fee-calculator-select-list
        [values]="lunchFeeList"
        placeholder="Lunch"
        [initialVal]="initialValues.Lunch"
        (saveFee)="saveFeeCalculator($event)"
      ></fee-calculator-select-list>
    </ng-container>

    <ng-container *ngIf="merchantType == merchantTypeEnum.Canteen || merchantType == merchantTypeEnum.Event">
      <fee-calculator-select-list
        [values]="eventFeeList"
        placeholder="Event"
        [initialVal]="initialValues.Event"
        (saveFee)="saveFeeCalculator($event)"
      ></fee-calculator-select-list>
    </ng-container>

    <ng-container *ngIf="merchantType === merchantTypeEnum.Uniform">
      <fee-calculator-select-list
        [values]="uniformFeeList"
        placeholder="Uniform"
        [initialVal]="initialValues.Uniform"
        (saveFee)="saveFeeCalculator($event)"
      ></fee-calculator-select-list>
    </ng-container>
  </ng-template>
</div>
