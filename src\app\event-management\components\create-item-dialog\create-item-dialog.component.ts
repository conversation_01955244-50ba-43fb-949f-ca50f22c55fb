import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { EditorItemFormComponent } from 'src/app/menu-editor/components/editor-item-form/editor-item-form.component';
import { SharedToolsModule } from 'src/app/shared-tools/shared-tools.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { AddEventItemData, MenuItem, SchoolEvent } from 'src/app/sharedModels';
import { SchoolEventManagerService } from 'src/app/sharedServices';

@Component({
  selector: 'app-create-item-dialog',
  templateUrl: './create-item-dialog.component.html',
  styleUrls: ['./create-item-dialog.component.scss'],
  standalone: true, 
  imports: [MatDialogModule, EditorItemFormComponent, CommonModule, SharedToolsModule, SharedModule],
})
export class CreateItemDialogComponent {
  loading: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<CreateItemDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public eventData: AddEventItemData,
    private schoolEventManagerService: SchoolEventManagerService
  ){}
  
  closeModal(): void {
    this.dialogRef.close();
  }

  saveItem(item: MenuItem){
    this.schoolEventManagerService.CreateNewItemToEvent(this.eventData.EventId, item).subscribe({
      next: (res) => {
        this.closeModal();
      },
      error: error => {
      },
    });
  }
}
