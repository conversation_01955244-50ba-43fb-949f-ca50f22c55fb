import { Component, Input, OnD<PERSON>roy, OnInit, SimpleChanges } from '@angular/core';

// Ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { FamilyState } from '../../../states';
import { familySetSelectedWeek } from '../../../states/family/family.actions';
import { selectedWeek } from '../../../states/family/family.selectors';
import { GroupedOrderBlockInfo } from 'src/app/sharedModels';
import { DebounceService } from 'src/app/sharedServices';

@Component({
  selector: 'family-canteen-display',
  templateUrl: './family-canteen-display.component.html',
  styleUrls: ['./family-canteen-display.component.scss'],
})
export class FamilyCanteenDisplayComponent implements OnInit, OnDestroy {
  @Input() orderTableData: GroupedOrderBlockInfo;
  private selectedWeekSubscription: Subscription;
  selectedWeek: number;

  constructor(private store: Store<{ family: FamilyState }>, private debounceService: DebounceService) {}

  ngOnInit(): void {
    this.selectedWeekSubscription = this.store.pipe(select(selectedWeek)).subscribe((week: number) => {
      this.selectedWeek = week;
    });
  }

  ngOnDestroy() {
    if (this.selectedWeekSubscription) {
      this.selectedWeekSubscription.unsubscribe();
    }
  }

  weekChangedEvent(week: number) {
    this.selectedWeek = week;
    this.weekChangedDebounce();
  }

  weekChangedDebounce = this.debounceService.callDebounce(this.updateStateSelectedWeek, 50, false, true);

  updateStateSelectedWeek() {
    this.store.dispatch(familySetSelectedWeek({ weekNumber: this.selectedWeek }));
  }
}
