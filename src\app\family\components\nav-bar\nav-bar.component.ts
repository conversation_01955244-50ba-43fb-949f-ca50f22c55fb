import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import {
  Router,
  // import as RouterEvent to avoid confusion with the DOM Event
  Event as RouterEvent,
  NavigationStart,
  NavigationEnd,
  NavigationCancel,
  NavigationError,
  ActivatedRoute,
} from '@angular/router';

// ngrx
import { Subscription } from 'rxjs';

// Services
import { PayService, UserService } from '../../../sharedServices';

// Models
import { GetBalanceRequest } from 'src/app/sharedModels/pay/getBalanceRequest';
import { NavBarData } from 'src/app/sharedModels/navBar/navBar';
import { BrazeTimerService } from 'src/app/sharedServices/brazeTimer.service';
import { Roles } from 'src/app/sharedModels';

@Component({
  selector: 'family-nav-bar',
  templateUrl: './nav-bar.component.html',
  styleUrls: ['./nav-bar.component.scss'],
})
export class FamilyNavBarComponent implements OnInit, OnDestroy {
  accountBalance: number;
  getBalanceRequest: GetBalanceRequest;
  messages: any[] = [];
  opened: boolean = false;
  private subscriptionBalance: Subscription;

  navData: NavBarData[] = [
    { Name: 'Orders', Link: '/family/home' },
    { Name: 'Order History', Link: '/family/order/history' },
    { Name: 'Children', Link: '/family/children' },
    { Name: 'Articles', Link: '/family/articles' },
    { Name: 'Account', Link: '/family/account' },
  ];

  constructor(
    private router: Router,
    private activeRoute: ActivatedRoute,
    private userService: UserService,
    private payService: PayService,
    private brazeTimerService: BrazeTimerService
  ) {}
  ngOnInit(): void {
    // init intercom chat
    this.userService.IdentifyUser();

    this.router.events.subscribe((event: RouterEvent) => {
      this.navigationInterceptor(event);
    });

    this.subscriptionBalance = this.payService
      .SubscribeBalanceUpdate()
      .subscribe(newBalance => (this.accountBalance = newBalance));

    this.payService.UpdateBalance();

    this.brazeTimerService.setUpBrazeInAppMessageTimer(Roles.Parent);
  }

  ngOnDestroy(): void {
    this.subscriptionBalance?.unsubscribe();
    this.brazeTimerService.unsubscribeFromBrazeTimer();
  }

  SettingsClick(): void {
    this.router.navigate(['./account'], { relativeTo: this.activeRoute });
  }

  SignOut(): void {
    this.userService.logout();
  }

  navigationInterceptor(event: RouterEvent): void {
    if (event instanceof NavigationStart) {
      this.opened = false;
    }
    if (event instanceof NavigationEnd) {
      this.opened = false;
    }

    // Set loading state to false in both of the below events to hide the spinner in case a request fails
    if (event instanceof NavigationCancel) {
      this.opened = false;
    }
    if (event instanceof NavigationError) {
      this.opened = false;
    }
  }
}
