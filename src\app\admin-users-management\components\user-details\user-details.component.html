<div class="container-fluid">
  <div *ngIf="user">
    <ng-container *ngIf="isChild; else userTemplate">
      <div class="child-wrapper row">
        <div class="col-12 col-lg-6 fullHeight">
          <user-management-child-details [child]="user"></user-management-child-details>
        </div>

        <div class="col-12 col-lg-6 fullHeight">
          <user-management-parents-list
            *ngIf="user.Parents"
            [parents]="user.Parents"
          ></user-management-parents-list>
        </div>
      </div>
    </ng-container>

    <ng-template #userTemplate>
      <div class="parent-wrapper row">
        <div class="col-12 col-lg-6 fullHeight">
          <user-management-parent-details [parent]="user"></user-management-parent-details>
        </div>

        <div class="col-12 col-lg-6 fullHeight">
          <user-management-children-list
            *ngIf="user.Children"
            [children]="user.Children"
          ></user-management-children-list>
        </div>
      </div>
    </ng-template>
  </div>
</div>
