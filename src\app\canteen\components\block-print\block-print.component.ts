import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import { DeviceDetectorService } from 'ngx-device-detector';

// Models
import { A4PageLabel, A4PrintPositions, BaseComponent, LabelV2 } from 'src/app/sharedModels';

// Services
import { CanteenService } from 'src/app/sharedServices';

@Component({
  selector: 'block-print',
  templateUrl: './block-print.component.html',
  styleUrls: ['./block-print.component.scss'],
})
export class BlockPrintComponent extends BaseComponent implements OnInit {
  @Input() listLabels: LabelV2[];
  private _fakeLabels: LabelV2[] = [];
  a4PageLabel: A4PageLabel[] = [];
  custom: boolean = true;
  displaySchoolName: boolean = false;
  SAFARI_BROWSER = 'Safari';

  constructor(
    private canteenService: CanteenService,
    private deviceService: DeviceDetectorService,
    private router: Router
  ) {
    super();
  }

  ngOnInit() {}

  IsDesktop(): boolean {
    return this.deviceService.isDesktop();
  }

  ClickTabletPrint() {
    if (this.deviceService.browser == this.SAFARI_BROWSER) {
      this.canteenService.SetLabels(this.a4PageLabel);
      this.router.navigate([`/canteen/iosPrint/${this.displaySchoolName}`]);
    } else {
      this.AndroidPrinting();
    }
  }

  private AndroidPrinting() {
    let printContents = document.getElementById('labelsToPrint').innerHTML;
    let popupWin = window.open('', '_blank', 'height=100%,width=auto');
    popupWin.document.open();

    popupWin.document.write(`
      <html>
        <head>
          <title>Print tab</title>
        
          <style>
          @page{
            margin-left: 4mm;
            margin-right: 1mm;
            margin-top: 7mm;
            margin-bottom: 1mm;
            }
          .pageA4{
            width: 295mm;
            padding-top:8mm;
            margin-bottom: 6mm;
        }
    
    .labelsContainer{
        margin: 30px;
    }
    
    
    .colLabel{
        padding-top: 2mm;
        padding-left: 6mm;
        padding-right: 2mm;
        padding-bottom: 2mm;
        width: 87mm;//86mm;
        height: 51mm;
        margin-bottom:2mm;
        margin-right:2mm;
        display: inline-flex;
    
        // border-width: 1px;
        // border-color: green;
        // border-style: solid;
        // border-radius: 8px;
    
    
        font-family: 'bariol_regular';
        font-size: 16px;
    
        &.custom{
            width: 61mm;
            height: 35mm;
            margin-left: 3MM;
            font-size: 13px;
    
            & li{
                font-size: 15px;
            }
        }
    }
    
    .importantText{
        //font-size: 18px;
        font-weight: bold;
    }
    
    .className{
        //font-size: 18px;
        font-family: 'bariol_regular';
        margin-top:4mm;
        margin-left:2mm;
    }
    
    .classNamePage1{
        //font-size: 18px;
        font-family: 'bariol_regular';
        margin-top:2mm;
        margin-left:2mm;
    }
    
    .subTitle{
        font-family: 'bariol_regular';
        margin-top:0px;
        margin-left:2mm;
        font-size: 15px;
    }
    
    .classNameBold{
        //font-size: 18px;
        font-family: 'bariol_bold';
        margin-top:2mm;
    
        &.totalItems{
          display: inline-block;
          margin-top: 2px;
        }
    }
    .orderNo{
        //font-size: 18px;
        font-family: 'bariol_regular';
    }
    
    ul{
        font-family: 'bariol_regular';
        margin-top: 2px;
        margin-bottom: 2px;
        padding-left: 20px;
    
        & li{
            font-size: 19px;
        }
    }
    
    h4{
        width: 100%;
        text-align: right;
    }
    mat-form-field{
        width: 70px;
    }
    
    
    .fullWidthInput{
        width: 100%;
    }

          </style>
        </head>
        <body onload="window.print()">${printContents}</body>
      </html>`);
  }

  generateLabels(settings: A4PrintPositions) {
    this._generateFakeLabels(settings.Line, settings.Column);
  }

  sliceLabels() {
    this._SliceLabelsA4();
  }

  updateCustomValue(val: boolean) {
    this.custom = val;
  }

  updateDisplaySchoolValue(val: boolean) {
    this.displaySchoolName = val;
  }

  private _generateFakeLabels(line, column) {
    let numberFakeLabels = 0; //(this.line.value * 3) + this.column.value;
    let numberLine = 0;
    let numberColumn = 0;

    if (line != 1) {
      numberLine = line - 1;
    }

    if (column != 1) {
      numberColumn = column - 1;
    }

    numberFakeLabels = numberLine * 3 + numberColumn;
    this._fakeLabels = [];

    for (let index = 0; index < numberFakeLabels; index++) {
      this._fakeLabels.push(new LabelV2());
    }

    this._SliceLabelsA4();
  }

  private _SliceLabelsA4() {
    this.a4PageLabel = [];
    this.a4PageLabel[0] = new A4PageLabel();
    this.a4PageLabel[0].fakeLabels = [] = this._fakeLabels;

    let sliceStart = 0;
    let indexPage = 0;

    // sort listLabels into local blocks
    while (sliceStart < this.listLabels.length) {
      if (!this.a4PageLabel[indexPage]) {
        this.a4PageLabel[indexPage] = new A4PageLabel();
      }

      let labelPage = this.a4PageLabel[indexPage].GetRemainingLabels();

      this.a4PageLabel[indexPage].listLabels = this.listLabels.slice(sliceStart, sliceStart + labelPage);

      sliceStart += labelPage;
      indexPage++;
    }
  }
}
