@import '../../../../styles/cashless-theme.scss';
.top-margin {
  margin: 10px 0 30px 0;
}

.disableCoverWhite {
  position: absolute;
  background-color: rgb(255, 255, 255);
  opacity: 0.5;
  width: 100%;
  height: 100%;
  z-index: 100;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;

  h4 {
    font-size: 28px;
    font-weight: 700;
    color: $grey-12;
    padding-top: 8px;
    padding-bottom: 8px;
    margin: 0;
  }
}

.merchant-section {
  position: relative;
  margin: 0;
  padding: 0;
}
.disableCoverWhite {
  position: absolute;
  background-color: #ffffff;
  opacity: 0.5;
  width: 100%;
  height: 100%;
  z-index: 100;
}

.table {
  width: 100%;
  overflow: hidden;
  margin-bottom: 30px;
  border-color: #ffffff;

  tr:nth-child(odd) {
    background-color: $grey-15;
    border-color: #ffffff;
  }
  tr:nth-child(even) {
    background-color: $grey-16;
  }
}

.mat-mdc-header-row .mat-mdc-header-cell {
  border-bottom: 1px solid #ffffff;
  border-top: 1px solid #ffffff;
  background-color: #ffffff;
  color: $grey-17;
  font-size: 14px;
  font-weight: 700;
  padding-right: 8px;
}

.mat-mdc-row .mat-mdc-cell {
  border-bottom: 1px solid #ffffff;
  border-top: 1px solid #ffffff;
}

.editIcon {
  height: 16px;
  width: 16px;
}

.mat-column-username {
  width: 35%;
  color: $grey-12;

  h5 {
    font-size: 14px;
    line-height: 24px;
    padding: 0;
    margin: 0;
  }

  h6 {
    font-size: 12px;
    line-height: 18px;
    padding: 0;
    margin: 0;
  }
}
.mat-column-edit {
  width: 25%;
}

.mat-column-allowUnprintedOrders,
.mat-column-menuEditor,
.mat-column-salesReport,
.mat-column-viewEvent,
.mat-column-emailUnprintedOrders {
  width: 10%;
}

.merchant-btn {
  background-color: $grey-14;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  outline: none;
  border: none;
  color: #ffffff;
  font-size: 18px;
  line-height: 24px;
  padding: 6px 16px;
  border-radius: 14px;
  font-weight: 700;
  cursor: pointer;

  img {
    width: 16px;
    height: 16px;
  }
}

.emptyMessage {
  padding: 10px;
  font-size: 14px;
  line-height: 24px;
}
