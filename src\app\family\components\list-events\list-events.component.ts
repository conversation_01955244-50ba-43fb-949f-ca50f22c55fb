import { Component, OnInit, Input, OnD<PERSON>roy } from '@angular/core';

// Models
import { BaseComponent } from '../../../sharedModels';

// Service
import { ParentHomeService, UserService } from 'src/app/sharedServices';
import { SchoolEventHomeScreenInfo } from 'src/app/sharedModels/home/<USER>';
import { Subscription } from 'rxjs';

@Component({
  selector: 'family-list-events',
  templateUrl: './list-events.component.html',
  styleUrls: ['./list-events.component.scss'],
})
export class ListEventsComponent extends BaseComponent implements OnInit, OnDestroy {
  @Input() listEvents: SchoolEventHomeScreenInfo[] = [];
  isLoading: boolean = false;
  private loadingSubscription: Subscription;

  constructor(private userService: UserService, private parentHomeService: ParentHomeService) {
    super();
  }

  ngOnInit() {
    this.loadingSubscription = this.parentHomeService.loadingEvent$.subscribe((res: boolean) => {
      this.isLoading = res;
    });
  }

  ngOnDestroy(): void {
    if (this.loadingSubscription) {
      this.loadingSubscription.unsubscribe();
    }
  }

  triggerIntercom() {
    this.userService.openIntercom();
  }

  showEventList() {
    return this.listEvents?.length > 0;
  }
}
