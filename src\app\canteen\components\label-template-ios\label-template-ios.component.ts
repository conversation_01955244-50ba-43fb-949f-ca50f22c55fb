import { Component, Input, OnInit } from '@angular/core';
import { LabelV2 } from 'src/app/sharedModels';

@Component({
  selector: 'label-template-ios',
  templateUrl: './label-template-ios.component.html',
  styleUrls: ['./label-template-ios.component.scss'],
})
export class LabelTemplateIosComponent implements OnInit {
  @Input() label: LabelV2;
  @Input() displaySchoolName: boolean;

  constructor() {}

  ngOnInit(): void {}

  GetPrintValue(printField: string, label: LabelV2): string {
    var pos = label.Positions.find(x => x.Name == printField);

    if (pos) {
      return pos.Value;
    } else {
      return '';
    }
  }

  ShowAllergies(label: LabelV2): boolean {
    var pos = label.Positions.find(x => x.Name == 'Allergies');

    if (pos) {
      return !Boolean(pos.Value == null || pos.Value == '');
    } else {
      return false;
    }
  }
}
