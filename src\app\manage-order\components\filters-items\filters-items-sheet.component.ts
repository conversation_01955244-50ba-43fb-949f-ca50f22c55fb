import { Component, OnInit, Inject, ChangeDetectionStrategy } from '@angular/core';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';

// models
import { MENU_FILTERS, MenuFilter, MenuFilterCheckBox } from 'src/app/sharedModels';

// services
import { ItemsFilterService } from 'src/app/sharedServices';

/** Sheet Filter component */
@Component({
  selector: 'filters-items-sheet',
  templateUrl: './filters-items-sheet.component.html',
  styleUrls: ['./filters-items.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FiltersItemsSheetComponent implements OnInit {
  availableFilters: MenuFilterCheckBox[] = [];

  constructor(
    private _bottomSheetRef: MatBottomSheetRef<FiltersItemsSheetComponent>,
    private filtersService: ItemsFilterService,
    @Inject(MAT_BOTTOM_SHEET_DATA) public data: string
  ) {}

  ngOnInit() {
    const previouslySelectedFilters = this.filtersService.GetSelectedFilterOptions();
    this.availableFilters = this.getActiveFilters(this.data, previouslySelectedFilters);
    this.filtersService.SetFilterOptions(this.availableFilters);
  }

  clickFilter() {
    const filtersActive: MenuFilterCheckBox[] = this.areAnyFiltersSelected();
    this.filtersService.SetSelectedFilterOptions(filtersActive);
    this._bottomSheetRef.dismiss(filtersActive?.length);
  }

  clearAllSelectedFilters(): void {
    this.availableFilters.map(filter => (filter.selected = false));
  }

  areAnyFiltersSelected(): MenuFilterCheckBox[] {
    return this.availableFilters?.filter((filter: MenuFilterCheckBox) => filter.selected);
  }

  //active filter = filter option the canteen has enabled
  getActiveFilters = (
    deactivatedFilters: string,
    previouslySelectedFilters: MenuFilterCheckBox[]
  ): MenuFilterCheckBox[] => {
    if (deactivatedFilters === '') {
      return this.getAllMenuFilterOptions(previouslySelectedFilters);
    }

    return this.getActiveMenuFilterOptions(deactivatedFilters, previouslySelectedFilters);
  };

  getActiveMenuFilterOptions(
    deactivatedFilters: string,
    previouslySelectedFilters: MenuFilterCheckBox[]
  ): MenuFilterCheckBox[] {
    const availableFilters = [];
    const arrayDeactivatedFilters = deactivatedFilters.split(',');
    MENU_FILTERS.forEach((initFilter: MenuFilter) => {
      if (this.filterHasBeenDeactivated(arrayDeactivatedFilters, initFilter)) {
        return;
      }
      availableFilters.push(this.getFilterCheckboxObject(initFilter, previouslySelectedFilters));
    });
    return availableFilters;
  }

  getAllMenuFilterOptions(previouslySelectedFilters: MenuFilterCheckBox[]): MenuFilterCheckBox[] {
    return MENU_FILTERS.map((initFilter: MenuFilter) => {
      return this.getFilterCheckboxObject(initFilter, previouslySelectedFilters);
    });
  }

  getFilterCheckboxObject(
    initFilter: MenuFilter,
    previouslySelectedFilters: MenuFilterCheckBox[]
  ): MenuFilterCheckBox {
    return { ...initFilter, selected: this.isFilterSelected(initFilter, previouslySelectedFilters) };
  }

  isFilterSelected(filter: MenuFilter, previouslySelectedFilters: MenuFilterCheckBox[]): boolean {
    const index = previouslySelectedFilters?.findIndex(prevFilter => prevFilter.code === filter.code);
    return index >= 0;
  }

  filterHasBeenDeactivated(arrayDeactivatedFilters: string[], menuFilter: MenuFilter): boolean {
    const indexFound = arrayDeactivatedFilters.find(filter => filter === menuFilter.deactivateName);
    return Boolean(indexFound);
  }
}
