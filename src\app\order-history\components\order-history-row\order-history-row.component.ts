import { Component, Input, SimpleChanges } from '@angular/core';
import { Router } from '@angular/router';
import { RefinedOrder, UserCashless } from 'src/app/sharedModels';
import { CreateOrderService } from 'src/app/sharedServices/order/create-order.service';
import { ConvertStringToDate } from 'src/app/utility';
import {
  convertSchoolDateTimeToLocalDateTime,
  convertSchoolTimeToLocalTime,
} from 'src/app/utility/timezone-helper';

@Component({
  selector: 'order-history-row',
  templateUrl: './order-history-row.component.html',
  styleUrls: ['./order-history-row.component.scss'],
})
export class OrderHistoryRowComponent {
  @Input() order: RefinedOrder;
  @Input() student: UserCashless;
  orderToShow: RefinedOrder;

  constructor(private router: Router, private createOrderService: CreateOrderService) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.order?.currentValue) {
      const menuCutOff: string = this.order?.MenuCutOffTime
        ? convertSchoolTimeToLocalTime(this.order.MenuCutOffTime, this.student.SchoolTimeZoneOffSetHours)
        : null;
      const eventCutOff: string = this.order.EventCutOffDate
        ? convertSchoolDateTimeToLocalDateTime(
            this.order.EventCutOffDate,
            this.student.SchoolTimeZoneOffSetHours
          )
        : null;
      this.orderToShow = {
        ...this.order,
        MenuCutOffTime: menuCutOff,
        EventCutOffDate: eventCutOff,
      };
    }
  }

  viewSelectedOrder() {
    this.createOrderService.getAndSetDayDetail(
      this.order.MenuType,
      this.order.MenuName,
      this.order.MenuId,
      ConvertStringToDate(this.order.OrderDate),
      this.student,
      null, // will get cut off time in selected order history screen
      this.order
    );
    this.router.navigate(['family/order/selectedOrderHistory/', this.order.OrderId]);
  }
}
