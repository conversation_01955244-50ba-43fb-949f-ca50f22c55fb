import { KeyValue } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Location } from '@angular/common';

// models
import { Roles, UserCashless } from 'src/app/sharedModels';

// services
import { AdminService } from 'src/app/sharedServices';

//dialog imports
import { MatDialog } from '@angular/material/dialog';
import { DialogTransferMoneyComponent } from '../dialog-transfer-money/dialog-transfer-money.component';

// states
import { select, Store } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { UserManagementState } from 'src/app/states/user-management/user-management-state.interface';
import { SelectedUserSelector } from 'src/app/states/user-management/user-management.selectors';
import { DialogWalkUpOrdersComponent } from '../dialog-walk-up-orders/dialog-walk-up-orders.component';

@Component({
  selector: 'management-selected-user',
  templateUrl: './selected-user.component.html',
  styleUrls: ['./selected-user.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectedUserComponent implements OnInit, OnDestroy {
  private userSubscription: Subscription;
  user: UserCashless;
  role: string;
  isParent: boolean = false;
  isChild: boolean = false;

  // tabs config
  tabsRoutes: KeyValue<string, string>[] = [];

  constructor(
    private adminService: AdminService,
    private cd: ChangeDetectorRef,
    private userManagementStore: Store<{ userManagement: UserManagementState }>,
    public dialog: MatDialog,
    private _location: Location
  ) {}

  ngOnInit(): void {
    this.userSubscription = this.userManagementStore.pipe(select(SelectedUserSelector)).subscribe(userRes => {
      if (userRes) {
        this.user = userRes;
        this.role = this.adminService.GetRoleText(this.user.Role.toString());
        this.isParent = this.user.Role == Roles.Parent;
        this.isChild = this.user.Role == Roles.Child;
        this.cd.markForCheck();
      }
    });
  }

  ngOnDestroy(): void {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  openTransferMoneyModal() {
    const dialogRef = this.dialog.open(DialogTransferMoneyComponent, {
      width: '500px',
      disableClose: true,
      data: this.user,
      autoFocus: false,
    });

    dialogRef.afterClosed().subscribe(updatedBalance => {
      if (updatedBalance) {
        let tempItem = Object.assign(new UserCashless(), this.user);
        tempItem.SpriggyBalance = updatedBalance;
        this.user = tempItem;
        this.cd.markForCheck();
      }
    });
  }

  createOrder() {
    this.dialog.open(DialogWalkUpOrdersComponent, {
      width: '500px',
      disableClose: false,
      data: this.user,
    });
  }

  goBack() {
    this._location.back();
  }
}
