<nav-back-button
  smallText="true"
  (navBack)="goBack()"
  text="Go Back"
  class="backButton"
  smallFont="true"
  noPadding="true"
></nav-back-button>

<div *ngIf="currentSchool" class="col-md-8 col-sm-12 page-container">
  <div class="d-flex justify-content-between align-items-center mt-3 mb-3">
    <school-header [title]="currentSchool.Name"></school-header>
    <div class="d-flex justify-content-end buttonContainer">
      <div [ngClass]="{ disableCoverGrey: editMode }"></div>
      <dropdown-button
        class="mr-3"
        [values]="schoolStatusOptions"
        label="Status"
        [currentValue]="currentSchool.BillingStatus"
        (onPress)="UpdateBillingStatus($event)"
        [confirmation]="confirmStatusChange.asObservable()"
        waitForConfirm="true"
      ></dropdown-button>
      <basic-button
        text="Unlink School"
        [buttonStyle]="1"
        (onPress)="unlinkSchool()"
        [disabled]="currentSchool.BillingStatus === 'Churned'"
      ></basic-button>
    </div>
  </div>

  <div class="form-container">
    <form [formGroup]="form" class="form m-4 pt-4 pb-4">
      <input-text
        placeholder="Canteen Fee"
        formControlName="canteenFee"
        inputType="number"
        [error]="canteenFee.invalid ? invalidValueError : null"
        step="0.01"
        min="0"
        max="1"
      ></input-text>
      <input-date
        placeholder="Invoicing Start Date"
        formControlName="startDate"
        [error]="startDate.invalid ? invalidValueError : null"
      ></input-date>
      <input-date
        placeholder="Churned Date"
        formControlName="churnedDate"
        [error]="churnedDate.invalid ? invalidValueError : null"
      ></input-date>
      <input-select-list
        formControlName="internalStatus"
        placeholder="Internal Status"
        [values]="listStatus"
      ></input-select-list>
      <input-text
        placeholder="Special Instructions"
        formControlName="instructions"
        multiline="true"
        [error]="instructions.invalid ? invalidValueError : null"
        
      ></input-text>

      <div>
        <mat-checkbox formControlName="waiveEventOrderFee">Waive Event Order Fee</mat-checkbox>
      </div>
      
      <div>
        <mat-checkbox formControlName="absorbFees">Absorb Fees</mat-checkbox>
      </div>

      <div class="pt-3">
        <div *ngIf="!editMode">
          <basic-button text="Edit" [buttonStyle]="0" (click)="clickEdit()"></basic-button>
        </div>
        <div *ngIf="editMode" class="d-flex">
          <basic-button
            text="Save"
            [buttonStyle]="0"
            (click)="updateInformation(3)"
            [disabled]="form.invalid"
            class="mr-2"
          ></basic-button>
          <basic-button text="Cancel" [buttonStyle]="2" (click)="areYouSureCancel()"></basic-button>
        </div>
      </div>
    </form>
  </div>
</div>
