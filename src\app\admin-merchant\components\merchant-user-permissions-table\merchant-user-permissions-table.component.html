<div class="merchant-section">
  <div [ngClass]="{ disableCoverWhite: disableMode }"></div>
  <div class="details-header">
    <h4>Merchant users and Permissions</h4>
    <basic-button text="Add User" (onPress)="addUserClick()" [buttonStyle]="1"></basic-button>
  </div>

  <hr class="details-divider" />

  <table *ngIf="dataSource && !isListEmpty()" mat-table [dataSource]="dataSource" class="table">
    <ng-container matColumnDef="username">
      <th mat-header-cell *matHeaderCellDef class="header">User Name</th>
      <td mat-cell *matCellDef="let element" class="noBorder">
        <div style="padding-top: 8px; padding-bottom: 8px">
          <h5>{{ element.FirstName }} {{ element.Lastname }}</h5>
          <h6>{{ element.Email }}</h6>
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="menuEditor">
      <th mat-header-cell *matHeaderCellDef class="header">View menu editor</th>
      <td mat-cell *matCellDef="let element" class="noBorder mediumColumn">
        <div class="checkboxWrapper">
          <img
            *ngIf="element.IsMenuEditorAvailable"
            size="24"
            src="assets/icons/black-tick.svg"
            class="checkBox"
          />
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="salesReport">
      <th mat-header-cell *matHeaderCellDef class="header">View sales reports</th>
      <td mat-cell *matCellDef="let element" class="noBorder mediumColumn">
        <div class="checkboxWrapper">
          <img
            *ngIf="element.IsSaleReportsAvailable"
            size="24"
            src="assets/icons/black-tick.svg"
            class="checkBox"
          />
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="viewEvent">
      <th mat-header-cell *matHeaderCellDef class="header">View event management</th>
      <td mat-cell *matCellDef="let element" class="noBorder mediumColumn">
        <div class="checkboxWrapper">
          <img
            *ngIf="element.IsEventManagementAvailable"
            size="24"
            src="assets/icons/black-tick.svg"
            class="checkBox"
          />
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="allowUnprintedOrders">
      <th mat-header-cell *matHeaderCellDef class="header">Allow unprinted orders</th>
      <td mat-cell *matCellDef="let element" class="noBorder mediumColumn">
        <div class="checkboxWrapper">
          <img
            *ngIf="element.IsOrdersNotPrintedReportsAvailable"
            size="24"
            src="assets/icons/black-tick.svg"
            class="checkBox"
          />
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="emailUnprintedOrders">
      <th mat-header-cell *matHeaderCellDef class="header">Email unprinted orders</th>
      <td mat-cell *matCellDef="let element" class="noBorder mediumColumn">
        <div class="checkboxWrapper">
          <img
            *ngIf="element.NotifyOrdersNotPrinted"
            size="24"
            src="assets/icons/black-tick.svg"
            class="checkBox"
          />
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="edit">
      <th mat-header-cell *matHeaderCellDef class="header"></th>
      <td mat-cell *matCellDef="let element" class="noBorder smallColumn noPadding">
        <a
          style="cursor: pointer; float: right"
          routerLink="./{{ selectedMerchant.canteenId }}/editmerchantuser/{{ element.UserId }}"
        >
          <img class="editIcon" src="assets/icons/orange-pencil.svg" alt="edit symbol" />
        </a>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>

  <!-- table row on empty -->
  <div *ngIf="isListEmpty()" class="emptyMessage">No linked users</div>
</div>
