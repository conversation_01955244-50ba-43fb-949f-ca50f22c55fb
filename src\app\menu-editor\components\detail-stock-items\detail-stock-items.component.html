<div class="row rowItems">
  <div class="col-6">
    <h2>Items Linked</h2>
    <div class="cardDefaultCanteen">
      <linked-item-table [tableData]="linkedItems" (remove)="removeItem($event)"></linked-item-table>
    </div>
  </div>
  <div class="col-6">
    <h2>Add Item</h2>

    <div class="cardDefaultCanteen">
      <div *ngIf="listCategories">
        <form *ngIf="form" [formGroup]="form">
          <mat-form-field appearance="outline">
            <mat-label>Category</mat-label>
            <mat-select formControlName="category" required>
              <mat-option *ngFor="let category of listCategories" [value]="category.MenuCategoryId">{{
                category.CategoryName
              }}</mat-option>
            </mat-select>
          </mat-form-field>
        </form>
      </div>

      <category-item-table
        [tableData]="menuItemsToDisplay"
        [selectedCategory]="selectedCategory"
        (addItem)="addItem($event)"
      ></category-item-table>
    </div>
  </div>
</div>
