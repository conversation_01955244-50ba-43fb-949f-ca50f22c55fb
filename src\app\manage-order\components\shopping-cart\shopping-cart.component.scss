@import '../../../../styles/cashless-theme.scss';
@import '../../../../styles/cashless-breakpoints.scss';

.title {
  font-size: 24px;
  vertical-align: center;
}

.itemRow {
  background-color: white;
  border-bottom: 1px solid $grey-3;
  display: flex;

  mat-form-field {
    max-width: 100%;
  }

  .itemName {
    font-size: 20px;
    margin: 0;
    padding: 0;
  }

  .spacerDescription {
    font-size: 30px;
    line-height: 16px;
    font-weight: bold;
  }
}

.totalRow {
  margin-bottom: 24px;
  background-color: white;
  border-bottom: 1px solid $grey-3;
  text-align: right;

  -webkit-box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);

  h5 {
    margin-top: 14px;
    margin-bottom: 14px;
    font-size: 20px;
  }
}

.cartTitle {
  font-size: 18px;
}

.removeFromCart {
  padding-top: 15px;

  & mat-icon {
    cursor: pointer;
  }
}

.noFunds {
  color: $red-1;
}
