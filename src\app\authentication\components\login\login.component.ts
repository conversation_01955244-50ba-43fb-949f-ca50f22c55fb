import { Component, OnInit, <PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { User } from 'firebase/auth';
import firebase from 'firebase/compat/app';
import { timeout } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { environment } from 'src/environments/environment';
import { SPRIGGY_SCHOOLS_TERMS_LINK } from 'src/constants';

import { SpinnerService, UserService, PhoneNumberService } from '../../../sharedServices';
import { UserCashless, Roles, BaseComponent, CreateUserRequest } from 'src/app/sharedModels';

import { trigger, transition, useAnimation } from '@angular/animations';
import { fadeIn } from 'ng-animate';

// ngrx
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/states';
import { SetConnectedUser } from 'src/app/states/user/user.actions';
import { AuthService } from 'src/app/sharedServices/authentication/auth.service';
import { isArray } from 'lodash';
import { FirebaseError } from 'firebase/app';

@Component({
  selector: 'authentication-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  animations: [
    trigger('bounce', [
      transition(
        '* => *',
        useAnimation(fadeIn, {
          // Set the duration to 5seconds and delay to 2seconds
          params: { timing: 0.5, delay: 0 },
        })
      ),
    ]),
  ],
})
export class LoginComponent extends BaseComponent implements OnInit, OnDestroy {
  loginMode: boolean;
  form: FormGroup;
  private userFire: User;
  bounce: any;
  private _offlineSubscription: Subscription;
  isOffline: boolean = false;
  termsLink: string = SPRIGGY_SCHOOLS_TERMS_LINK;
  public errorMessage: string;

  constructor(
    public afAuth: AngularFireAuth,
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: SpinnerService,
    private userService: UserService,
    private authService: AuthService,
    private ngZone: NgZone,
    private phoneNumberService: PhoneNumberService
  ) {
    super();
  }

  ngOnInit() {
    this.route.data.subscribe((data: { login: boolean }) => {
      this.loginMode = data.login;
      this._createForm();
    });

    this.userService.IdentifyUser();
  }

  ngOnDestroy() {
    if (this._offlineSubscription) {
      this._offlineSubscription.unsubscribe();
    }
  }

  ////////////////////////////////////////
  // Form
  ////////////////////////////////////////
  get email() {
    return this.form.get('email');
  }

  get password() {
    return this.form.get('password');
  }

  get firstname() {
    return this.form.get('firstname');
  }

  get lastname() {
    return this.form.get('lastname');
  }

  get mobile() {
    return this.form.get('mobile');
  }

  get termsConditions() {
    return this.form.get('termsConditions');
  }

  get ageConditions() {
    return this.form.get('ageConditions');
  }

  getErrorMessageEmail() {
    return this.form.get('email').hasError('required')
      ? 'You must enter a value'
      : this.form.get('email').hasError('email')
      ? 'Not a valid email'
      : '';
  }

  getErrorMessagePassword() {
    if (this.password.hasError('required')) {
      return 'You must enter a value';
    } else if (this.password.hasError('minlength')) {
      return 'Must be at least 14 characters';
    } else {
      return '';
    }
  }

  getErrorMessageFirstname() {
    return this.form.get('firstname').hasError('required') ? 'You must enter a value' : '';
  }

  getErrorMessageLastname() {
    return this.form.get('lastname').hasError('required') ? 'You must enter a value' : '';
  }

  getErrorMessageMobile() {
    if (this.form.get('mobile').hasError('required')) {
      return 'You must enter a value';
    }

    return 'You must enter a valid mobile number';
  }

  getErrorMessageTerms() {
    return this.form.get('termsConditions').hasError('required') ||
      this.form.get('ageConditions').hasError('required')
      ? 'You must agree to the Terms to continue'
      : '';
  }

  ////////////////////////////////////////
  // Log in / Sign up
  ////////////////////////////////////////
  onSubmit() {
    if (this.form.valid) {
      this.clearApiError();

      // Start spinner
      this.spinnerService.start();

      if (this.loginMode) {
        this.signIn();
      } else {
        this.signUp();
      }
    }
  }

  // Call Firebase to sign in
  private signIn(): void {

    this.afAuth
      .signInWithEmailAndPassword(this.email.value, this.password.value)
      .then(fuser => {
        this.signInSucceeded(fuser);
      })
      .catch(error => {
        this.signInFailed(error);
      });
  }

  // Register a new User
  private signUp(): void {

    // Create all accounts as parents for the time being
    let request: CreateUserRequest = new CreateUserRequest();
    request.Email = this.email.value;
    request.Password = this.password.value;
    request.Role = Roles.Parent;
    request.FirstName = this.firstname.value;
    request.Lastname = this.lastname.value;
    request.Mobile = this.phoneNumberService.serverMobileNumber(this.mobile.value);

    this.userService.RegisterUser(request).subscribe({
      next: (response: UserCashless) => {
        this.signIn();
      },
      error: error => {
        this.signUpFailed(error);
      },
    });
  }

  // Sign in failed
  private signInFailed(error: FirebaseError): void {

    if(error.code == 'auth/invalid-password' || error.code == 'auth/wrong-password' || error.code == 'auth/invalid-credential' || error.code  == 'auth/user-not-found'){
      this.errorMessage = 'Sorry this email and password combination was incorrect'
    }else{
       this.errorMessage = 'Something has gone wrong...'
    }

    this.spinnerService.stop();
  }

  // Sign up failed
  // - 4xx: Failed validations or authorisations
  // - 5xx: API errors
  private signUpFailed(error: any): void {
    this.spinnerService.stop();
    this.errorMessage = this.getApiError(error);
  }

  // Sign in an existing user
  private signInSucceeded(fuser: firebase.auth.UserCredential): void {
    // console.log("signInSucceeded(): Sign in user success - " + JSON.stringify(fuser));

    this._ClearStorageOnNewVersion();
    this.userFire = null;
    this.userFire = fuser.user;
    this.userFire.getIdToken().then(token => {
      this.authService.SetToken(token);
      this._GetUser();
    });

    // console.log("signInSucceeded(): Clean up after sign in");

    // Will we get to these statements once a redirect is issued?
    this.spinnerService.stop();
    this.clearApiError();
  }

  private _GetUser() {
    this.userService
      .GetUserByFireBaseId(this.userFire.uid)
      .pipe(timeout(5000))
      .subscribe({
        next: (userResponse: UserCashless) => {
          if (userResponse) {
            this.userService.SetUserConnected(userResponse);
            this._RedirectUser(userResponse);
          }
        },
        error: error => {
          this.spinnerService.stop();
          this.handleErrorFromService(error);
        },
      });
  }

  private _ClearStorageOnNewVersion() {
    let savedVersion = localStorage.getItem('appVersion');

    if (savedVersion != environment.AppVersion) {
      localStorage.clear();
      localStorage.setItem('appVersion', environment.AppVersion);
    }
  }

  private _RedirectUser(user: UserCashless) {
    if (user.Role == Roles.Admin) {
      this.ngZone.run(() => this.router.navigate(['/admin']));
    } else if (user.Role == Roles.Canteen) {
      this.ngZone.run(() => this.router.navigate(['/canteen']));
    } else {
      this.ngZone.run(() => this.router.navigate(['/family']));
    }
  }

  private _createForm() {
    if (this.loginMode) {
      this.form = new FormGroup({
        email: new FormControl('', [Validators.required, Validators.email]),
        password: new FormControl('', [Validators.required]),
      });
    } else {
      this.form = new FormGroup({
        email: new FormControl('', [Validators.required, Validators.email]),
        password: new FormControl('', [Validators.required, Validators.minLength(14)]),
        firstname: new FormControl('', [Validators.required]),
        lastname: new FormControl('', [Validators.required]),
        mobile: new FormControl('', [Validators.required, Validators.minLength(12)]),
        termsConditions: new FormControl(true, [Validators.requiredTrue]),
        ageConditions: new FormControl(true, [Validators.requiredTrue]),
      });
    }
  }

  ////////////////////////////////////////
  // Get Text
  ////////////////////////////////////////
  GetTextSubmit(): string {
    if (this.loginMode) {
      return 'Sign In';
    } else {
      return 'Sign Up';
    }
  }

  formatMobileInput() {
    let res = this.phoneNumberService.aussieMobileBranded(this.mobile.value);
    this.mobile.setValue(res);
  }

  ////////////////////////////////////////
  // API error handling                 //
  //                                    //
  // TODO - Move to a shared component  //
  ////////////////////////////////////////

  // Initialiate Schools API error handling
  private clearApiError(): void {
    this.errorMessage = null;
  }

  // // Process Schools API errors
  // private processApiError(error: any): void {
  //   this.error = error;
  // }

  // Fetch the first error message returned by the API. Expecting a structure that looks like this:
  // {
  //    "errors": {
  //      "Email": ["There's already an account with the email address: <EMAIL>"]
  //    },
  //    "type": "https://www.rfc-editor.org/rfc/rfc7231#section-6.5.1",
  //    "title": "One or more validation errors occurred.",
  //    "status": 400
  // }
  public getApiError(error: any): string {
    if (!error) {
      return null;
    }

    // console.log("getApiError(): Fetching API error from - " + JSON.stringify(this.error));

    if (error.errors) {
      let messages = this.getErrorMessages(error.errors);

      // Fetch the first error
      if (isArray(messages) && messages.length > 0) {
        return messages[0];
      }
    }

    // Old format
    if (error.message) {
      return error.message;
    }

    if (error.title) {
      return error.title;
    }

    // Return a generic message
    return 'Something has gone wrong...';
  }

  // Get all the error messages in a structure that looks like this:
  // {
  //    "errors": {
  //      "Email": ["There's already an account with the email address: <EMAIL>"]
  //    },
  //    "type": "https://www.rfc-editor.org/rfc/rfc7231#section-6.5.1",
  //    "title": "One or more validation errors occurred.",
  //    "status": 400
  // }
  private getErrorMessages(errors: any): Array<string> {
    if (!errors) {
      return;
    }

    let messages: Array<string> = [];
    Object.entries(errors).forEach(err => {
      if (isArray(err) && err.length > 1 && isArray(err[1]) && err[1].length > 0) {
        // Expecting err = ["FieldName" : ["Some sort of error message"]]
        messages.push(err[1][0]);
      }
    });

    return messages;
  }
}
