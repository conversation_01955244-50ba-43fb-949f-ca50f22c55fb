import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { environment } from 'src/environments/environment';

// Models
import {
  BaseComponent,
  Canteen,
  ConfirmModal,
  ImageUrlEnum,
  ItemCategory,
  PopupCategoryModel,
  MenuItem,
} from 'src/app/sharedModels';

// state
import { Subscription } from 'rxjs';
import { Store, select } from '@ngrx/store';
import { CanteenState } from 'src/app/states';
import { selectedCanteen } from 'src/app/states/canteen/canteen.selectors';

// service
import { MenuEditorApiService, SpinnerService } from 'src/app/sharedServices';

// component
import { PopupCategoryFormComponent } from './popup-category-form.component';
import { DialogConfirmComponent } from 'src/app/shared/components/dialog-confirm/dialog-confirm.component';

@Component({
  selector: 'app-manage-categories',
  templateUrl: './manage-categories.component.html',
  styleUrls: ['./manage-categories.component.scss'],
})
export class ManageCategoriesComponent extends BaseComponent implements OnInit, OnDestroy {
  listCategories: ItemCategory[] = [];
  private subscription: Subscription;
  private canteen: Canteen;
  listItems: MenuItem[] = [];

  constructor(
    private _location: Location,
    private route: ActivatedRoute,
    public dialog: MatDialog,
    private menueditorService: MenuEditorApiService,
    private spinnerService: SpinnerService,
    private store: Store<{ canteen: CanteenState }>
  ) {
    super();
  }

  ngOnInit() {
    this.route.data.subscribe(data => {
      this.listCategories = data['categories'];
    });

    this.subscription = this.store.pipe(select(selectedCanteen)).subscribe((selectedCanteen: Canteen) => {
      this.canteen = selectedCanteen;
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  GoBackClick() {
    this._location.back();
  }

  NewCategoryClick() {
    let request = new ItemCategory();
    request.SortOrder = 1;
    this.OpenDialog(request);
  }

  ArchiveClicked(cat: ItemCategory) {
    let data = new ConfirmModal();
    // Check here it items exist in category before allowing delete action.
    this.menueditorService.GetItemsByCategoryIdAPI(cat.MenuCategoryId).subscribe({
      next: (response: MenuItem[]) => {
        this.listItems = response;
        if (this.listItems.length > 0) {
          data.Title = 'Delete Category';
          data.Text =
            'Deleting this category is not possible because there are still items in this category. Please remove all items and try again.';
          data.ConfirmButton = 'Ok';
        } else {
          data.Title = 'Delete Category';
          data.Text =
            'Deleting this category will inactivate the category permanently and can not be undone. Proceed?';
          data.CancelButton = 'No';
          data.ConfirmButton = 'Yes';
        }
        const dialogRef = this.dialog.open(DialogConfirmComponent, {
          width: '500px',
          disableClose: true,
          data: data,
        });

        dialogRef.afterClosed().subscribe(result => {
          if (result && this.listItems.length == 0) {
            this.ArchiveClickConfirmed(cat);
          }
        });
      },
      error: error => {
        this.handleErrorFromService(error);
      },
    });
  }

  ArchiveClickConfirmed(cat: ItemCategory) {
    this.spinnerService.start();
    this.menueditorService.ArchiveCategoryAPI(cat.MenuCategoryId).subscribe({
      next: (response: any) => {
        this.RefreshCategories();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  OpenDialog(category: ItemCategory) {
    let data = new PopupCategoryModel();
    data.Canteen = this.canteen;
    data.SelectedCategory = category;

    // open dialog
    let dialogRef;
    dialogRef = this.dialog.open(PopupCategoryFormComponent, {
      width: '1000px',
      maxHeight: '95vh',
      disableClose: false,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // refresh categories
        this.RefreshCategories();
      }
    });
  }

  RefreshCategories() {
    this.spinnerService.start();
    this.menueditorService.GetCategoriesForEditorByCanteenIdAPI(this.canteen.CanteenId).subscribe({
      next: (response: ItemCategory[]) => {
        this.listCategories = response;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }
}
