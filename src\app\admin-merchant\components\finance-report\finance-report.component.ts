import { KeyValue } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { BaseFormComponent } from 'src/app/schools-form/components';
import * as moment from 'moment';
import * as _ from 'lodash';

// service
import { MerchantService, SpinnerService } from 'src/app/sharedServices';
import { InvoiceExportRequest, InvoiceType, InvoiceTypeEnum } from 'src/app/sharedModels';
import { UNIVERSAL_DATE_FORMAT } from 'src/app/utility';

interface SelectedWeekAndYear {
  selectedWeek: number;
  selectedYear: number;
}

enum WeekDateType {
  start,
  end,
}

@Component({
  selector: 'finance-report',
  templateUrl: './finance-report.component.html',
  styleUrls: ['./finance-report.component.scss'],
})
export class FinanceReportComponent extends BaseFormComponent implements OnInit {
  selectWeekValues: KeyValue<string, string>[] = [];
  formGroupDates: FormGroup;
  invalidValueError: string = 'Invalid value entered';

  constructor(
    private router: Router,
    private merchantService: MerchantService,
    private spinnerService: SpinnerService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getSelectWeekValues();

    // setup form for weekly export
    this.formGroup = new FormGroup({
      week: new FormControl(this.selectWeekValues[0].key),
    });

    //set form for custom date export
    this.formGroupDates = new FormGroup({
      startDate: new FormControl([Validators.required]),
      endDate: new FormControl([Validators.required]),
    });
  }

  goBackClick() {
    this.router.navigate(['./admin/merchants']);
  }

  /**
   * Get the values / text to display in the select list
   */
  getSelectWeekValues() {
    let endCurrentWeek = moment().endOf('week');

    this.selectWeekValues.push({
      key: this.getWeekYearNumber(endCurrentWeek),
      value: this.getWeekText(endCurrentWeek),
    });

    for (let index = 0; index < 80; index++) {
      endCurrentWeek = endCurrentWeek.subtract(1, 'week');
      this.selectWeekValues.push({
        key: this.getWeekYearNumber(endCurrentWeek),
        value: this.getWeekText(endCurrentWeek),
      });
    }
  }

  /**
   * Get the value to use in the select list
   * @param date
   * @returns
   */
  private getWeekYearNumber(date: moment.Moment): string {
    return date.format('w-YYYY');
  }

  /**
   * Get the text to display in the select list
   * @param date
   * @returns
   */
  private getWeekText(date: moment.Moment): string {
    const endDate = _.cloneDeep(date);
    const startDate = _.cloneDeep(date);
    const textStart = startDate.subtract(1, 'week').add(1, 'day').format('D-MMM-YYYY');
    const textEnd = endDate.format('D-MMM-YYYY');

    return textStart + ' to ' + textEnd;
  }

  getInvoice() {
    this._downloadReport(InvoiceType.Invoice);
  }

  getSettlement() {
    this._downloadReport(InvoiceType.Settlement);
  }

  getRevenue() {
    if (this.formGroupDates.invalid) {
      return;
    }
    this._downloadReport(InvoiceType.Revenue);
  }

  /**
   * Download the report from the API
   * @param type
   */
  private _downloadReport(type: InvoiceType) {
    let filename = type + '.csv';

    const request: InvoiceExportRequest = this.getRequest(type);

    this.spinnerService.animatedStart();
    this.merchantService.GetInvoice(request).subscribe({
      next: res => {
        var downloadURL = window.URL.createObjectURL(res);
        var link = document.createElement('a');
        link.href = downloadURL;
        link.download = filename;
        link.click();
        this.spinnerService.animatedStop();
      },
      error: error => {
        this.spinnerService.animatedStop();
        this.ErrorModal('Something went wrong', error);
      },
    });
  }

  getRequest(type: InvoiceType): InvoiceExportRequest {
    const selectedDateValues: SelectedWeekAndYear = this.getSelectedWeekAndYear();

    return {
      exportType: InvoiceTypeEnum[type],
      startDate: this.getRequestDate(type, selectedDateValues, WeekDateType.start),
      endDate: this.getRequestDate(type, selectedDateValues, WeekDateType.end),
    };
  }

  getSelectedWeekAndYear(): SelectedWeekAndYear {
    const weekYearKeyValueData: string = this.formGroup.get('week').value;
    const weekYearArray: string[] = weekYearKeyValueData.split('-');
    const selectedWeek: number = +weekYearArray[0];
    const selectedYear: number = +weekYearArray[1];
    return { selectedWeek, selectedYear };
  }

  getRequestDate(type: InvoiceType, selectedDateValues: SelectedWeekAndYear, weekType: WeekDateType): string {
    const momentValue =
      type === InvoiceType.Revenue
        ? this.getRevenueDate(weekType)
        : this.getWeekDate(selectedDateValues, weekType);

    return momentValue.format(UNIVERSAL_DATE_FORMAT);
  }

  getRevenueDate(weekType: WeekDateType): moment.Moment {
    return weekType === WeekDateType.start ? moment(this.startDate.value._d) : moment(this.endDate.value._d);
  }

  getWeekDate(selectedDateValues: SelectedWeekAndYear, weekType: WeekDateType): moment.Moment {
    const year = selectedDateValues.selectedYear.toString(); // year value needs to be string
    const week = selectedDateValues.selectedWeek; // week value needs to be int
    const momentValue = moment(year).weeks(week);
    return weekType === WeekDateType.start ? momentValue.startOf('week') : momentValue.endOf('week');
  }

  get startDate() {
    return this.formGroupDates.get('startDate');
  }

  get endDate() {
    return this.formGroupDates.get('endDate');
  }
}
