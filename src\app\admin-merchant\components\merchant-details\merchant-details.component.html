<div class="merchant-section">
  <div [ngClass]="{ disableCoverWhite: disableMode && !editDetailsMode }"></div>
  <div class="details-header">
    <h4>Merchant Details</h4>
    <button *ngIf="!editDetailsMode" class="editBtn" (click)="triggerEdit()">
      <img class="editIcon" src="assets/icons/orange-pencil.svg" alt="edit symbol" />
    </button>
  </div>

  <hr class="details-divider" />

  <div class="top-margin">
    <div *ngIf="!editDetailsMode && merchantDetails">
      <ul>
        <li><strong>Name: </strong>{{ merchantDetails.name }}</li>
        <li><strong>Friendly Name: </strong>{{ merchantDetails.friendlyName }}</li>
        <li><strong>Type: </strong>{{ merchantDetails.type }}</li>
        <li><strong>Phone: </strong>{{ displayLandLine(merchantDetails.phone) }}</li>
      </ul>
    </div>

    <!-- Edit Section -->
    <div *ngIf="editDetailsMode && formGroup" class="top-margin">
      <form class="cashlessForm" [formGroup]="formGroup">
        <div class="editInput">
          <input-text
            placeholder="Name"
            formControlName="name"
            [error]="name.invalid ? this.invalidValueError : null"
          ></input-text>
          <input-text
            placeholder="Friendly Name"
            formControlName="friendlyName"
            [error]="friendlyName.invalid ? this.invalidValueError : null"
          ></input-text>
          <input-text placeholder="Type" formControlName="type"></input-text>
          <input-text
            placeholder="Phone"
            formControlName="phone"
            [error]="phone.invalid ? this.invalidValueError : null"
          ></input-text>
        </div>
      </form>

      <div class="editBtnContainer">
        <button class="saveBtn" (click)="saveMerchantDetails()" [disabled]="isFormDisabled()">Save</button>
        <button class="cancelBtn" (click)="cancelEditPopup()">Cancel</button>
      </div>
    </div>
  </div>
</div>
