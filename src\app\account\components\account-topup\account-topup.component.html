<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <nav-back-button (navBack)="backClicked()" text="Account"></nav-back-button>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <p class="accountTitle">Balance</p>
    </div>
  </div>

  <div class="row cardDefaultParent cardBalance">
    <div class="col-12 col-md-4 col-lg-6">
      <p class="balanceAmount align-items-center">${{ accountBalance | number : '1.2-2' }}</p>
    </div>
    <div class="col-12 col-md-8 col-lg-6">
      <payment-top-up-choices
        (choiceChanged)="TopUpAmountChanged($event)"
        [isNestedTopUp]="false"
      ></payment-top-up-choices>
    </div>
  </div>

  <div *ngIf="topUpAmount" class="row cardDefaultParent justify-content-center cardTopUp">
    <div class="col-12 col-md-8 col-lg-6">
      <top-up-form
        (PaymentSucceed)="backClicked()"
        [topUpAmount]="topUpAmount"
        [userBalance]="accountBalance"
      ></top-up-form>
    </div>
  </div>
</div>
