@import '../../../../styles/cashless-theme.scss';

.accountTable {
  width: 100%;
}

.searchResults {
  padding-bottom: 60px;
  position: relative;
  display: flex;
}

.chevron {
  height: 14px;
  width: 8px;
}
.result-title {
  font-size: 16px;
  font-weight: bold;
  margin: 0;
  color: $charcoal-1;
}

.result-subtitle {
  font-size: 12px;
  margin: 0;
  color: $charcoal-1;
}

.result-details {
  width: 70%;
  padding: 5px 20px 40px 20px;
  background-color: white;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  position: relative;
  z-index: 1;
  overflow-y: scroll;
  height: 80vh;
}

.tabContainer {
  padding: 0;
  overflow-y: scroll;
  height: 80vh;
  width: 100%;
}

.tabContainerOpen {
  width: 30%;
}

.mat-mdc-table {
  box-shadow: none;
}

.mat-mdc-row {
  background-color: white;
  margin: 0;
  width: 100%;
  height: auto;
}

.mat-mdc-cell {
  border-bottom: 1px solid $grey-6;
  padding: 2px 0;
  margin: 0;
}

.result-tab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 14px;
}

.result-tab:hover {
  background-color: $orange-7;
  cursor: pointer;
}

.result-tab-inner {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.selectedTab {
  background-color: $grey-6;
}

.selectedTabText {
  color: $orange-3;
}

.disableCoverTab {
  position: absolute;
  background-color: rgb(255, 255, 255);
  opacity: 0.5;
  width: 30%;
  height: 80vh;
  z-index: 100;
}

.disableCoverGrey {
  position: absolute;
  background-color: $grey-4;
  opacity: 0.5;
  width: 100%;
  height: 100%;
  z-index: 100;
}
