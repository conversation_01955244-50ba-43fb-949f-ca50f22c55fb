import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

// Services
import { CategoriesResolver } from '../sharedServices';

// Components
import {
  AccountSettingsComponent,
  SchoolSettingsComponent,
  AdminSettingsComponent,
  MenuSettingsComponent,
  ManageCategoriesComponent,
} from './components';

const routes: Routes = [
  {
    path: '',
    component: AccountSettingsComponent,
  },
  {
    path: 'school',
    component: SchoolSettingsComponent,
  },
  {
    path: 'admin',
    component: AdminSettingsComponent,
  },
  {
    path: 'menu',
    component: MenuSettingsComponent,
  },
  {
    path: 'menu/categories',
    component: ManageCategoriesComponent,
    resolve: { categories: CategoriesResolver },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CanteenSettingsRoutingModule {}
