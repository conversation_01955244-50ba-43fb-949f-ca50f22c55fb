import { ChangeDetectionStrategy, Component, Input, OnInit, ChangeDetectorRef } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ArchiveParentAccountComponent } from 'src/app/shared-tools/components';
import { DialogConfirmComponent, DialogResultComponent } from 'src/app/shared/components';
// models
import {
  ArchiveUserRequest,
  BaseComponent,
  ConfirmModal,
  ResultDialogData,
  Roles,
  UserCashless,
} from 'src/app/sharedModels';

// services
import { AdminService, SpinnerService, UserService } from 'src/app/sharedServices';
import { ReconciliationService } from 'src/app/sharedServices/reconciliation/reconciliation.service';
import { DialogUserDetailsFormComponent } from '../dialog-user-details-form/dialog-user-details-form.component';
import { DialogUserReconciliationComponent } from '../dialog-user-reconciliation-component/dialog-user-reconciliation.component';

@Component({
  selector: 'user-management-parent-details',
  templateUrl: './parent-details.component.html',
  styleUrls: ['./parent-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParentDetailsComponent extends BaseComponent implements OnInit {
  @Input() parent: UserCashless;
  role: string;
  roles = Roles;

  constructor(
    private adminService: AdminService,
    private spinnerService: SpinnerService,
    private userService: UserService,
    public dialog: MatDialog
  ) {
    super();
  }

  ngOnInit(): void {
    this.role = this.adminService.GetRoleText(this.parent.Role.toString());
  }

  isParent() {
    return Roles[this.role] === Roles.Parent;
  }

  //////////////////////////// Get spriggy account ID
  getSpriggyAccountId() {
    this.spinnerService.start();

    this.userService.updateSpriggyAccountId(this.parent.UserId).subscribe({
      next: response => {
        window.location.reload();
      },
      error: error => {
        let errorMessageArray = this.GetErrorMessage(error);
        this.errorGettingSpriggyAccountID(errorMessageArray[0]);
        this.spinnerService.stop();
      },
    });
  }

  errorGettingSpriggyAccountID(error: string) {
    let data = new ConfirmModal();
    data.Title = 'Getting Spriggy Account Id was unsuccessful';
    data.Text = typeof error === 'string' ? `${error}` : 'Please try again.';
    data.ConfirmButton = 'Ok';

    this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });
  }

  ////////////////////////// Archive Parent
  canArchiveParent() {
    return this.parent.Role == Roles.Parent && this.parent.IsActive;
  }

  reconcileUser() {
    let data = {
      SpriggyBalance: this.parent.SpriggyBalance,
      UserId: this.parent.UserId,
    };

    const ref = this.dialog.open(DialogUserReconciliationComponent, {
      width: '500px',
      disableClose: false,
      data: data,
    });
  }

  archiveClicked(): void {
    let request = new ArchiveUserRequest();
    request.UserId = this.parent.UserId;

    let dialogRef = this.dialog.open(ArchiveParentAccountComponent, {
      width: '500px',
      disableClose: false,
      data: request,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        window.location.reload();
      }
    });
  }

  editUser() {
    this.dialog.open(DialogUserDetailsFormComponent, {
      width: '500px',
      disableClose: false,
      data: this.parent,
    });
  }
}
