import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

// components
import { AccountComponent } from '../account/components/account/account.component';
import { AdminDashboardComponent, EngineerDashboardComponent } from './components';
import { NavBarV2Component } from '../navigation/components/nav-bar-v2/nav-bar-v2.component';

// Services
import { ListCanteensResolver, ListNoticeResolver } from '../sharedServices';
import { AdminNoticeComponent } from './components/admin-notice/admin-notice.component';

// resolvers
import { DasboardErrorsResolver } from './resolvers/dasboard-errors.resolver';
import { eventManagementRoutes } from '../event-management/event-management.routes';

const routes: Routes = [
  {
    path: '',
    component: NavBarV2Component,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'users',
      },
      {
        path: 'dashboard',
        component: EngineerDashboardComponent,
      },
      {
        path: 'dashboard2',
        component: AdminDashboardComponent,
        resolve: {
          dashboard: DasboardErrorsResolver,
        },
      },
      {
        path: 'demo',
        loadChildren: () =>
          import('../school-components-demo/school-components-demo.module').then(
            m => m.SchoolComponentsDemoModule
          ),
      },
      {
        path: 'order',
        loadChildren: () => import('../manage-order/manage-order.module').then(m => m.ManageOrderModule),
      },
      {
        path: 'schools',
        loadChildren: () => import('../admin-schools/admin-schools.module').then(m => m.AdminSchoolsModule),
      },
      {
        path: 'merchants',
        loadChildren: () =>
          import('../admin-merchant/admin-merchant.module').then(m => m.AdminMerchantModule),
      },
      {
        path: 'users',
        loadChildren: () =>
          import('../admin-users-management/admin-users-management.module').then(
            m => m.AdminUsersManagementModule
          ),
        // component: UserDetailsPageComponent,
        // resolve: { user: UserDetailsResolver, transactionData: TransactionHistoryResolver }
      },
      {
        path: 'notice',
        component: AdminNoticeComponent,
        resolve: { notices: ListNoticeResolver },
      },
      {
        path: 'account',
        component: AccountComponent,
      },
      {
        path: 'events',
        loadChildren: () => eventManagementRoutes,
        resolve: { merchants: ListCanteensResolver }, 
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminRoutingModule {}
