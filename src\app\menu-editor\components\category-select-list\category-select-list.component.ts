import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { SharedToolsModule } from 'src/app/shared-tools/shared-tools.module';
import { CategoryEditor } from 'src/app/sharedModels';

@Component({
  selector: 'category-select-list',
  templateUrl: './category-select-list.component.html',
  styleUrls: ['./category-select-list.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    SharedToolsModule,
    MatInputModule,
    MatSelectModule
  ]
})
export class CategorySelectListComponent{
  @Input() listCategories: CategoryEditor[];
  @Output() categorySelected: EventEmitter<number> = new EventEmitter();

  valuechanged($event: any){
    this.categorySelected.emit($event.value);
  }
}
