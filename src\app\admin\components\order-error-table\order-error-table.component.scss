@import 'src/styles/schools-table';

.message-text {
  font-size: 12px;
  line-height: 16px;
}

.link {
  color: $link-default;
  text-decoration: none;
  cursor: pointer;
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
.error-table ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background-color: $text-dark-strong;
}
/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
.error-table ::ng-deep .mat-mdc-checkbox.mat-accent.mat-checkbox-indeterminate .mat-checkbox-background {
  background-color: $text-dark-strong;
}

.selectedRow {
  background-color: $action-secondary-hovered;
}
