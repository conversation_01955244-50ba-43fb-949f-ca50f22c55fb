import { Dictionary } from '@ngrx/entity';
import moment from 'moment';
import { CartItem, CartOption, RefinedOrder, RefinedSelectedOption } from 'src/app/sharedModels';
import { formatDateToUniversal } from 'src/app/utility';
import * as _ from 'lodash';
import { GetCustomMenuName } from 'src/app/sharedServices/menu/menu-custom-name';

export function ConvertOrderItemToCartType(
  menuCutOffDateTime: string,
  order: RefinedOrder,
  index: number = 0,
  studentFirstName: string,
  date: string = null
): CartItem {
  const cartDate = date ? date : order.OrderDate;
  const orderItem = order.Items[index];
  const menuName = GetCustomMenuName(order.MenuType, order.MenuName);

  return {
    menuItemId: orderItem.MenuItemId,
    quantity: orderItem.Quantity,
    itemPriceIncGst: orderItem.ItemPriceIncGst,
    name: orderItem.Name,
    date: new Date(formatDateToUniversal(cartDate)),
    studentId: order.StudentId,
    studentName: studentFirstName,
    schoolId: order.SchoolId,
    menuType: order.MenuType,
    menuId: order.MenuId,
    menuName: menuName,
    menuCutOffDateTime,
    canteenId: order.CanteenId,
    itemCartId: index + moment().unix(),
    selectedOptions: getSelectedOptions(orderItem.SelectedOptions),
  };
}

function getSelectedOptions(selectedOptions: RefinedSelectedOption[]): CartOption[] {
  if (selectedOptions?.length === 0) {
    return [];
  }

  return selectedOptions.map(option => {
    return {
      menuItemOptionId: option.MenuItemOptionId,
      optionName: option.OptionName,
      optionCost: option.OptionCost,
      parentOptionId: option.MenuItemOptionsCategoryId,
    };
  });
}

export function GetGroupedShoppingCart(cartItems: CartItem[]): CartItem[][] {
  const groupedCartItems: Dictionary<CartItem[]> = SortCartItems(cartItems);
  return Object.values(groupedCartItems).map((cartArray: CartItem[]) => cartArray);
}

function SortCartItems(items: CartItem[]): Dictionary<CartItem[]> {
  return _.groupBy(items, item => [
    item.studentId,
    item.menuId,
    item.menuType,
    formatDateToUniversal(item.date),
    item.menuName,
  ]);
}
