import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MerchantLinkedSchoolsTableComponent } from './merchant-linked-schools-table.component';

describe('MerchantLinkedSchoolsTableComponent', () => {
  let component: MerchantLinkedSchoolsTableComponent;
  let fixture: ComponentFixture<MerchantLinkedSchoolsTableComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [MerchantLinkedSchoolsTableComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MerchantLinkedSchoolsTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
