<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <form [formGroup]="formGroup">
        <input-search
          placeholder="Search Users"
          formControlName="search"
          [error]="null"
          (searchEvent)="searchClicked()"
        ></input-search>
      </form>
    </div>
  </div>
  <div class="row">
    <div class="col-12 mt-2 d-flex align-items-center justify-content-center" *ngIf="showSpinner">
      <app-spinner [manual]="true"></app-spinner>
    </div>
    <div class="col-12 mt-2" *ngIf="!showSpinner" style="padding-bottom: 60px">
      <search-users-table
        [data]="users"
        [selectedPage]="searchValue.PageIndex"
        [numberRows]="searchValue.NumberRows"
        (pageChanged)="pageChanged($event)"
      ></search-users-table>
    </div>
  </div>
</div>
