<div class="container-fluid">
  <!-- children list -->
  <div class="row d-none d-md-block">
    <div class="col-12 childrenListCol">
      <p class="titleCreateOrders">Create and manage orders for</p>
      <children-list></children-list>
    </div>
  </div>

  <ng-container *ngIf="selectChild">
    <!-- announcements -->
    <family-announcements [announcements]="activeAnnouncements"></family-announcements>

    <div class="row">
      <div class="col-12 noPadding">
        <mat-card appearance="outlined" class="SuppliesOrder">
          <mat-card-content class="scrolling-horizontal-wrapper">
            <table class="table" [@bounce]="bounce">
              <tr>
                <td>
                  <div
                    class="blocSupplies"
                    [ngClass]="{ selected: currentTab == 'Canteen' }"
                    (click)="tabClick('Canteen')"
                  >
                    <span class="icon icon-canteen"></span>
                    <p class="suppliesTextMain hasImg">Canteen Orders</p>
                  </div>
                </td>
                <td>
                  <div
                    class="blocSupplies"
                    [ngClass]="{ selected: currentTab == 'Event' }"
                    (click)="tabClick('Event')"
                  >
                    <p class="suppliesTextMain">Events</p>
                  </div>
                </td>
                <td>
                  <div
                    class="blocSupplies"
                    [ngClass]="{ selected: currentTab == 'Uniform' }"
                    (click)="tabClick('Uniform')"
                  >
                    <span class="icon icon-uniform"></span>
                    <p class="suppliesTextMain hasImg">Uniform Shop</p>
                  </div>
                </td>
                <td class="tdDesktop">
                  <div class="comingSoon">
                    <p class="suppliesTextComingSoon"></p>
                  </div>
                </td>
                <td class="tdDesktop">
                  <div class="comingSoon">
                    <p class="suppliesTextComingSoon"></p>
                  </div>
                </td>
                <td class="tdDesktop">
                  <div class="comingSoon">
                    <p class="suppliesTextComingSoon"></p>
                  </div>
                </td>
              </tr>
            </table>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <div *ngIf="selectChild.NeedToUpdateClass">
      <app-warning
        title="Class inactive"
        description="Your child's class is no longer active. Please update your child's class in the app before making an order."
      ></app-warning>
    </div>

    <div *ngIf="currentTab == 'Event' && !selectChild.NeedToUpdateClass">
      <!-- Events order  -->
      <div class="row titleOrder">
        <div class="col-12 col-sm-6">
          <p class="titleBlock">Events</p>
        </div>
      </div>

      <div class="row justify-content-center backgroundEvents">
        <div class="col-12 col-sm-6">
          <family-list-events [listEvents]="eventListData"></family-list-events>
        </div>
      </div>
    </div>

    <!-- Uniform Shop -->
    <ng-container *ngIf="currentTab == 'Uniform' && !selectChild.NeedToUpdateClass">
      <div class="row titleOrder">
        <div class="col-12 col-sm-6">
          <p class="titleBlock">Uniform Shop</p>
        </div>
      </div>

      <family-uniform [selectedChild]="selectChild" [hasUniformMenu]="hasUniformMenu"></family-uniform>
    </ng-container>

    <!-- Canteen -->
    <ng-container *ngIf="currentTab == 'Canteen' && !selectChild.NeedToUpdateClass">
      <family-canteen-display [orderTableData]="groupedOrderTableData"> </family-canteen-display>
    </ng-container>
  </ng-container>
</div>
