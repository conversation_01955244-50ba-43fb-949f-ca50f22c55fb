<div *ngIf="dataSource.data?.length > 0; else noResults">
  <table mat-table class="showRowHover" [dataSource]="dataSource">
    <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef>User Id</th>
      <td mat-cell *matCellDef="let element">{{ element.UserId }}</td>
    </ng-container>

    <ng-container matColumnDef="firstname">
      <th mat-header-cell *matHeaderCellDef>First name</th>
      <td mat-cell *matCellDef="let element">{{ element.FirstName }}</td>
    </ng-container>

    <ng-container matColumnDef="lastname">
      <th mat-header-cell *matHeaderCellDef>Last name</th>
      <td mat-cell *matCellDef="let element">{{ element.Lastname }}</td>
    </ng-container>

    <ng-container matColumnDef="school">
      <th mat-header-cell *matHeaderCellDef>School</th>
      <td mat-cell *matCellDef="let element">{{ element.SchoolName }}</td>
    </ng-container>

    <ng-container matColumnDef="class">
      <th mat-header-cell *matHeaderCellDef>Class</th>
      <td mat-cell *matCellDef="let element">{{ element.ClassName }}</td>
    </ng-container>

    <ng-container matColumnDef="role">
      <th mat-header-cell *matHeaderCellDef>Role</th>
      <td mat-cell *matCellDef="let element">{{ getRoleText(element.Role) }}</td>
    </ng-container>

    <ng-container matColumnDef="Mobile">
      <th mat-header-cell *matHeaderCellDef>Phone</th>
      <td mat-cell *matCellDef="let element">{{ element.Mobile }}</td>
    </ng-container>

    <ng-container matColumnDef="email">
      <th mat-header-cell *matHeaderCellDef>Email</th>
      <td mat-cell *matCellDef="let element">{{ element.Email }}</td>
    </ng-container>

    <ng-container matColumnDef="options" stickyEnd>
      <th mat-header-cell *matHeaderCellDef></th>
      <td mat-cell *matCellDef="let element"></td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns" [routerLink]="['./', row.UserId]"></tr>
  </table>

  <schools-table-paginator
    [length]="totalRows"
    [selectedPage]="selectedPage"
    [numberRows]="numberRows"
    (pageChanged)="pageChangedEvent($event)"
  ></schools-table-paginator>
</div>

<ng-template #noResults>
  <no-data-table-row text="No results"></no-data-table-row>
</ng-template>
