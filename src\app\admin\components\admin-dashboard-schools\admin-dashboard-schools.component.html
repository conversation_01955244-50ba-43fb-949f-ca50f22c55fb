<!-- 
<table mat-table [dataSource]="dataSource" style="width: 100%;">

    <ng-container matColumnDef="SchoolId">
        <th mat-header-cell *matHeaderCellDef>schoolId</th>
        <td mat-cell *matCellDef="let element"> {{element.schoolId}} </td>
    </ng-container>

    <ng-container matColumnDef="Orders">
        <th mat-header-cell *matHeaderCellDef>orders</th>
        <td mat-cell *matCellDef="let element"> {{element.orders}} </td>
    </ng-container>

    <ng-container matColumnDef="Errors">
        <th mat-header-cell *matHeaderCellDef>orders</th>
        <td mat-cell *matCellDef="let element"> {{element.orders}} </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
</table> -->

<admin-dashboard-line-schools
  *ngFor="let item of ordersSchool; trackBy: trackBy"
  [record]="item"
></admin-dashboard-line-schools>
