@import '../../../../styles/cashless-theme.scss';
@import 'src/styles/schools-theme';

.row-container {
  background-color: white;
  border-radius: 20px;
  padding: 20px;
  border: 1px $grey-2 solid;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 26px;
  cursor: pointer;

  h3,
  p {
    margin: 0;
  }

  .content-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .date-container {
    display: flex;
    align-items: center;
    justify-self: flex-start;
  }

  .indicator-container {
    display: flex;
    align-items: center;
    margin-left: auto;
  }

  img {
    object-fit: cover;
    width: 100%;
    max-width: 100px;
    min-height: 100px;
    height: auto;
    border-radius: 8px;
  }
}

.row-container:hover {
  border: 1px $mobile-dark-orange solid;
}
