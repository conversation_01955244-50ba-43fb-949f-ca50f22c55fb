import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import * as _ from 'lodash';

// ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { ChildrenState, FamilyState } from '../../../states';
import { children } from '../../../states/children/children.selectors';

// Model
import {
  UserCashless,
  Roles,
  CreateOrdersSummary,
  CreateEditSummary,
  CreateOrdersInfo,
  StudentFee,
} from 'src/app/sharedModels';
import { UserService } from 'src/app/sharedServices';

@Component({
  selector: 'app-orders-details',
  templateUrl: './orders-details.component.html',
  styleUrls: ['./orders-details.component.scss'],
})
export class OrdersDetailsComponent implements OnInit, OnDestroy {
  @Input() editOrderSummary: CreateEditSummary;
  @Input() createOrderSummary: CreateOrdersSummary;
  @Input() totalFees: number;
  @Input() accountBalance: number;

  // children
  private subscriptionChildren$: Subscription;
  listChildren: UserCashless[] = [];
  currentStudentFirstName: string;

  feesGroupedByStudent: StudentFee[] = [];

  isAdminMerchant: boolean;

  constructor(private store: Store<{ family: FamilyState }>, private userService: UserService) {}

  ngOnInit() {
    let connectedUser = this.userService.GetUserConnected();
    this.isAdminMerchant = connectedUser.Role == Roles.Admin || connectedUser.Role == Roles.Canteen;

    this.subscriptionChildren$ = this.store.pipe(select(children)).subscribe((children: ChildrenState) => {
      this.listChildren = children.list;
      this.currentStudentFirstName = children.selected.FirstName;
    });

    if (this.createOrderSummary) {
      this.feesGroupedByStudent = this.groupFeesByStudent(this.createOrderSummary.createOrdersInfo);
    }
  }

  ngOnDestroy(): void {
    this.subscriptionChildren$?.unsubscribe();
  }

  GetTextTotalOrder() {
    return this.editOrderSummary ? 'New order' : 'Total';
  }

  groupFeesByStudent = (feesData: CreateOrdersInfo[]): StudentFee[] => {
    const feesGroupedByStudent: { string: StudentFee } | {} = feesData.reduce((feeArray, index) => {
      const currentStudentFee = feeArray[index.studentId] ?? 0;
      return {
        ...feeArray,
        [index.studentId]: {
          fee: (currentStudentFee?.fee || 0) + index.fee,
          name: index.studentName,
        },
      };
    }, {});

    return Object.values(feesGroupedByStudent);
  };

  isRefund(): boolean {
    return this.editOrderSummary?.priceDiff < 0;
  }
}
