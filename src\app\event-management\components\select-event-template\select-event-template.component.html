

<form *ngIf="form" [formGroup]="form" class="form">

    <div class="row" *ngIf="eventTypes">
        <div class="col-12 col-md-6 col-lg-4">
            <mat-form-field appearance="outline">
                <mat-label>Template</mat-label>
                <mat-select formControlName="template">
                    <mat-option [value]="0">-- None --</mat-option>
                    <mat-optgroup *ngFor="let type of eventTypes" [label]="type.type">
                        <mat-option *ngFor="let template of type.templates" [value]="template.eventTemplateId">{{
                            template.title
                            }}</mat-option>
                    </mat-optgroup>
                </mat-select>
            </mat-form-field>
        </div>
    </div>

</form>
