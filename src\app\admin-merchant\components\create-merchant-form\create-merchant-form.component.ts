import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

import {
  ResultDialogData,
  CanteenUser,
  MerchantOwner,
  CreateMerchantRequest,
  BaseComponent,
  MerchantTypeEnum,
} from '../../../sharedModels';
import { SpinnerService, MerchantService } from '../../../sharedServices';
import { Subscription } from 'rxjs';

//dialog imports
import { MatDialog } from '@angular/material/dialog';
import { DialogResultComponent } from 'src/app/shared/components/';

@Component({
  selector: 'create-merchant-form',
  templateUrl: './create-merchant-form.component.html',
  styleUrls: ['./create-merchant-form.component.scss'],
})
export class CreateMerchantFormComponent extends BaseComponent implements OnInit, OnD<PERSON>roy {
  private routeSubscription: Subscription;
  merchantTypeEnum = MerchantTypeEnum;
  form: FormGroup;
  editUserFirstName: boolean = false;
  editUserLastName: boolean = false;
  currentRoute: any;
  currentUser: CanteenUser;
  mostRecentName: string;

  constructor(
    private spinnerService: SpinnerService,
    private merchantService: MerchantService,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute
  ) {
    super();
    //get current route
    this.routeSubscription = router.events.subscribe(route => (this.currentRoute = route));
  }

  ngOnInit() {
    // get data from resolver
    this.route.data.subscribe(data => {
      this.currentUser = data['user'];
    });

    this.CreateForm();
  }

  ngOnDestroy(): void {
    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }

    //clear search filter if navigating off search result pages
    if (!this.currentRoute.url.includes('createmerchant')) {
      this.merchantService.setMerchantSearchFilters(null);
    }

    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }

    this.merchantService.setDisableMode(false);
  }

  GoBackClick() {
    this.spinnerService.start();
    this.router.navigate(['./admin/merchants/createmerchant']);
  }

  CreateForm() {
    this.form = new FormGroup({
      firstName: new FormControl(this.currentUser.FirstName, [Validators.required]),
      lastName: new FormControl(this.currentUser.Lastname, [Validators.required]),
      email: new FormControl(this.currentUser.Email),
      merchantName: new FormControl('', [Validators.required]),
      mobile: new FormControl(this.currentUser.Mobile),
      merchantType: new FormControl('Canteen', [Validators.required]),
    });
  }

  onSubmit() {
    if (!this.form.invalid) {
      let data = new ResultDialogData();
      data.TitleLine1 = 'Are you sure?';
      data.TextLine1 = `Are you sure you want to create a new merchant?`;
      data.CancelButton = 'No, Cancel';
      data.ConfirmButton = 'Yes, Create';

      const dialogRef = this.dialog.open(DialogResultComponent, {
        width: '400px',
        disableClose: true,
        data: data,
      });

      dialogRef.afterClosed().subscribe(cancelResult => {
        if (!cancelResult) {
          this.confirmCreateMerchant();
        }
      });
    }
  }

  confirmCreateMerchant() {
    this.spinnerService.start();

    let ownerData: MerchantOwner = {
      ownerId: this.currentUser.UserId,
      ownerFirstName: this.firstName.value,
      ownerLastName: this.lastName.value,
      ownerMobile: this.mobile.value,
    };

    let merchantData: CreateMerchantRequest = {
      owner: ownerData,
      name: this.merchantName.value,
      type: this.type.value,
    };

    this.merchantService.CreateMerchant(merchantData).subscribe({
      next: (res: any) => {
        this.merchantService.setNewMerchantId(res);
        this.SuccessPopUp();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
        this.SomethingWentWrongPopup();
      },
    });
  }

  get merchantName() {
    return this.form.get('merchantName');
  }
  get type() {
    return this.form.get('merchantType');
  }
  get firstName() {
    return this.form.get('firstName');
  }
  get lastName() {
    return this.form.get('lastName');
  }
  get mobile() {
    return this.form.get('mobile');
  }

  ///////////////////////
  // Pop ups
  ///////////////////////
  SomethingWentWrongPopup() {
    let data = new ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = 'We were unable to create this merchant.';
    data.TextLine2 = 'Please try again.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.confirmCreateMerchant();
      }
    });
  }

  SuccessPopUp() {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = 'Merchant created successfully.';
    data.ConfirmButton = 'Okay';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      this.spinnerService.start();
      this.router.navigate(['./admin/merchants']);
    });
  }
}
