import { Routes } from '@angular/router';
import { EventListPreviewPageComponent } from './components/event-list-preview-page/event-list-preview-page.component';
import { ViewEventPageComponent } from './components/view-event-page/view-event-page.component';
import { CreateEventFlowPageComponent } from './components/create-event-flow-page/create-event-flow-page.component';
import { EventViewResolver, SchoolClassResolver, EventTemplateResolver } from './resolvers';

export const eventManagementRoutes: Routes = [
  { path: '', component: EventListPreviewPageComponent },
  { path: 'view/:id', component: ViewEventPageComponent, resolve: { eventData: EventViewResolver } },
  { path: 'create', component: CreateEventFlowPageComponent, resolve: { classes: SchoolClassResolver, templates: EventTemplateResolver } },
  {
    path: 'view/:id/editor',
    loadChildren: () => import('../menu-editor/menu-editor.module').then(m => m.MenuEditorModule),
  },
];
