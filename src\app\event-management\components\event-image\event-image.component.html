<div *ngIf="imageUrl" class="image-preview">
  <img [src]="imageUrl" alt="Event image" />
  <div class="mt-4">
    <a (click)="DeleteImage()">Delete</a>
  </div>
</div>

<ng-container *ngIf="!imageUrl">
  <event-image-placeholder />

  <div class="mt-4">
    <input
      #fileInput
      id="fileInput"
      type="file"
      accept=".png,.jpg,.jpeg"
      (click)="fileInput.value = null"
      value=""
      (change)="onSelectFile($event.target.files)"
    />

    <basic-button-v2
      *ngIf="url"
      text="Upload"
      buttonStyle="primaryOrange"
      (onPress)="Upload()"
    ></basic-button-v2>
  </div>
</ng-container>
