<nav-back-button
  smallText="true"
  (navBack)="goBackClick()"
  text="Go Back"
  class="backButton"
  noPadding="true"
></nav-back-button>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <school-header title="Finance Reports"></school-header>
    </div>
  </div>
  <div class="row">
    <div class="col-lg-6 col-md-9 col-sm-12">
      <div class="pt-4">
        <school-panel>
          <p class="mb-0 panelTitle">Export weekly settlement information for all active schools</p>
          <form *ngIf="formGroup" [formGroup]="formGroup" class="pb-4">
            <div class="row">
              <div class="col-12 pt-4">
                <input-select-list
                  formControlName="week"
                  placeholder="Select week"
                  [values]="selectWeekValues"
                ></input-select-list>
              </div>
              <p class="col-12 subtitle">Export as .csv</p>
              <div class="col-6">
                <basic-button
                  text="Settlement"
                  [buttonStyle]="1"
                  [fullWidth]="true"
                  (click)="getSettlement()"
                ></basic-button>
              </div>
              <div class="col-6">
                <basic-button
                  text="Invoice"
                  [buttonStyle]="1"
                  [fullWidth]="true"
                  (click)="getInvoice()"
                ></basic-button>
              </div>
            </div>
          </form>
        </school-panel>
      </div>

      <div class="pt-4">
        <school-panel>
          <p class="mb-0 panelTitle">Export settlement information for all active schools</p>
          <form *ngIf="formGroup" [formGroup]="formGroupDates" class="pb-4">
            <div class="row pt-4">
              <div class="col-6">
                <input-date
                  placeholder="Start Date"
                  formControlName="startDate"
                  [error]="startDate.invalid ? invalidValueError : null"
                ></input-date>
              </div>
              <div class="col-6">
                <input-date
                  placeholder="End Date"
                  formControlName="endDate"
                  [error]="endDate.invalid ? invalidValueError : null"
                ></input-date>
              </div>
              <p class="col-12 subtitle">Export as .csv</p>
              <div class="col-6">
                <basic-button
                  text="Export"
                  [buttonStyle]="1"
                  [fullWidth]="true"
                  (click)="getRevenue()"
                ></basic-button>
              </div>
            </div>
          </form>
        </school-panel>
      </div>
    </div>
  </div>
</div>
