@import '../../../../styles/cashless-breakpoints.scss';
@import '../../../../styles/cashless-font.scss';
@import '../../../../styles/cashless-theme.scss';

.container {
  background-color: white;
  border: 1px solid;
  padding: 10px;
  border: 1px solid #dddddd;
  border-radius: 12px;
  box-sizing: border-box;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.titleHeader {
  color: #333b44;
  font-size: 36px;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.loginForm {
  border: 1px solid black;
  margin-top: auto;
  margin-bottom: auto;
}

.forgotPwd {
  text-align: center;
  margin-top: 15px;
}

.subtext {
  font-size: 12px;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
  size: 11px;
  font-weight: 400;
  color: $grey-14;
}

.tcWrapper {
  padding-left: 5px;
  padding-right: 5px;
}

.mat-mdc-unelevated-button {
  font-family: 'bariol_bold';
  font-size: 22px;
}
.submitButton {
  border-radius: 8px;
  width: 100%;
  height: 56px;
  // background-color: #FF9E00;
  background: linear-gradient(96.62deg, $orange-1 0%, $orange-2 100%);
  color: white;
  font-family: 'bariol_bold';
  font-size: 22px;
  border-width: 0px;
  @media (min-width: $breakpoint-sm) {
    text-align: center;
  }
}

.googleButton {
  width: 100%;
  height: 36px;
  //border-radius: 5px;
  border: 1px solid lightgrey;
  color: grey;
  //text-align: center;
  //cursor: pointer;
}

.bottomLink {
  text-align: center;
  margin-top: 17px;
}

.resetLink {
  color: #ff9e00;
}

.containerBottom {
  background-color: white;
  border: 1px solid;
  border: 1px solid #dddddd;
  border-radius: 12px;
  box-sizing: border-box;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
}

.register {
  background: rgba(51, 59, 68, 0.15);
  border-radius: 8px;
  border-width: 0;
  width: 90%;
  height: 56px;
  color: #333b44;
  font-family: 'bariol_regular';
  font-size: 20px;
  margin-bottom: 21px;
  margin-top: 12px;
}

.offlineMode {
  color: red;
}

.hidden {
  display: none;
}
