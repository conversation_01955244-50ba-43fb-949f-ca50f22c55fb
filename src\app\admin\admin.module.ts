import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { AdminRoutingModule } from './admin-routing.module';
import { AccountModule } from '../account/account.module';
import { SharedModule } from '../shared/shared.module';
import { SharedToolsModule } from '../shared-tools/shared-tools.module';
import { SchoolsFormModule } from '../schools-form/schools-form.module';
import { SchoolsButtonModule } from '../schools-button/schools-button.module';
import { SchoolsCommonModule } from '../schools-common/schools-common.module';

import { NavigationModule } from '../navigation/navigation.module';

// material
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule } from '@angular/material/dialog';

// Ngrx
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';

// state
import { dashboardFeature } from '../states/admin/dashboardFeature/dashboard.reducer';
import { DashboardEffects } from '../states/admin/dashboardFeature/dashboard.effects';

// components
import {
  AdminDashboardErrorsComponent,
  AdminDashboardSchoolsComponent,
  AdminDashboardLineSchoolsComponent,
  AdminDashboardComponent,
  AdminNoticeComponent,
  AdminNoticeDialog,
  AdminDashboardProcessingComponent,
  OrderErrorTableComponent,
  OrderErrorTableHeadingPipe,
  DashboardHeaderComponent,
  EngineerDashboardComponent,
  ErrorStatusBarComponent,
} from './components';
import { AdminUsersManagementModule } from '../admin-users-management/admin-users-management.module';

@NgModule({
  declarations: [
    AdminNoticeComponent,
    AdminNoticeDialog,
    AdminDashboardComponent,
    AdminDashboardLineSchoolsComponent,
    AdminDashboardSchoolsComponent,
    AdminDashboardErrorsComponent,
    AdminDashboardProcessingComponent,
    OrderErrorTableComponent,
    OrderErrorTableHeadingPipe,
    DashboardHeaderComponent,
    EngineerDashboardComponent,
    ErrorStatusBarComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    AdminRoutingModule,
    AccountModule,
    SharedModule,
    SharedToolsModule,
    NavigationModule,
    AdminUsersManagementModule,
    // state
    StoreModule.forFeature('dashboard', dashboardFeature),
    EffectsModule.forFeature([DashboardEffects]),

    // schools modules
    SchoolsCommonModule,
    SchoolsFormModule,
    SchoolsButtonModule,

    // material
    MatIconModule,
    MatCheckboxModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatCardModule,
    MatTableModule,
    MatButtonModule,
    MatMenuModule,
    MatDialogModule,
  ],
  providers: [DatePipe],
})
export class AdminModule {}
