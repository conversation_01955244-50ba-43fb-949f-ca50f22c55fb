<nav-back-button
  smallText="true"
  (navBack)="GoBackClick()"
  text="Go Back"
  class="backButton"
  smallFont="true"
  noPadding="true"
></nav-back-button>

<div class="container-fluid">
  <div class="header">
    <h3 class="merchant-heading">{{ pageTitle }}</h3>
    <button *ngIf="!editMode" class="merchant-btn" (click)="addUser()">
      <img src="assets/icons/white-plus.svg" alt="plus symbol" />
      Add User
    </button>
    <button *ngIf="editMode" class="merchant-btn" (click)="removeUser()">
      <img src="assets/icons/white-dash.svg" alt="white dash" />
      Remove User
    </button>
  </div>

  <!-- User information form  -->
  <form *ngIf="form" [formGroup]="form" class="user-form">
    <div class="input-wrapper">
      <mat-form-field appearance="outline">
        <mat-label>Name</mat-label>
        <input matInput placeholder="Name" formControlName="name" type="text" required readonly />
      </mat-form-field>

      <mat-form-field class="email-input" appearance="outline">
        <mat-label>Email</mat-label>
        <input matInput placeholder="Email" formControlName="email" type="email" required readonly />
      </mat-form-field>
    </div>
  </form>

  <!-- school permissions -->
  <div class="sub-heading-wrapper">
    <h4 class="sub-heading">School Permissions</h4>
    <div *ngIf="schoolData" class="button-wrapper">
      <a (click)="formSelect('School', false)">Clear</a>
      <a style="font-weight: 700" (click)="formSelect('School', true)">Select All</a>
    </div>
  </div>

  <form [formGroup]="schoolForm">
    <div class="form-wrapper school-form" [ngClass]="{ error: submitError }">
      <div
        class="checkbox-wrapper"
        formArrayName="schools"
        *ngFor="let school of schoolFormArray.controls; let i = index"
      >
        <mat-checkbox [formControlName]="i">
          {{ schoolData[i].Name }}
        </mat-checkbox>
      </div>

      <div *ngIf="isSchoolListEmpty()" class="emptyMessage">No linked schools</div>
    </div>
  </form>

  <div *ngIf="submitError" class="school-error-message">
    <img src="assets/icons/black-error-icon.svg" alt="error symbol" />
    <p>{{ getSchoolError() }}</p>
  </div>

  <!-- User permissions -->
  <div class="sub-heading-wrapper">
    <h4 class="sub-heading">User Permissions</h4>
    <div class="button-wrapper">
      <a (click)="formSelect('User', false)">Clear</a>
      <a style="font-weight: 700" (click)="formSelect('User', true)">Select All</a>
    </div>
  </div>

  <form [formGroup]="form">
    <div class="form-wrapper permissions-form">
      <mat-checkbox formControlName="isAdmin"> Is Admin </mat-checkbox>

      <mat-checkbox formControlName="menuEditor"> View menu editor </mat-checkbox>

      <mat-checkbox formControlName="salesReport"> View sales reports </mat-checkbox>

      <mat-checkbox formControlName="viewEvent"> View event management </mat-checkbox>

      <mat-checkbox formControlName="allowUnprintedOrders"> Allow unprinted orders </mat-checkbox>

      <mat-checkbox formControlName="emailUnprintedOrders"> Email unprinted orders </mat-checkbox>
    </div>
  </form>

  <!-- Save changes button -->
  <div *ngIf="editMode" class="save-btn-wrapper">
    <button
      *ngIf="editMode"
      class="save-changes-btn"
      (click)="updateUser()"
      [disabled]="disableSaveChanges()"
    >
      Save Changes
    </button>
  </div>
</div>
