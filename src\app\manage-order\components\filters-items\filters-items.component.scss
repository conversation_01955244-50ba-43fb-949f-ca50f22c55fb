@import '../../../../styles/cashless-breakpoints.scss';
@import '../../../../styles/cashless-theme.scss';

.iconFilter {
  padding: 8px;
  height: 40px;
  width: 40px;
  background-color: white;
  border-radius: 50px;
  margin-top: 5px;
  cursor: pointer;

  &.activeFilter {
    color: $orange-3;
  }
}

// Sheet component
h2 {
  color: $orange-3;
  margin-bottom: 40px;
  margin-top: 5px;
}
.rowSheet {
  margin-bottom: 20px;
}
.sheetButton {
  margin-bottom: 80px;

  @media (min-width: $breakpoint-md) {
    margin-bottom: 20px;
  }
}
