import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { NgxPrintModule } from 'ngx-print';

// google material
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule } from '@angular/material/dialog';

//Modules
import { CanteenSettingsRoutingModule } from './canteen-settings-routing.module';
import { AccountModule } from '../account/account.module';
import { SharedModule } from '../shared/shared.module';
import { SharedToolsModule } from '../shared-tools/shared-tools.module';
import { SchoolsEventsModule } from '../schools-events/schools-events.module';
import { SchoolsButtonModule } from '../schools-button/schools-button.module';
import { SchoolsFormModule } from '../schools-form/schools-form.module';

// Components
import {
  PopupCategoryFormComponent,
  ManageCategoriesComponent,
  AccountSettingsComponent,
  SchoolSettingsComponent,
  MenuSettingsComponent,
  AdminSettingsComponent,
  ClassesListComponent,
  AddSchoolClassComponent,
  OpeningDaysFormComponent,
  DietaryLabelsFormComponent,
  MenuCategoriesFormComponent,
  AllergyAlertFormComponent,
  OrderAdvanceFormComponent,
  PrinterOptionsFormComponent,
  SchoolClosingDateFormComponent,
  SchoolCloseDateTableComponent,
  SettingsCheckboxListComponent,
  FoodBreakSettingsFormComponent,
} from './components';
import { CategoryIconComponent } from '../manage-order/components/category-icon/category-icon.component';

@NgModule({
  declarations: [
    AccountSettingsComponent,
    SchoolSettingsComponent,
    MenuSettingsComponent,
    AdminSettingsComponent,
    ClassesListComponent,
    PopupCategoryFormComponent,
    ManageCategoriesComponent,
    AddSchoolClassComponent,
    OpeningDaysFormComponent,
    DietaryLabelsFormComponent,
    MenuCategoriesFormComponent,
    OrderAdvanceFormComponent,
    AllergyAlertFormComponent,
    PrinterOptionsFormComponent,
    SchoolClosingDateFormComponent,
    SchoolCloseDateTableComponent,
    SettingsCheckboxListComponent,
    FoodBreakSettingsFormComponent,
  ],
  imports: [
    CommonModule,
    SchoolsFormModule,
    ReactiveFormsModule,
    FormsModule,
    NgxPrintModule,
    CanteenSettingsRoutingModule,
    //CashlessCoreModule,
    AccountModule,
    SharedModule,
    SharedToolsModule,
    SchoolsEventsModule,
    SchoolsButtonModule,
    // material
    MatFormFieldModule,
    MatRadioModule,
    MatCheckboxModule,
    MatExpansionModule,
    MatSelectModule,
    MatTableModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
    MatSortModule,
    MatPaginatorModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatTooltipModule,
    MatMenuModule,
    MatDialogModule,
    CategoryIconComponent,
  ],
  exports: [MatTooltipModule],
  providers: [DatePipe],
})
export class CanteenSettingsModule {}
