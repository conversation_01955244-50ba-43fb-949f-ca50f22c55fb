<div class="container-fluid">
  <form *ngIf="form" [formGroup]="form">
    <div class="row">
      <div class="col-12 col-sm-4 col-md-2">
        <mat-form-field class="fullWidthInput" appearance="outline">
          <mat-label>Settings</mat-label>
          <mat-select formControlName="settings">
            <mat-option [value]="printSettings.Default"> Chrome Default </mat-option>
            <mat-option [value]="printSettings.Custom"> Chrome No Margins </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="col-12 col-sm-4 col-md-2">
        <mat-form-field appearance="outline">
          <mat-label>Start line</mat-label>
          <mat-select formControlName="line" required>
            <mat-option value="1">1</mat-option>
            <mat-option value="2">2</mat-option>
            <mat-option value="3">3</mat-option>
            <mat-option value="4">4</mat-option>
            <mat-option value="5">5</mat-option>
            <mat-option value="6">6</mat-option>
            <mat-option value="7">7</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="col-12 col-sm-4 col-md-2">
        <mat-form-field appearance="outline">
          <mat-label>Start column</mat-label>
          <mat-select formControlName="column" required>
            <mat-option value="1">1</mat-option>
            <mat-option value="2">2</mat-option>
            <mat-option value="3">3</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="col-12 col-sm-4 col-md-2 d-flex align-items-center">
        <mat-checkbox formControlName="displaySchoolName">Display School Name</mat-checkbox>
      </div>
    </div>
  </form>
</div>
