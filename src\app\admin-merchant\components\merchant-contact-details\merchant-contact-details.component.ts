import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { SpinnerService, MerchantService, PhoneNumberService } from '../../../sharedServices';
import { MerchantContactDetails, MerchantOwner } from '../../../sharedModels';

//dialog
import { MatDialog } from '@angular/material/dialog';

// components
import { BaseMerchantFormComponent } from '../../models/base-merchant-form';

@Component({
  selector: 'merchant-contact-details',
  templateUrl: './merchant-contact-details.component.html',
  styleUrls: ['../../styles/merchant-form.scss'],
})
export class MerchantContactDetailsComponent extends BaseMerchantFormComponent implements OnInit, OnDestroy {
  @Input() contactDetails: MerchantContactDetails;

  constructor(
    private spinnerService: SpinnerService,
    protected merchantService: MerchantService,
    public dialog: MatDialog,
    private phoneNumberService: PhoneNumberService
  ) {
    super(dialog, merchantService);
  }

  ngOnInit() {
    this._baseOnInit();
  }

  ngOnDestroy(): void {
    this._baseOnDestroy();
  }

  /**
   * Retry popup after having an error when saving
   */
  SomethingWentWrongPopup() {
    const dialogRef = this._somethingWentWrongPopup();

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.saveContactDetails();
      }
    });
  }

  /**
   * Save the contactDetails
   */
  saveContactDetails() {
    this.spinnerService.start();

    let phoneNumber = this.phoneNumberService.serverMobileNumber(this.phone.value);

    let data: MerchantOwner = {
      ownerId: this.contactDetails.ownerId,
      ownerFirstName: this.firstName.value,
      ownerLastName: this.lastName.value,
      ownerMobile: phoneNumber,
    };

    this.merchantService.UpdateMerchantContactDetails(this.selectedMerchant.canteenId, data).subscribe({
      next: res => {
        this.spinnerService.stop();
        this.SuccessPopUp();
        //update form values
        this.contactDetails.ownerFirstName = this.firstName.value;
        this.contactDetails.ownerLastName = this.lastName.value;
        this.contactDetails.ownerMobile = phoneNumber;
        //update merchant sidebar values
        let fullName = this.firstName.value + ' ' + this.lastName.value;
        this.merchantService.UpsertMerchantToService(
          fullName,
          this.selectedMerchant.canteenId,
          this.selectedMerchant.ownerId,
          'UserName'
        );
      },
      error: error => {
        this.spinnerService.stop();
        this.SomethingWentWrongPopup();
      },
    });
  }

  ///////////////////////////////////
  // Form
  ///////////////////////////////////

  /**
   * Trigger the form creation
   */
  triggerEdit() {
    this._createForm();
    this.editDetailsMode = true;
    this.merchantService.setDisableMode(true);
  }

  /**
   * Setup the form with all the controls
   */
  private _createForm() {
    this.formGroup = new FormGroup({
      firstName: new FormControl(this.contactDetails.ownerFirstName, Validators.required),
      lastName: new FormControl(this.contactDetails.ownerLastName, Validators.required),
      email: new FormControl({ value: this.contactDetails.ownerEmail, disabled: true }, [
        Validators.required,
        Validators.email,
      ]),
      phone: new FormControl(this.contactDetails.ownerMobile, [
        Validators.required,
        Validators.pattern(/(^\+614[0-9]{8}$)|(^614[0-9]{8}$)|(^04[0-9]{8}$)/),
      ]),
    });
  }

  /**
   * Form controls accessors
   */
  get firstName() {
    return this.formGroup.get('firstName');
  }
  get lastName() {
    return this.formGroup.get('lastName');
  }
  get email() {
    return this.formGroup.get('email');
  }
  get phone() {
    return this.formGroup.get('phone');
  }
}
