@import 'src/styles/schools-table';
@import 'src/styles/schools-list';

h4 {
  margin-left: 56px;
  margin-bottom: 0;
}

.infoList {
  margin-top: 6px;
  margin-left: 56px;

  & li {
    margin-bottom: 6px;
  }
}

.declinedText {
  color: $status-error-text;
}

.option-column {
  text-align: right;
}

.download-link {
  cursor: pointer;
  color: $link-default;
  font-size: 14px;
}

.status-error {
  color: $error;
}

.transactions-wrapper {
  & * {
    -webkit-user-select: text; /* Chrome 49+ */
    -moz-user-select: text; /* Firefox 43+ */
    -ms-user-select: text; /* No support yet */
    user-select: text; /* Likely future */
  }

  & table {
    width: 100%;
  }
}

.response {
  color: $text-dark-medium;
  font-size: 12px;
  line-height: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  & h3 {
    margin: 0;
    font-size: 18px;
  }

  & img {
    cursor: pointer;
  }
}

.mat-column-message {
  max-width: 380px;
}

.mat-column-options {
  max-width: 150px;
}
