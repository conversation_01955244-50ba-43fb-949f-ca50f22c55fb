<table>
  <thead>
    <th>Id</th>
    <th>Category</th>
    <th>Name</th>
    <th>Is Active</th>
    <th></th>
  </thead>
  <tbody>
    <tr *ngFor="let item of tableData" class="line">
      <td>{{ item.MenuItemId }}</td>
      <td>{{ item.CatName }}</td>
      <td>{{ item.Name }}</td>
      <td>
        <mat-checkbox [checked]="item.IsActive" [disabled]="true"></mat-checkbox>
      </td>
      <td>
        <mat-icon matTooltip="Remove" class="actionTableau" (click)="removeItem(item)">delete</mat-icon>
      </td>
    </tr>
  </tbody>
</table>
