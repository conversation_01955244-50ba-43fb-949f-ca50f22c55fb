import { Input, Directive } from '@angular/core';
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';

// models
import { MenuTypeEnum, ListOrder, UserCashless } from 'src/app/sharedModels';
import { FamilyState } from 'src/app/states';
import { selectedChild } from 'src/app/states/children/children.selectors';
import { MenuPickerSelect, datePickerSelect } from 'src/app/states/family/family.selectors';

@Directive()
export class BaseMenuDateFilterComponent {
  private subscriptionDate$: Subscription;
  private subscriptionStudent$: Subscription;
  private subscriptionMenu$: Subscription;
  @Input() title: string;
  @Input() isEdit: boolean;
  isUniformOrder: boolean = false;
  isEventOrder: boolean = false;
  cutOffTime: string;
  listOrders: ListOrder;
  schoolWeeksPreOrder: number;

  student: UserCashless;
  menuType: string;
  date: Date;

  constructor(protected store: Store<{ family: FamilyState }>) {}

  OnInitFunction() {
    this.subscriptionStudent$ = this.store.pipe(select(selectedChild)).subscribe((student: UserCashless) => {
      this.student = student;
      this.schoolWeeksPreOrder = student?.SchoolWeeksPreOrder ? student.SchoolWeeksPreOrder : 3;
    });

    this.subscriptionDate$ = this.store.pipe(select(datePickerSelect)).subscribe((date: Date) => {
      this.date = date;
    });

    this.subscriptionMenu$ = this.store.pipe(select(MenuPickerSelect)).subscribe((menuType: string) => {
      if (!this.menuType) {
        this.isUniformOrder = menuType == MenuTypeEnum.Uniform;
        this.isEventOrder = menuType == MenuTypeEnum.Event;
      }
      this.menuType = menuType;
    });
  }

  OnDestroyFunction() {
    this.subscriptionDate$?.unsubscribe();
    this.subscriptionStudent$?.unsubscribe();
    this.subscriptionMenu$?.unsubscribe();
  }
}
