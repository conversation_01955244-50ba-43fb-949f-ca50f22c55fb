import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

// Services
import { ListCanteensResolver, ItemMenuResolver, OptionResolver, StockResolver } from '../sharedServices';

// Components
import {
  ListOptionsComponent,
  ListItemsComponent,
  DetailOptionComponent,
  EditorItemComponent,
  ListStocksComponent,
  DetailStockComponent,
} from './components';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'items',
  },
  {
    path: 'items',
    component: ListItemsComponent,
    resolve: { canteens: ListCanteensResolver },
  },
  {
    path: 'item/:itemId',
    component: EditorItemComponent,
    resolve: {
      item: ItemMenuResolver,
    },
  },
  {
    path: 'item/add',
    component: EditorItemComponent,
  },
  {
    path: 'options',
    component: ListOptionsComponent,
    resolve: { canteens: ListCanteensResolver },
  },
  {
    path: 'option/:optionId',
    component: DetailOptionComponent,
    resolve: {
      option: OptionResolver,
    },
  },
  {
    path: 'option/add',
    component: DetailOptionComponent,
  },
  {
    path: 'stocks',
    component: ListStocksComponent,
    resolve: { canteens: ListCanteensResolver },
  },
  {
    path: 'stock/:stockId',
    component: DetailStockComponent,
    resolve: {
      stock: StockResolver,
    },
  },
  {
    path: 'stock/add',
    component: DetailStockComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MenuEditorRoutingModule {}
