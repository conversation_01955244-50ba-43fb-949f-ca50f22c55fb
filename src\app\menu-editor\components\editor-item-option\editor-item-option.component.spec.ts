import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { EditorItemOptionComponent } from './editor-item-option.component';

describe('EditorItemOptionComponent', () => {
  let component: EditorItemOptionComponent;
  let fixture: ComponentFixture<EditorItemOptionComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [EditorItemOptionComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EditorItemOptionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
