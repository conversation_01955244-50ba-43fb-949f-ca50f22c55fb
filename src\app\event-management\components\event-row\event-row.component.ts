import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SchoolEvent } from 'src/app/sharedModels';
import { EventTimeFormatPipe } from 'src/app/sharedPipes';
import { ImagePlaceholderComponent } from "../image-placeholder/image-placeholder.component";
import { ActiveIndicatorComponent } from "../active-indicator/active-indicator.component";

@Component({
  selector: 'event-row',
  standalone: true,
  imports: [CommonModule, EventTimeFormatPipe, ImagePlaceholderComponent, ActiveIndicatorComponent],
  templateUrl: './event-row.component.html',
  styleUrls: ['./event-row.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EventRowComponent {
  @Input() schoolEvent: SchoolEvent;
  @Output() onPress = new EventEmitter<number>();

  pressEvent(): void {
    this.onPress.emit(this.schoolEvent.SchoolEventId);
  }
}
