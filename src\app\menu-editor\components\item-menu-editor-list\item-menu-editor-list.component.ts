import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';

//models
import { BaseComponent, CanteenSchool, ItemMenus } from 'src/app/sharedModels';

//services
import { MenuEditorApiService, SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'item-menu-editor-list',
  templateUrl: './item-menu-editor-list.component.html',
  styleUrls: ['./item-menu-editor-list.component.scss'],
})
export class ItemMenuEditorListComponent extends BaseComponent implements OnChanges {
  @Input() itemMenus: ItemMenus[] = [];
  @Input() merchantSchools: CanteenSchool[];
  @Input() menuItemId: number;
  @Input() merchantId: number;
  menuList: ItemMenus[] = [];

  constructor(private spinnerService: SpinnerService, private menuEditorAPIService: MenuEditorApiService) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.menuItemId?.currentValue) {
      //read only object needs to be cloned to make changes
      this.menuList = this.getMenusToDisplay();
    }
  }

  getMenusToDisplay(): ItemMenus[] {
    if (!this.itemMenus?.length) {
      return [];
    }
    const menuList: ItemMenus[] = this.getMenusFromCurrentMerchantSchools();
    const sortedMenus: ItemMenus[] = this.sortInAlphabeticalOrder(menuList);
    return sortedMenus;
  }

  getMenusFromCurrentMerchantSchools(): ItemMenus[] {
    let menuList: ItemMenus[] = [];
    this.itemMenus.forEach(m => {
      let index = this.merchantSchools.findIndex(i => i.SchoolId == m.SchoolId);
      if (index > -1) {
        menuList.push(m);
      }
    });
    return menuList;
  }

  sortInAlphabeticalOrder(menus: ItemMenus[]): ItemMenus[] {
    return menus.sort((a, b) => {
      if (a.SchoolName < b.SchoolName) return -1;
      if (a.SchoolName > b.SchoolName) return 1;
      else return 0;
    });
  }

  UpdateLinkMenuMenu(menuId: number, isSelected: boolean): void {
    this.spinnerService.start();

    const request: ItemMenus = new ItemMenus();
    request.MenuItemId = this.menuItemId;
    request.MenuId = menuId;
    request.IsSelected = isSelected;
    request.CanteenId = this.merchantId;

    this.menuEditorAPIService.UpdateLinkItemAndMenuAPI(request).subscribe({
      next: (response: any) => {
        this.updateMenuStatus(menuId, request.IsSelected);
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  updateMenuStatus(menuId: number, isSelected: boolean): void {
    const index = this.menuList.findIndex(x => x.MenuId == menuId);
    if (index >= 0) {
      this.menuList[index].IsSelected = isSelected;
    }
  }
}
