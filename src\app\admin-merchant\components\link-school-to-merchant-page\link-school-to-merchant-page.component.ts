import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';

import { ResultDialogData, BaseComponent, Merchant, School } from '../../../sharedModels';
import { SpinnerService, MerchantService } from '../../../sharedServices';

//dialog imports
import { MatDialog } from '@angular/material/dialog';
import { DialogResultComponent } from 'src/app/shared/components/';

@Component({
  selector: 'app-link-school-to-merchant-page',
  templateUrl: './link-school-to-merchant-page.component.html',
  styleUrls: ['./link-school-to-merchant-page.component.scss'],
})
export class LinkSchoolToMerchantPageComponent extends BaseComponent implements OnInit, OnDestroy {
  private routeSubscription: Subscription;
  currentRoute: any;
  selectedMerchant: Merchant;

  constructor(
    private spinnerService: SpinnerService,
    private router: Router,
    private merchantService: MerchantService,
    public dialog: MatDialog,
    private route: ActivatedRoute
  ) {
    super();

    //get current route
    this.routeSubscription = router.events.subscribe(route => (this.currentRoute = route));
  }

  ngOnInit() {
    // get data from resolver
    this.route.data.subscribe(data => {
      this.selectedMerchant = data['merchant'];
    });
  }

  ngOnDestroy() {
    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }

    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }

  GoBackClick() {
    this.spinnerService.start();
    this.router.navigate(['./admin/merchants']);
  }

  selectSchool(school: School) {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Are you sure?';
    data.TextLine1 = `Are you sure you want to link '${school.Name}' to ‘${this.selectedMerchant.name}’?`;
    data.TextLine2 = '';
    data.CancelButton = 'No, Cancel';
    data.ConfirmButton = 'Yes, link now';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.confirmLinkSchool(school);
      }
    });
  }

  confirmLinkSchool(school: School) {
    this.spinnerService.start();
    this.merchantService.LinkSchoolToMerchant(this.selectedMerchant.canteenId, school.SchoolId).subscribe({
      next: res => {
        this.spinnerService.stop();
        this.SuccessPopUp(school.Name);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
        this.SomethingWentWrongPopup(school);
      },
    });
  }

  ///////////////////////
  // Pop ups
  ///////////////////////
  SomethingWentWrongPopup(school: School) {
    let data = new ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = `School could not be linked to merchant.`;
    data.TextLine2 = 'Please try again.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.confirmLinkSchool(school);
      }
    });
  }

  SuccessPopUp(schoolName: string) {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = `School linked to merchant successfully.`;
    data.ConfirmButton = 'Okay';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      //navigate back to home
      this.spinnerService.start();
      this.router.navigate(['./admin/merchants']);
    });
  }
}
