<editor-nav></editor-nav>

<div class="container-fluid">
  <div class="row">
    <div class="col-12 col-sm-3">
      <canteen-select-list (selectedChanged)="CanteenChangedEvent($event)"></canteen-select-list>
    </div>
  </div>

  <div class="row">
    <div class="col-2 offset-md-2">
      <button class="PrimaryButton addButton" type="button" [routerLink]="['../stock/add']">Add Stock</button>
    </div>
  </div>

  <div class="row">
    <div class="col-8 offset-md-2">
      <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 tableau eTable">
        <!-- Image Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef>Id</th>
          <td mat-cell *matCellDef="let element">{{ element.StockId }}</td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Stock</th>
          <td mat-cell *matCellDef="let element">{{ element.StockName }}</td>
        </ng-container>

        <!-- Daily stock Column -->
        <ng-container matColumnDef="dailyStock">
          <th mat-header-cell *matHeaderCellDef>Daily Stock</th>
          <td mat-cell *matCellDef="let element">
            {{ element.DailyStock }}
          </td>
        </ng-container>

        <!-- Stock Column -->
        <ng-container matColumnDef="stock">
          <th mat-header-cell *matHeaderCellDef>Remaining Quantity</th>
          <td mat-cell *matCellDef="let element">
            {{ element.StockQuantity }}
          </td>
        </ng-container>

        <!-- location Column -->
        <ng-container matColumnDef="isActive">
          <th mat-header-cell *matHeaderCellDef>Visibility</th>
          <td mat-cell *matCellDef="let element">
            <mat-checkbox [checked]="element.IsActive" [disabled]="true"></mat-checkbox>
          </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let element">
            <mat-icon matTooltip="Edit" [routerLink]="['../stock', element.StockId]" class="actionTableau"
              >edit</mat-icon
            >
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </div>
  </div>
</div>
