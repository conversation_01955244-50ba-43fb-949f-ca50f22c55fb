<mat-accordion>
  <mat-expansion-panel [expanded]="true" [ngClass]="{ 'mat-elevation-z0': forReports }">
    <mat-expansion-panel-header>
      <mat-panel-title>
        {{ formTitle }}
      </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="row" *ngIf="formGroup || filterForm" [formGroup]="getForm()">
      <ng-container *ngIf="!isUniformShop; else uniformFilters">
        <div *ngIf="!isEventMerchant" class="col-6 col-md-2 col-lg-1">
          <mat-checkbox formControlName="recess">{{ MenuTypeEnum.Recess | merchantMenuName }}</mat-checkbox>
        </div>
        <div *ngIf="!isEventMerchant" class="col-6 col-md-2 col-lg-1">
          <mat-checkbox formControlName="lunch">{{ MenuTypeEnum.Lunch | merchantMenuName }}</mat-checkbox>
        </div>
        <div class="col-6 col-md-2 col-lg-1">
          <mat-checkbox formControlName="event">Event</mat-checkbox>
        </div>
      </ng-container>

      <ng-template #uniformFilters>
        <div class="col-6 col-md-2 col-lg-1">
          <mat-checkbox formControlName="uniNew"
            >New <span *ngIf="statusTotals && uniNew.value">({{ this.statusTotals.New }})</span></mat-checkbox
          >
        </div>
        <div class="col-6 col-md-2 col-lg-1">
          <mat-checkbox formControlName="uniProcessing"
            >Processing
            <span *ngIf="statusTotals && uniProcessing.value"
              >({{ this.statusTotals.Processing }})</span
            ></mat-checkbox
          >
        </div>
        <div class="col-6 col-md-2 col-lg-1">
          <mat-checkbox formControlName="uniReady"
            >Ready
            <span *ngIf="statusTotals && uniReady.value">({{ this.statusTotals.Ready }})</span></mat-checkbox
          >
        </div>
        <div class="col-6 col-md-2 col-lg-1">
          <mat-checkbox formControlName="uniCompleted"
            >Completed
            <span *ngIf="statusTotals && uniCompleted.value"
              >({{ this.statusTotals.Completed }})</span
            ></mat-checkbox
          >
        </div>
      </ng-template>
    </div>
  </mat-expansion-panel>
</mat-accordion>
