import { Component, OnInit } from '@angular/core';
import { Store, select } from '@ngrx/store';
import { CanteenState, UserState } from 'src/app/states';
import { FeatureFlagService, SpinnerService, UserService } from 'src/app/sharedServices';
import { Canteen, UserCashless, CanteenUser, MerchantTypeEnum } from 'src/app/sharedModels';
import { Subscription } from 'rxjs';
import { Location } from '@angular/common';
import { userState } from 'src/app/states/user/user.selectors';
import { FeatureFlags } from 'src/constants';

const _canteenAdminColumns = [
  'name',
  'email',
  'menuEditor',
  'salesReport',
  'notPrintedReport',
  'notPrintedAlert',
];

const _canteenAdminWithEventColumns = [
  'name',
  'email',
  'menuEditor',
  'salesReport',
  'viewEvent',
  'notPrintedReport',
  'notPrintedAlert',
];

@Component({
  selector: 'admin-settings',
  templateUrl: './admin-settings.component.html',
  styleUrls: ['./admin-settings.component.scss'],
})
export class AdminSettingsComponent implements OnInit {
  selectedCanteen: Canteen;
  currentUser: UserCashless;
  users: CanteenUser[];
  isListLoaded: boolean;
  private subscription: Subscription;
  displayedColumns = [];
  canteenListVisible: boolean = true;
  eventFeatureFlag: boolean = false;

  constructor(
    private _location: Location,
    private store: Store<{ canteen: CanteenState }>,
    private spinnerService: SpinnerService,
    private userService: UserService,
    private featureFlagService: FeatureFlagService
  ) {}

  ngOnInit() {
    this.store.pipe(select(userState)).subscribe((state: UserState) => {
      this.currentUser = state.connectedUser;
    });

    // get flag
    this.featureFlagService.getFlag(FeatureFlags.viewEventManagement, false).then(res => {
      this.eventFeatureFlag = res;
      this.displayColumns();
    });
  }

  ngDestroy() {
    this.subscription.unsubscribe();
  }

  GoBackClick() {
    this._location.back();
  }

  LoadUsers(canteenId) {
    this.userService.GetUsersByCanteenAPI(canteenId).subscribe({
      next: users => {
        if (users.Users) {
          const selectedUser = users.Users.find(serverUser => serverUser.UserId === this.currentUser.UserId);
          const restUsers =
            users.Users.filter(
              serverUser =>
                serverUser.UserId !== this.currentUser.UserId &&
                serverUser.Email != '<EMAIL>'
            ) || [];
          this.users = [selectedUser, ...restUsers];
        }
        this.isListLoaded = true;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
      },
    });
  }

  onCanteenSelect(event) {
    this.spinnerService.start();
    this.selectedCanteen = event;
    
    this.displayColumns();

    this.LoadUsers(event.CanteenId);
  }

  private displayColumns(){
    if(this.eventFeatureFlag && this.selectedCanteen.CanteenType != MerchantTypeEnum.Uniform){
      this.displayedColumns = _canteenAdminWithEventColumns;
    }else{
      this.displayedColumns = _canteenAdminColumns;
    }
  }

  onMarkClick(user: CanteenUser, type: string) {
    this.spinnerService.start();
    // const { CanteenId, IsSaleReportsAvailable, UserId, IsMenuEditorAvailable } = user

    // const commonData = { UserId, CanteenId, IsSaleReportsAvailable, IsMenuEditorAvailable, ResponseDescription: 'OK' }
    // const menuEditorData = { ...commonData, IsMenuEditorAvailable: !IsMenuEditorAvailable }
    // const salesReportData = { ...commonData, IsSaleReportsAvailable: !IsSaleReportsAvailable }

    if (type == 'menuEditor') {
      user.IsMenuEditorAvailable = !user.IsMenuEditorAvailable;
    } else if (type == 'salesReport') {
      user.IsSaleReportsAvailable = !user.IsSaleReportsAvailable;
    } else if (type == 'notPrintedReport') {
      user.IsOrdersNotPrintedReportsAvailable = !user.IsOrdersNotPrintedReportsAvailable;
    } else if(type == 'viewEvent') {
      user.IsEventManagementAvailable = !user.IsEventManagementAvailable;
    } else {
      user.NotifyOrdersNotPrinted = !user.NotifyOrdersNotPrinted;
    }

    this.userService.UpdateCanteenUserSettingsAPI(user).subscribe({
      next: res => {
        this.LoadUsers(this.selectedCanteen.CanteenId);
      },
      error: err => {
        this.spinnerService.stop();
      },
    });
  }

  CanteenListVisibleChanged(isVisible: boolean) {
    this.canteenListVisible = isVisible;
  }
}
