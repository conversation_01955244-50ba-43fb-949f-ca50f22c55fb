import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Location } from '@angular/common';

import { Menu, BaseComponent } from '../../../sharedModels';
import { MenuService, SpinnerService, MenuEditorApiService } from '../../../sharedServices';

@Component({
  selector: 'app-editor-menu',
  templateUrl: './editor-menu.component.html',
  styleUrls: ['./editor-menu.component.scss'],
})
export class EditorMenuComponent extends BaseComponent implements OnInit {
  errorAPI: any;
  form: FormGroup;
  menu: Menu;

  constructor(
    private _location: Location,
    private spinnerService: SpinnerService,
    private menuService: MenuService,
    private menuEditorApi: MenuEditorApiService,
    private activatedRoute: ActivatedRoute
  ) {
    super();
  }

  ngOnInit() {
    this.menu = this.activatedRoute.snapshot.data['menu'];

    if (!this.menu) {
      this.menu = new Menu();
    }

    this.CreateForm();
  }

  get name() {
    return this.form.get('name');
  }

  get friendlyName() {
    return this.form.get('friendlyName');
  }

  get isActive() {
    return this.form.get('isActive');
  }

  getErrorMessageName() {
    return this.form.get('name').hasError('required') ? 'You must enter a value' : '';
  }

  CreateForm() {
    this.form = new FormGroup({
      id: new FormControl(this.menu.MenuId),
      name: new FormControl(this.menu.Name, [Validators.required]),
      friendlyName: new FormControl(this.menu.FriendlyName),
      schoolId: new FormControl(this.menuService.GetSelectedSchooldId()),
      isActive: new FormControl(this.menu.IsActive),
    });
  }

  onSubmit() {
    this.spinnerService.start();

    this.menuEditorApi.UpsertMenuAPI(this.convertObject()).subscribe({
      next: (response: Menu) => {
        this.NavBack();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      },
    });
  }

  private convertObject(): Menu {
    let menu = new Menu();

    menu.MenuId = this.form.get('id').value;
    menu.SchoolId = this.form.get('schoolId').value;
    menu.Name = this.form.get('name').value;
    menu.FriendlyName = this.form.get('friendlyName').value;

    let isActive = this.form.get('isActive').value;

    if (isActive == null) {
      isActive = false;
    }
    menu.IsActive = isActive;
    //menu.MenuType = this.form.get('menuType').value;

    return menu;
  }

  NavBack() {
    this._location.back();
  }
}
