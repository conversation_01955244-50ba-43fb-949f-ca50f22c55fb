import { Component, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

// Model
import { BaseComponent } from 'src/app/sharedModels';

// Service
import { SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'dialog-add-option',
  templateUrl: './dialog-add-option.component.html',
  styleUrls: ['./dialog-add-option.component.scss'],
})
export class DialogAddOptionComponent extends BaseComponent implements OnInit {
  optionAdded: boolean = false;
  isProcessing: boolean = false;

  constructor(public dialogRef: MatDialogRef<DialogAddOptionComponent>) {
    super();
  }

  ngOnInit() {}

  Close() {
    this.dialogRef.close();
  }

  SubmitOption() {}
}
