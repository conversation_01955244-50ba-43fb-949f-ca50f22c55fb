import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { Observable } from 'rxjs';

// services
import { SchoolService } from 'src/app/sharedServices';

export const SchoolMerchantsResolver: ResolveFn<any> = (route: ActivatedRouteSnapshot): Observable<any> => {
  const schoolService = inject(SchoolService);
  let id = route.params['id'];

  if (id == null || id == undefined) {
    id = route.parent.params['id'];
  }

  return schoolService.GetMerchantsPerSchoolAPI(id);
};
