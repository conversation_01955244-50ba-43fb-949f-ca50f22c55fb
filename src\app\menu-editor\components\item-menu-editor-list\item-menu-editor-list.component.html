<h2>Menus</h2>
<div class="cardDefaultCanteen">
  <div class="row">
    <div class="col-12 noPadding">
      <table>
        <div class="menuSchoolContainer">
          <thead>
            <th>Menu type</th>
            <th>School</th>
            <th>Menu name</th>
          </thead>
          <tbody>
            <tr
              *ngFor="let menu of menuList"
              class="line"
              trackBy:menu.MenuId
              [ngClass]="{ lineSelected: menu.IsSelected }"
            >
              <td>{{ menu.MenuName | merchantMenuName }}</td>
              <td>{{ menu.SchoolName }}</td>
              <td>{{ menu.EventName }}</td>
              <td>
                <mat-icon
                  *ngIf="!menu.IsSelected"
                  matTooltip="Add"
                  class="actionTableau"
                  (click)="UpdateLinkMenuMenu(menu.MenuId, true)"
                  >add</mat-icon
                >
                <mat-icon
                  *ngIf="menu.IsSelected"
                  matTooltip="Remove"
                  class="actionTableau"
                  (click)="UpdateLinkMenuMenu(menu.MenuId, false)"
                  >delete</mat-icon
                >
              </td>
            </tr>
          </tbody>
        </div>
      </table>
    </div>
  </div>
</div>
