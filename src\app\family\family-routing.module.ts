import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

// components
import { FamilyNavBarComponent, FamilyComponent } from './components';

//Service
import { ListStudentsResolver } from '../sharedServices';
import { FamilyArticlesComponent } from './components/articles/articles.component';

const routes: Routes = [
  {
    path: '',
    component: FamilyNavBarComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'home',
      },
      {
        path: 'home',
        component: FamilyComponent,
        resolve: { children: ListStudentsResolver },
      },
      {
        path: 'order',
        loadChildren: () => import('../manage-order/manage-order.module').then(m => m.ManageOrderModule),
      },
      {
        path: 'children',
        loadChildren: () =>
          import('../manage-children/manage-children.module').then(m => m.ManageChildrenModule),
      },
      {
        path: 'account',
        loadChildren: () => import('../account/account.module').then(m => m.AccountModule),
      },
      {
        path: 'articles',
        component: FamilyArticlesComponent,
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class FamilyRoutingModule {}
