@import '../../../../styles/cashless-theme.scss';

.center-text {
  text-align: center;
}

.infoContainer {
  border: 1px solid $grey-1;
  border-radius: 8px;
  padding: 12px;

  .groupMessage {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
  }
}

.result {
  height: 60%;

  .formContainer {
    max-height: 60%;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.spinner {
  justify-content: center;
  padding-top: 100px;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background-color: rgb(79, 79, 79, 0.4);
  align-self: center;
  display: flex;
}
