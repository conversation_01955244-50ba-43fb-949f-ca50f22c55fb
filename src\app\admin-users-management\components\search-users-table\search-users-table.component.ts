import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { BasePaginatorComponent, UserCashless } from 'src/app/sharedModels';
import { PaginatorChange } from 'src/app/sharedModels/base/paginatorChange';
import { AdminService } from 'src/app/sharedServices';

const _columns = ['id', 'firstname', 'lastname', 'role', 'Mobile', 'email', 'options'];

@Component({
  selector: 'search-users-table',
  templateUrl: './search-users-table.component.html',
  styleUrls: ['./search-users-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchUsersTableComponent
  extends BasePaginatorComponent<UserCashless>
  implements OnInit, OnChanges
{
  @Input() data: UserCashless[];
  @Input() selectedPage: number = 0;
  @Input() numberRows: number;
  @Output() pageChanged: EventEmitter<PaginatorChange> = new EventEmitter();

  constructor(private adminService: AdminService) {
    super(_columns);
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    for (const propName in changes) {
      switch (propName) {
        case 'data':
          this.dataSource.data = this.data;
          if (this.data && this.data[0]) {
            this.totalRows = this.data[0].TotalRows;
          } else {
            this.totalRows = 0;
          }
          break;

        default:
          break;
      }
    }
  }

  pageChangedEvent(page: PaginatorChange): void {
    this.pageChanged.emit(page);
  }

  getRoleText(role: number) {
    return this.adminService.GetRoleText(role.toString());
  }
}
