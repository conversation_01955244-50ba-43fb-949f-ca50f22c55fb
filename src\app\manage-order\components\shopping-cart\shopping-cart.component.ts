import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { DeviceDetectorService } from 'ngx-device-detector';
import * as _ from 'lodash';

// Ngrx
import { Store, select } from '@ngrx/store';
import { FamilyState } from '../../../states';
import * as cartActions from '../../../states/shoppingCart/shopping-cart.actions';

// Models
import {
  UserCashless,
  MenuTypeEnum,
  PlaceOrderDialogData,
  ConfirmModal,
  CartItem,
  FamilyDayOrders,
  RefinedOrder,
} from '../../../sharedModels';

// Services
import { SpinnerService, UserService, AdminService } from '../../../sharedServices';

// Components
import { DialogPlaceOrderComponent } from '../dialog-place-order/dialog-place-order.component';
import { DialogConfirmComponent } from '../../../shared/components/';
import { Subscription } from 'rxjs';
import { editCartNotChanged, getCartItems } from 'src/app/states/shoppingCart/shopping-cart.selectors';
import { GetCartItemsPrice } from '../../functions/calculate-price';
import { GetGroupedShoppingCart } from '../../functions/convert-to-cart-items';
import { dayDetail } from 'src/app/states/family/family.selectors';

@Component({
  selector: 'shopping-cart',
  templateUrl: './shopping-cart.component.html',
  styleUrls: ['./shopping-cart.component.scss'],
})
export class ShoppingCartComponent implements OnInit, OnDestroy {
  //this component shouldn't receive @Input because the inputs are not received during on mobile view (different screen layout)

  shoppingCartForDisplay: CartItem[][] = [];
  isMobile: boolean = false;
  isAdminCanteenUser: boolean = false;
  studentParent: UserCashless;
  showPlaceOrder: boolean = false;
  priceCart: number = 0;
  fullScreenDialog: boolean = false;
  textPlaceOrder: string = 'Place Order';
  menuTypeEnum = MenuTypeEnum;
  availableParentFunds: number;
  connectedUser: UserCashless;
  cartPrice: number;
  editCartNotChanged: boolean = true;
  parentBalanceRemaining: number;
  orderToEdit: RefinedOrder;

  subscriptionCart$: Subscription;
  subscriptionEditCart$: Subscription;
  subscriptionDayDetail$: Subscription;

  constructor(
    private store: Store<{ family: FamilyState }>,
    private spinnerService: SpinnerService,
    private route: ActivatedRoute,
    private location: Location,
    public dialog: MatDialog,
    private deviceService: DeviceDetectorService,
    private userService: UserService,
    private adminService: AdminService
  ) {}

  ngOnInit(): void {
    this.connectedUser = this.userService.GetUserConnected();

    this.route.data.subscribe((data: { isMobile: boolean }) => {
      this.isMobile = data.isMobile;
    });

    this.subscriptionCart$ = this.store.pipe(select(getCartItems)).subscribe((cartItems: CartItem[]) => {
      this.showPlaceOrder = cartItems?.length > 0;
      this.shoppingCartForDisplay = GetGroupedShoppingCart(cartItems);
      this.cartPrice = GetCartItemsPrice(cartItems);
    });

    this.subscriptionEditCart$ = this.store
      .pipe(select(editCartNotChanged))
      .subscribe((editCartNotChanged: boolean) => {
        this.editCartNotChanged = editCartNotChanged;
      });

    this.subscriptionDayDetail$ = this.store
      .pipe(select(dayDetail))
      .subscribe((dayDetail: FamilyDayOrders) => {
        if (dayDetail?.OrderToEdit) {
          this.textPlaceOrder = 'Edit Order';
          this.orderToEdit = dayDetail.OrderToEdit;
        }
      });

    if (this.userService.IsCanteenOrAdmin()) {
      this.setUpOrderForAdminOrMerchant();
    }
  }

  ngOnDestroy(): void {
    this.subscriptionCart$?.unsubscribe();
    this.subscriptionEditCart$?.unsubscribe();
    this.subscriptionDayDetail$?.unsubscribe();
  }

  setUpOrderForAdminOrMerchant(): void {
    this.isAdminCanteenUser = true;
    this.studentParent = this.adminService.GetParent();

    if (!this.studentParent) {
      this.GoBackClick();
    } else {
      const parentBalance = Number(this.studentParent.SpriggyBalance);

      const orderToEditPrice = this.orderToEdit ? this.orderToEdit.Price : 0;
      this.parentBalanceRemaining = parentBalance + orderToEditPrice;
    }
  }

  GoBackClick(): void {
    this.location.back();
  }

  ClickCancelChanges(): void {
    this.store.dispatch(cartActions.restoreOldShoppingCart());
  }

  isEditOrderWithNoChanges(): boolean {
    return this.orderToEdit && this.editCartNotChanged;
  }

  canCancelChanges(): boolean {
    return !this.isMobile && !this.isEditOrderWithNoChanges();
  }

  disableConfirmButton(): boolean {
    const orderPriceMoreThanWalletBalance = this.cartPrice > this.parentBalanceRemaining;
    const adminNotEnoughFunds = this.isAdminCanteenUser && orderPriceMoreThanWalletBalance;
    return this.isEditOrderWithNoChanges() || adminNotEnoughFunds;
  }

  //////////////////////////////////////////////////
  // Cart
  //////////////////////////////////////////////////
  ClearCart(): void {
    this.store.dispatch(cartActions.clearCart());
  }

  confirmClearCart(): void {
    const data = new ConfirmModal();
    data.Title = 'Clear all?';
    data.Text = 'This will remove all items from your cart. Do you want to continue?';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, clear all';

    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.ClearCart();
      }
    });
  }

  RemoveFromCart(itemCartId: number): void {
    this.store.dispatch(cartActions.removeItem({ itemCartId: itemCartId }));
  }

  InputChanged(itemCartId: number, event): void {
    const quantity = event.target.value;
    this.store.dispatch(cartActions.updateQuantity({ itemCartId, quantity: quantity }));
  }

  //////////////////////////////////////////////////
  // Order
  //////////////////////////////////////////////////

  OrderClick(): void {
    let dialogRef;
    this.fullScreenDialog = this.deviceService.isMobile();
    this.spinnerService.start();

    let placeOrderData: PlaceOrderDialogData = {
      groupedCarts: this.shoppingCartForDisplay,
      editOrderId: this.orderToEdit ? this.orderToEdit.OrderId : 0,
    };

    if (this.fullScreenDialog) {
      dialogRef = this.dialog.open(DialogPlaceOrderComponent, {
        maxWidth: '100vw',
        maxHeight: '100vh',
        height: '100%',
        width: '100%',
        panelClass: 'custom-dialog-container',
        disableClose: true,
        data: placeOrderData,
      });
    } else {
      dialogRef = this.dialog.open(DialogPlaceOrderComponent, {
        width: '500px',
        disableClose: true,
        data: placeOrderData,
      });
    }
    this.spinnerService.stop();

    dialogRef.afterClosed().subscribe((errorOccurred: boolean) => {
      if (errorOccurred) {
        this.showErrorDialog();
      }
    });
  }

  showErrorDialog(): void {
    let data = new ConfirmModal();
    data.Title = `Something went wrong`;
    data.Text = `Try again in a minute. If it’s still not working give Member Help a shout.`;
    data.ConfirmButton = 'Ok';

    this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });
  }
}
