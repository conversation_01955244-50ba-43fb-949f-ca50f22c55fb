import { Component } from '@angular/core';

// Models
import { SortedOrderHistory } from '../../../sharedModels';
import { ActivatedRoute } from '@angular/router';
import { CommonOrderHistoryClass } from '../common-order-history/common-order-history';

@Component({
  selector: 'app-event-history',
  templateUrl: './event-history.component.html',
  styleUrls: ['./event-history.component.scss'],
})
export class EventHistoryComponent extends CommonOrderHistoryClass {
  listOrders: SortedOrderHistory[] = [];
  loading: boolean = false;

  constructor(protected route: ActivatedRoute) {
    super(route);
  }

  ngOnInit() {
    this.onInitFunction();
  }
}
