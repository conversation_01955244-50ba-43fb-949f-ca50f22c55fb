<div class="container-fluid pt-4">
  <canteen-order-filter (filtersChanged)="FiltersChanged($event)" (settingsChanged)="SettingsChanged($event)">
  </canteen-order-filter>

  <div class="row mt-4">
    <div class="col-sm-7 d-flex justify-content-between align-items-end">
      <div class="d-flex justify-content-between align-items-end">
        <h3 style="margin: 0">
          <strong>Labels</strong> <span style="font-weight: 400"> ({{ this.totalRows }})</span>
        </h3>
        <h4 style="margin: 0" *ngIf="isUniformCanteen" class="labelCount">
          Selected ({{ selection.selected.length }})
        </h4>
      </div>

      <div class="d-flex">
        <div *ngIf="isUniformCanteen">
          <uniform-order-status-picker
            [selectedOrders]="selection.selected"
            (clearRowSelection)="clearRowSelection()"
            (loadTableData)="LoadTableData()"
          ></uniform-order-status-picker>
        </div>

        <basic-button text="Print Labels" (onPress)="PrintAllLabels()" [buttonStyle]="1"></basic-button>

        <a id="canteenPrinter" style="color: white"></a>
      </div>
    </div>
  </div>

  <!-- table orders column -->
  <div class="row mt-2">
    <div class="col-sm-7 pb-3">
      <div class="table-container mat-elevation-z6">
        <table
          mat-table
          [dataSource]="dataSource"
          class="mat-elevation-z8 tableau"
          matSort
          matSortActive="ClassName"
          matSortDisableClear
          matSortDirection="desc"
          (matSortChange)="SortChange($event)"
        >
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox
                (change)="$event ? masterToggle() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()"
                [aria-label]="checkboxLabel()"
              >
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox
                (click)="selection.toggle(row)"
                (change)="$event ? selection.toggle(row) : null"
                [checked]="selection.isSelected(row)"
                [aria-label]="checkboxLabel(row)"
              >
              </mat-checkbox>
            </td>
          </ng-container>

          <ng-container matColumnDef="OrderId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header disableClear>No.</th>
            <td mat-cell *matCellDef="let element">{{ element.OrderId }}</td>
          </ng-container>

          <ng-container matColumnDef="CanteenStatus">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let element">{{ element.CanteenStatus }}</td>
          </ng-container>

          <ng-container matColumnDef="SchoolName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header disableClear>School</th>
            <td mat-cell *matCellDef="let element">{{ element.SchoolName }}</td>
          </ng-container>

          <ng-container matColumnDef="MenuType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header disableClear>Order Type</th>
            <td mat-cell *matCellDef="let element">{{ element.MenuType | merchantMenuName }}</td>
          </ng-container>

          <ng-container matColumnDef="StudentName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header disableClear>Student</th>
            <td mat-cell *matCellDef="let element">
              <a
                matTooltip="View Child"
                routerLink="./../students/details/{{ element.StudentId }}"
                class="student-link"
                >{{ element.StudentName }}</a
              >
            </td>
          </ng-container>

          <ng-container matColumnDef="ClassName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header disableClear>Class</th>
            <td mat-cell *matCellDef="let element">{{ element.ClassName }}</td>
          </ng-container>

          <ng-container matColumnDef="OrderDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header [disabled]="!isUniformCanteen">
              Order Date
            </th>
            <td mat-cell *matCellDef="let element">
              {{
                isUniformCanteen
                  ? (element.OrderDate | date : 'EEEE d MMMM y')
                  : (element.OrderDate | date : 'EEEE d MMMM')
              }}
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
          <tr
            mat-row
            *matRowDef="let row; columns: displayedColumns"
            (click)="RowClick(row)"
            [ngClass]="{ selectedRow: isRowSelected(row) }"
          ></tr>
        </table>
      </div>
      <mat-paginator
        [pageSize]="25"
        [pageSizeOptions]="[25, 50, 100]"
        [length]="totalRows"
        [pageIndex]="listfilters.PageIndex"
        (page)="PageChange($event)"
      ></mat-paginator>
    </div>

    <!-- selected order column -->
    <div class="col-sm-5 pb-3">
      <div class="row">
        <div class="col-12">
          <selected-order
            [order]="selectedOrder"
            [showOrder]="showSelectedOrder()"
            [itemsSelectedOrder]="itemsSelectedOrder"
            (printOrder)="PrintSelectedOrder()"
          ></selected-order>
        </div>
      </div>
    </div>
  </div>
</div>
