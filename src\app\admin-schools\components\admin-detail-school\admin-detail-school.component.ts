import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AbstractControl, FormControl, FormGroup, Validators } from '@angular/forms';
import { Location } from '@angular/common';

// Service
import { SpinnerService, SchoolService } from 'src/app/sharedServices';

// Models
import {
  BaseComponent,
  PricingModelEnum,
  Canteen,
  State,
  ResultDialogData,
  SchoolDto,
} from 'src/app/sharedModels';

//dialog imports
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { DialogResultComponent } from 'src/app/shared/components/';

@Component({
  selector: 'app-admin-detail-school',
  templateUrl: './admin-detail-school.component.html',
  styleUrls: ['./admin-detail-school.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdminDetailSchoolComponent extends BaseComponent implements OnInit {
  school: SchoolDto;
  listStates: State[] = [];
  form: FormGroup;
  showError: boolean = false;
  pricingEnum = PricingModelEnum;
  canteens: Canteen[];

  constructor(
    private activatedRoute: ActivatedRoute,
    private spinnerService: SpinnerService,
    private schoolService: SchoolService,
    private location: Location,
    public dialog: MatDialog
  ) {
    super();
  }

  ngOnInit(): void {
    this.school = this.activatedRoute.snapshot.data['school'];
    this.listStates = this.activatedRoute.snapshot.data['states'];
    this.canteens = this.activatedRoute.snapshot.data['canteens'];

    this._createForm();
  }

  ///////////////////////////////////
  // Form
  ///////////////////////////////////

  private _createForm(): void {
    let canteenId = null;

    if (!this.school) {
      this.setSchoollDefaultValues();

      canteenId = this.canteens[0].CanteenId;
    }

    this.form = new FormGroup({
      name: new FormControl(this.school.Name, [Validators.required, Validators.maxLength(200)]),
      active: new FormControl(this.school.IsActive),
      marketingFree: new FormControl(this.school.IsMarketingFree),
      canteen: new FormControl(canteenId),
      state: new FormControl(this.school.StateId),
      deactivatedFilters: new FormControl(this.school.DeactivatedFilters),
      totalStudentEst: new FormControl(this.school.TotalStudentEst, Validators.min(0)),
    });
  }

  setSchoollDefaultValues(): void {
    this.school = {
      SchoolId: null,
      Name: '',
      StateId: this.listStates[0].StateId,
      DeactivatedFilters: '',
      IsActive: true,
      IsMarketingFree: false,
      TotalStudentEst: 0,
    };
  }

  get name(): AbstractControl<any, any> {
    return this.form.get('name');
  }
  get canteen(): AbstractControl<any, any> {
    return this.form.get('canteen');
  }
  get active(): AbstractControl<any, any> {
    return this.form.get('active');
  }
  get marketingFree(): AbstractControl<any, any> {
    return this.form.get('marketingFree');
  }
  get deactivatedFilters(): AbstractControl<any, any> {
    return this.form.get('deactivatedFilters');
  }
  get state(): AbstractControl<any, any> {
    return this.form.get('state');
  }
  get totalStudentEst(): AbstractControl<any, any> {
    return this.form.get('totalStudentEst');
  }

  onSubmit(): void {
    if (this.school.SchoolId) {
      this.confirmSubmit();
      return;
    }
    this.areYouSurePopup();
  }

  confirmSubmit(): void {
    this.spinnerService.start();

    this._convertToItemObject();

    if (this.school.SchoolId > 0) {
      this.editSchoolAPICall();
    } else {
      this.createSchoolAPICall();
    }
  }

  editSchoolAPICall(): void {
    this.schoolService.EditApi(this.school).subscribe({
      next: () => {
        this.spinnerService.stop();
        this.successPopup('School has been successfully updated.');
      },
      error: error => {
        this.spinnerService.stop();
        this.SomethingWentWrongPopup('We were unable to update this school.');
        this.handleErrorFromService(error);
      },
    });
  }

  createSchoolAPICall(): void {
    this.schoolService.CreateApi(this.school, this.canteen.value).subscribe({
      next: (response: number) => {
        this.school.SchoolId = response;
        this.spinnerService.stop();
        let dialogRef = this.successPopup('School has been successfully created.');
        dialogRef.afterClosed().subscribe(result => {
          this.location.back();
        });
      },
      error: error => {
        this.spinnerService.stop();
        let errorMessage =
          typeof error?.errors?.Name[0] === 'string'
            ? error?.errors?.Name[0]
            : 'We were unable to create this school.';
        this.SomethingWentWrongPopup(errorMessage);
      },
    });
  }

  /** Put the values of the form into the MenuItem */
  private _convertToItemObject(): void {
    this.school.Name = this.name.value;
    this.school.IsActive = this.active.value;
    this.school.DeactivatedFilters = this.deactivatedFilters.value;
    this.school.IsMarketingFree = this.marketingFree.value;
    this.school.StateId = this.state.value;
    this.school.TotalStudentEst = this.totalStudentEst.value || 0;
  }

  ///////////////////////////////////
  // view functions
  ///////////////////////////////////
  getErrorMessageName(): string {
    return 'Please enter a valid input';
  }

  GetTextSubmitButton(): string {
    return this.school.SchoolId ? 'Save' : 'Add';
  }

  ///////////////////////////////////
  // Pop ups
  ///////////////////////////////////
  successPopup(text: string): MatDialogRef<DialogResultComponent, any> {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = text;
    data.ConfirmButton = 'Okay';

    return this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });
  }

  SomethingWentWrongPopup(text: string): void {
    let data = new ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = text;
    data.TextLine2 = 'Please try again.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.confirmSubmit();
      }
    });
  }

  areYouSurePopup(): void {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Are you sure?';
    data.TextLine1 = 'Are you sure you want to create a new school?';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, Create';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.confirmSubmit();
      }
    });
  }
}
