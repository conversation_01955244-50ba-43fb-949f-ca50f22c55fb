import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// modules
import { SchoolsEventsModule } from '../schools-events/schools-events.module';
import { AdminSchoolsRoutingModule } from './admin-schools-routing.module';
import { AccountModule } from '../account/account.module';
import { SharedModule } from '../shared/shared.module';
import { SharedToolsModule } from '../shared-tools/shared-tools.module';
import { SchoolsCommonModule } from '../schools-common/schools-common.module';
import { SchoolsFormModule } from '../schools-form/schools-form.module';
import { SchoolsButtonModule } from '../schools-button/schools-button.module';

// material
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';

import {
  SchoolNavBarComponent,
  AdminSchoolClassesComponent,
  AddSchoolClassComponent,
  ListClassesComponent,
  AdminListSchoolsComponent,
  AdminDetailSchoolComponent,
  AdminEventManagementComponent,
  AdminSchoolFeaturesComponent,
  StringArrayFormatPipe,
} from './components';

@NgModule({
  declarations: [
    SchoolNavBarComponent,
    AdminSchoolClassesComponent,
    AddSchoolClassComponent,
    ListClassesComponent,
    AdminListSchoolsComponent,
    AdminDetailSchoolComponent,
    AdminEventManagementComponent,
    AdminSchoolFeaturesComponent,
    StringArrayFormatPipe,
  ],
  imports: [
    CommonModule,
    AdminSchoolsRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    AccountModule,
    SharedModule,
    SharedToolsModule,
    SchoolsCommonModule,
    SchoolsFormModule,
    SchoolsButtonModule,
    // material
    MatIconModule,
    MatCheckboxModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatCardModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatRadioModule,
    MatSelectModule,
    MatTableModule,
    MatPaginatorModule,
    MatButtonModule,
    SchoolsEventsModule,
  ],
})
export class AdminSchoolsModule {}
