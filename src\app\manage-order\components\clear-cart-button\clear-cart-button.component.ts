import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'clear-cart-button',
  standalone: true,
  imports: [MatIconModule, CommonModule],
  templateUrl: './clear-cart-button.component.html',
  styleUrls: ['./clear-cart-button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ClearCartButtonComponent {
  @Output() pressed = new EventEmitter();
  @Input() showButton: boolean;

  buttonPressed() {
    this.pressed.emit();
  }
}
