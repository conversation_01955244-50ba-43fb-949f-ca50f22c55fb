@import '../../../../styles/cashless-theme.scss';

.table-container {
  padding: 0;
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

.tableau {
  width: 100%;
}

.titleFilters {
  font-size: 18px;
  margin-bottom: 0;
  color: $orange-2;
}

.filterDiv {
  display: inline-block;
  margin-left: 20px;

  & button {
    display: inline-block;

    width: 150px;
    height: 30px;
    vertical-align: middle;
    font-size: 18px;
    margin-left: 20px;
  }

  & mat-form-field {
    width: 200px;
    display: inline-block;
  }
}

.updateStatusDiv {
  & h3 {
    color: $orange-2;
    margin-top: 5px;
    font-size: 22px;
  }

  & button {
    width: 120px;
    height: 35px;
    font-size: 18px;
    margin-left: 40px;
  }
}

.singleOrderPrinting {
  height: 40px;
  width: 200px;
  margin-top: 30px;
}

.selectAllLink {
  padding-bottom: 5px;

  & a {
    font-size: 14px;
    color: $orange-2;
    cursor: pointer;
  }
}

.selectedRow {
  background-color: $orange-4;
}

.mat-mdc-row {
  cursor: pointer;
}

.mat-mdc-row:hover {
  background-color: $orange-9;
}

.student-link {
  color: $blue-2;
  font-weight: 700;
}

.print-btn {
  background-color: $grey-14;
  color: #ffffff;
  font-size: 16px;
  text-align: center;
  font-weight: 700;
  padding: 5px 10px;
  margin: 0;
  line-height: 16px;
}
.labelCount {
  padding-left: 15px;
  color: $grey-17;
  font-size: 16px;
}
.status-menu-btn {
  line-height: 16px;
  font-size: 16px;
  padding: 0 5px;
  margin: 0;
}
/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
.table-container ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background-color: $grey-14;
}
/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
.table-container ::ng-deep .mat-mdc-checkbox.mat-accent.mat-checkbox-indeterminate .mat-checkbox-background {
  background-color: $grey-14;
}
/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
.table-container ::ng-deep.mat-mdc-checkbox .mat-checkbox-frame {
  border-color: $grey-14;
}
