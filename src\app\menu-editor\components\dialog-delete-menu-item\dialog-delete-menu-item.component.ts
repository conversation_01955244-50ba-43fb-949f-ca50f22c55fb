import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-dialog-delete-menu-item',
  templateUrl: './dialog-delete-menu-item.component.html',
  styleUrls: ['./dialog-delete-menu-item.component.scss'],
})
export class DialogDeleteMenuItemComponent implements OnInit {
  constructor(
    public dialogRef: MatDialogRef<DialogDeleteMenuItemComponent>,
    @Inject(MAT_DIALOG_DATA) public data
  ) {}

  ngOnInit() {}

  onDeletePress() {
    this.data.onDeletePress();
    this.dialogRef.close();
  }

  onNoClick(): void {
    this.dialogRef.close();
  }
}
