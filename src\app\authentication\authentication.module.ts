import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// google material
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

import { AuthenticationRoutingModule } from './authentication-routing.module';
import { LoginComponent } from './components/login/login.component';

//import { CashlessCoreModule } from 'cashless-core';
import { AuthenticationComponent } from './components/authentication/authentication.component';
import { ResetComponent } from './components/reset/reset.component';
@NgModule({
  declarations: [LoginComponent, AuthenticationComponent, ResetComponent],
  imports: [
    CommonModule,
    AuthenticationRoutingModule,
    //CashlessCoreModule,
    FormsModule,
    ReactiveFormsModule,
    // material
    MatFormFieldModule,
    MatCheckboxModule,
    MatInputModule,
    MatButtonModule,
  ],
})
export class AuthenticationModule {}
