import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import moment from 'moment';

//Models
import { BaseComponent, ConfirmModal, DateSchool } from 'src/app/sharedModels';

//Services
import { SchoolService, SpinnerService } from 'src/app/sharedServices';

//Dialog
import { DialogConfirmComponent } from 'src/app/shared/components';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'school-closing-date-form',
  templateUrl: './school-closing-date-form.component.html',
  styleUrls: ['./school-closing-date-form.component.scss'],
})
export class SchoolClosingDateFormComponent extends BaseComponent implements OnInit {
  @Input() schoolId: number;
  form: FormGroup;
  schoolClosingDates: DateSchool[] = [];

  constructor(
    private spinnerService: SpinnerService,
    private schoolService: SchoolService,
    public dialog: MatDialog
  ) {
    super();
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.schoolId?.currentValue) {
      this.getCloseDates(this.schoolId);
      this._createForm();
    }
  }

  private _createForm() {
    if (this.form) {
      return;
    }
    this.form = new FormGroup({
      closeStartDate: new FormControl(null, Validators.required),
      closeEndDate: new FormControl(null, Validators.required),
    });
  }

  submitForm() {
    if (this.form.invalid) {
      return;
    }
    this.spinnerService.start();

    let data = new DateSchool();
    data.StartDate = this.formatDate(this.closeStartDate.value);
    data.EndDate = this.formatDate(this.closeEndDate.value);
    data.SchoolId = this.schoolId;

    this.schoolService.UpsertSchoolCloseDatesByIdApi(data).subscribe({
      next: res => {
        this.getCloseDates(this.schoolId);
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  formatDate(date: string): Date {
    return new Date(moment(date).format('YYYY-MM-DD'));
  }

  getCloseDates(schoolId: number) {
    this.schoolService.GetSchoolCloseDatesByIdApi(schoolId).subscribe(CloseDates => {
      this.schoolClosingDates = CloseDates;
    });
  }

  archiveClicked(dateId: number) {
    let data = new ConfirmModal();
    data.Title = 'Delete School Closing Date';
    data.Text = 'Deleting this record cannot be undone, are you sure you want to delete it?';
    data.CancelButton = 'No';
    data.ConfirmButton = 'Yes';

    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.deleteSchoolDate(dateId);
      }
    });
  }

  deleteSchoolDate(dateId: number) {
    this.spinnerService.start();
    this.schoolService.DeleteSchoolCloseDatesByDateIdApi(dateId).subscribe({
      next: res => {
        this.getCloseDates(this.schoolId);
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  get closeStartDate() {
    return this.form.get('closeStartDate');
  }
  get closeEndDate() {
    return this.form.get('closeEndDate');
  }
}
