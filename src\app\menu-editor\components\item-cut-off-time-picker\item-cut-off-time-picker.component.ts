import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

//Models
import { ItemCutOffTimeTypes } from 'src/app/sharedModels';

@Component({
  selector: 'item-cut-off-time-picker',
  templateUrl: './item-cut-off-time-picker.component.html',
  styleUrls: ['./item-cut-off-time-picker.component.scss'],
})
export class ItemCutOffTimePickerComponent implements OnInit {
  @Input() cutOffTimeVal: string;
  @Input() cutOffTimeTypeVal: string;
  @Input() form: FormGroup;
  @Input() newTheme: boolean = false;
  ItemCutOffTimeTypes = ItemCutOffTimeTypes;

  CUT_OFF_TIME_LIST: number[] = [];
  STARTING_VAL: number = 0.5;
  ENDING_VAL: number = 24;
  INCREMENT_VAL: number = 0.5;
  SPECIFIC_CUT_OFF_TIME_VALUES: number[] = [0.25, 48, 72, 96, 120];

  constructor() {}

  ngOnInit(): void {
    this._addControlsToForm();
    this.populateSelectListValues();
  }

  private _addControlsToForm(): void {
    let hasCutOffTime: boolean = this.cutOffTimeVal != null;

    this.form.addControl('hasCutOffTime', new FormControl(hasCutOffTime));
    this.form.addControl('cutOffTimeType', new FormControl(this.cutOffTimeTypeVal));
    this.form.addControl('cutOffTime', new FormControl(this.cutOffTimeVal));

    this.hasCutOffTime.valueChanges.subscribe(val => {
      if (val) {
        this.cutOffTime.setValue(this.CUT_OFF_TIME_LIST[0]);
        this.cutOffTimeType.setValue(this.ItemCutOffTimeTypes.Early);
      } else {
        this.cutOffTime.setValue(null);
        this.cutOffTimeType.setValue(null);
      }
    });
  }

  populateSelectListValues() {
    for (let i = this.STARTING_VAL; i <= this.ENDING_VAL; i = i + this.INCREMENT_VAL) {
      this.CUT_OFF_TIME_LIST.push(i);
    }
    this.SPECIFIC_CUT_OFF_TIME_VALUES.forEach(time => this.CUT_OFF_TIME_LIST.push(time));

    this.CUT_OFF_TIME_LIST.sort((a, b) => a - b);
  }

  get hasCutOffTime() {
    return this.form.get('hasCutOffTime');
  }
  get cutOffTimeType() {
    return this.form.get('cutOffTimeType');
  }
  get cutOffTime() {
    return this.form.get('cutOffTime');
  }
}
