import { Component, OnInit, OnDestroy } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { Subscription } from 'rxjs';

import { SchoolClass } from '../../../sharedModels';

import { AdminService } from '../../../sharedServices';

@Component({
  selector: 'admin-list-classes',
  templateUrl: './list-classes.component.html',
  styleUrls: ['./list-classes.component.scss'],
})
export class ListClassesComponent implements OnInit, OnDestroy {
  displayedColumns: string[] = ['id', 'name', 'teacher', 'yearGroup', 'options'];
  private listClasses: SchoolClass[];
  private addClassSbuscription: Subscription;
  dataSource = new MatTableDataSource<SchoolClass>();

  constructor(private adminService: AdminService) {}

  ngOnInit() {
    this.RefreshTable(this.adminService.GetListClasses());

    this.addClassSbuscription = this.adminService.classesListUpdatedEvent$.subscribe(listClasses => {
      // Refresh table
      this.RefreshTable(listClasses);
    });
  }

  ngOnDestroy() {
    this.addClassSbuscription.unsubscribe();
  }

  EditSchool(schoolClass: SchoolClass) {
    this.adminService.EditClassRequest(schoolClass);
  }

  RefreshTable(listClasses: SchoolClass[]) {
    this.listClasses = listClasses;
    this.dataSource.data = this.listClasses;
  }
}
