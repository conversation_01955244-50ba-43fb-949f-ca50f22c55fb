import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';

//Models
import { Canteen } from 'src/app/sharedModels';

@Component({
  selector: 'notice-header',
  templateUrl: './notice-header.component.html',
  styleUrls: ['./notice-header.component.scss'],
})
export class NoticeHeaderComponent implements OnInit {
  @Input() title: string;
  @Output() schoolChanged: EventEmitter<number> = new EventEmitter();
  @Output() merchantChanged: EventEmitter<Canteen> = new EventEmitter();
  @Output() openForm = new EventEmitter();

  constructor() {}

  ngOnInit(): void {}

  OnSchoolSelect(event: number) {
    this.schoolChanged.emit(event);
  }

  OnMerchantChange(event: Canteen) {
    this.merchantChanged.emit(event);
  }

  openNoticeForm() {
    this.openForm.emit();
  }
}
