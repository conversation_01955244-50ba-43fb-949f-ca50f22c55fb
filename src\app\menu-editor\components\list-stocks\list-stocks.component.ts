import { Component, OnInit } from '@angular/core';

// Models
import { Canteen } from 'src/app/sharedModels';

// Services
import { MenuEditorApiService, SpinnerService } from 'src/app/sharedServices';
import { StockManagement } from 'src/app/sharedModels/stocks/stockManagement';

const _columns = ['id', 'name', 'dailyStock', 'stock', 'isActive', 'actions'];

@Component({
  selector: 'editor-list-stocks',
  templateUrl: './list-stocks.component.html',
  styleUrls: ['../editor-table-page.scss'],
})
export class ListStocksComponent extends StockManagement implements OnInit {
  constructor(menuEditorAPIService: MenuEditorApiService, spinnerService: SpinnerService) {
    super(_columns, menuEditorAPIService, spinnerService);
  }

  ngOnInit() {}

  CanteenChangedEvent(canteen: Canteen) {
    this.CanteenChanged(canteen);
  }
}
