@import '../../../../styles/cashless-breakpoints.scss';
@import '../../../../styles/cashless-theme.scss';

@import '../../../../styles/cashless-select.scss';

h3 {
  margin-left: 20px;
  vertical-align: middle;
}

.title {
  font-size: 24px;
  margin: 10px 10px 16px 6px;
  padding: 0;
}

.listMenu {
  margin: 0;
  margin-bottom: 8px;
  padding: 14px 0;

  @media (max-width: $breakpoint-sm) {
    margin-top: 0px;
    margin-bottom: 5px;
  }

  li {
    display: inline-block;
    text-decoration: none;
  }

  .categoryContainers {
    @media (max-width: $breakpoint-md) {
      padding: 0;
      padding-left: 10px;
    }
  }
}

.itemsContainers {
  @media (max-width: $breakpoint-md) {
    padding: 0;
  }
}

.item {
  @media (min-width: $breakpoint-md) {
    margin-bottom: 14px;
  }

  @media (max-width: $breakpoint-md) {
    padding: 0;
  }

  .card {
    height: auto;
    cursor: pointer;
    padding: 0;
  }
}

.noItemAfterFilter {
  padding-left: 10px;

  & h4 {
    font-size: 18px;
    color: $orange-3;
  }
}

.placeOrderMobileLink {
  display: inline-block;
  width: 100%;
  height: 70px;
  position: fixed;
  bottom: 0px;

  background: linear-gradient(96.62deg, $orange-1 0%, $orange-2 100%);
  color: white;
  text-align: center;
  padding-top: 20px;
  text-decoration: none;
}

// Bug fixing to follow
.noMarginRight {
  margin-right: 0px;
}

.backButton {
  font-size: 20px;
  vertical-align: middle;
  // margin-right: 2px;
}

.noMenuAvailable {
  padding-top: 50px;
  text-align: center;
}

.mobileFilterCol {
  text-align: center;
}

.uniformSelectList {
  margin: 15px;
}
