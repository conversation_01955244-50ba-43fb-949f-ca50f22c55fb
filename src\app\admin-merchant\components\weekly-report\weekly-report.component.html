<school-panel>
  <p class="mb-0 panelTitle">Export weekly settlement information for all active schools</p>
  <form *ngIf="formGroup && this.selectWeekValues?.length" [formGroup]="formGroup" class="pb-4">
    <div class="row">
      <div class="col-12 pt-4">
        <input-select-list
          formControlName="week"
          placeholder="Select week"
          [values]="selectWeekValues"
        ></input-select-list>
      </div>
      <div class="col-12">
        <div *ngIf="selectedWeek">
          <weekly-invoice-buttons
            [selectedWeek]="selectedWeek.value"
            [generatedInvoiceList]="generatedInvoiceData"
            (getWeeklyReport)="getWeeklyReport($event)"
          ></weekly-invoice-buttons>
        </div>
      </div>
    </div>
  </form>
</school-panel>
