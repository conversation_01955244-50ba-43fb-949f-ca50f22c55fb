@import '../../../../styles/cashless-theme.scss';
@import '../../../../styles/schools-theme.scss';

.errorRow {
  border-bottom: 1px solid $grey-1;
}

h3 {
  font-family: 'Bariol';
  font-style: normal;
  font-weight: 700;
  line-height: 22px;
  align-items: center;
  color: $grey-5;

  & span {
    font-size: 14px;
  }
}

h4 {
  color: $orange-6;
  margin-bottom: 2px;

  & span {
    font-size: 12px;
  }
}

.action-button {
  margin-top: 12px;
  border: 1px solid $neutral-grey-2;
  border-radius: 10px;
}

.subTitle {
  margin-top: 2px;
  margin-bottom: 2px;
  font-size: 12px;
  line-height: 14px;

  // authorize text selection
  -webkit-user-select: text; /* Chrome 49+ */
  -moz-user-select: text; /* Firefox 43+ */
  -ms-user-select: text; /* No support yet */
  user-select: text; /* Likely future */

  & .stuck {
    color: $error;
    font-weight: bold;
    font-size: 14px;
    margin-left: 5px;
  }
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
.order-wrapper ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background-color: $text-dark-strong;
}
/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
.order-wrapper ::ng-deep .mat-mdc-checkbox.mat-accent.mat-checkbox-indeterminate .mat-checkbox-background {
  background-color: $text-dark-strong;
}
