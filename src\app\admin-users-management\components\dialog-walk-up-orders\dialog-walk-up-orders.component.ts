import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { UserCashless } from 'src/app/sharedModels';

@Component({
  selector: 'dialog-walk-up-orders',
  templateUrl: './dialog-walk-up-orders.component.html',
  styleUrls: ['./dialog-walk-up-orders.component.scss'],
})
export class DialogWalkUpOrdersComponent implements OnInit {
  constructor(
    public dialogRef: MatDialogRef<DialogWalkUpOrdersComponent>,
    @Inject(MAT_DIALOG_DATA) public user: UserCashless
  ) {}

  ngOnInit() {}

  closeDialog(): void {
    this.dialogRef.close(false);
  }
}
