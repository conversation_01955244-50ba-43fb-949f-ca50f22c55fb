import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { SortedOrderHistory, UserCashless } from 'src/app/sharedModels';
import { ChildrenState, FamilyState } from 'src/app/states';
import { children } from 'src/app/states/children/children.selectors';

@Component({
  selector: 'order-history-list',
  templateUrl: './order-history-list.component.html',
  styleUrls: ['./order-history-list.component.scss'],
})
export class OrderHistoryListComponent implements OnInit, OnDestroy {
  @Input() listOrders: SortedOrderHistory[] = [];
  @Input() loading: boolean = false;
  listChildren: UserCashless[] = [];
  subscriptionChildren$: Subscription;

  constructor(private store: Store<{ family: FamilyState }>) {}

  ngOnInit() {
    this.subscriptionChildren$ = this.store.pipe(select(children)).subscribe((children: ChildrenState) => {
      this.listChildren = children.list;
    });
  }

  ngOnDestroy() {
    this.subscriptionChildren$?.unsubscribe();
  }

  getOrderChild(studentId: number): UserCashless {
    return this.listChildren?.find(f => f.UserId == studentId);
  }
}
