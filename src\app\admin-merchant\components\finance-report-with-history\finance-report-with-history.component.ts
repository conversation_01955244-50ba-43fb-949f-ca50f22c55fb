import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseFormComponent } from 'src/app/schools-form/components';
import * as _ from 'lodash';

// service
import { MerchantService, SpinnerService } from 'src/app/sharedServices';
import {
  DownloadCSV,
  GeneratedInvoice,
  InvoiceExportRequest,
  InvoiceType,
  InvoiceTypeEnum,
} from 'src/app/sharedModels';
import { formatDateToUniversal } from 'src/app/utility';

@Component({
  selector: 'finance-report-with-history.',
  templateUrl: './finance-report-with-history.component.html',
  styleUrls: ['./finance-report-with-history.component.scss'],
})
export class FinanceReportWithHistoryComponent extends BaseFormComponent implements OnInit {
  formGroupDates: FormGroup;
  invalidValueError: string = 'Invalid value entered';
  InvoiceType = InvoiceType;
  generatedInvoiceList: GeneratedInvoice[];

  constructor(
    private router: Router,
    private merchantService: MerchantService,
    private spinnerService: SpinnerService,
    private route: ActivatedRoute
  ) {
    super();
  }

  ngOnInit(): void {
    // get data from resolver
    this.route.data.subscribe(data => {
      this.generatedInvoiceList = data['generatedInvoiceList'];
    });

    this.createFrom();
  }

  createFrom(): void {
    this.formGroupDates = new FormGroup({
      startDate: new FormControl([Validators.required]),
      endDate: new FormControl([Validators.required]),
    });
  }

  goBackClick(): void {
    this.router.navigate(['./admin/merchants']);
  }

  getRevenue(): void {
    if (this.formGroupDates.invalid) {
      return;
    }
    this._downloadDatesReport();
  }

  private _downloadDatesReport() {
    let filename = InvoiceType.Revenue + '.csv';
    const request: InvoiceExportRequest = this.getDatesRequest();

    this.spinnerService.animatedStart();
    this.merchantService.GetInvoice(request).subscribe({
      next: res => {
        DownloadCSV(filename, res);
        this.spinnerService.animatedStop();
      },
      error: error => {
        this.spinnerService.animatedStop();
        this.ErrorModal('Something went wrong', error);
      },
    });
  }

  getDatesRequest(): InvoiceExportRequest {
    return {
      exportType: InvoiceTypeEnum.Revenue,
      startDate: formatDateToUniversal(this.startDate.value._d),
      endDate: formatDateToUniversal(this.endDate.value._d),
    };
  }

  get startDate() {
    return this.formGroupDates?.get('startDate');
  }

  get endDate() {
    return this.formGroupDates?.get('endDate');
  }
}
