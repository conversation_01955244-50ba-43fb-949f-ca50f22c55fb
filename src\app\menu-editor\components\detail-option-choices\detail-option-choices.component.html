<h2 *ngIf="!inDialog">Choices</h2>

<!-- choices list -->
<div [ngClass]="{ cardDefaultCanteen: !inDialog }">
  <table>
    <thead>
      <tr>
        <th>Name</th>
        <th>Price</th>
        <th>Display Order</th>
        <th>Active</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let choice of option.SubOptions" class="lineChoice">
        <td>{{ choice.OptionName }}</td>
        <td>${{ choice.OptionCost }}</td>
        <td>{{ choice.OptionOrder }}</td>
        <td>
          <mat-checkbox [checked]="choice.IsActive" [disabled]="true"></mat-checkbox>
        </td>
        <td>
          <mat-icon matTooltip="Edit" (click)="ClickAddEdit(choice)" class="actionTableau">edit</mat-icon>
        </td>
      </tr>
      <tr>
        <td colspan="7" style="text-align: right">
          <button
            *ngIf="!subOptionForm"
            class="PrimaryButton choiceButton"
            type="button"
            (click)="ClickAddEdit(null)"
          >
            Add
          </button>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<!-- choices form -->
<div *ngIf="subOptionForm" [ngClass]="{ cardDefaultCanteen: !inDialog,  formDialog: inDialog}">
  <form (ngSubmit)="SubmitChoiceForm()" #choiceForm="ngForm">
    <mat-form-field appearance="outline" class="pr-2">
      <mat-label>Name</mat-label>
      <input
        matInput
        [(ngModel)]="subOptionForm.OptionName"
        name="OptionName"
        placeholder="Name"
        type="text"
        maxlength="100"
        required
      />
    </mat-form-field>

    <mat-form-field appearance="outline" class="pr-2">
      <mat-label>Cost</mat-label>
      <span matPrefix>&nbsp; $ &nbsp;</span>
      <input
        matInput
        [(ngModel)]="subOptionForm.OptionCost"
        name="OptionCost"
        placeholder="Cost"
        type="number"
        required
      />
    </mat-form-field>

    <mat-form-field appearance="outline" class="pr-2">
      <mat-label>Display Order</mat-label>
      <input
        matInput
        [(ngModel)]="subOptionForm.OptionOrder"
        name="OptionOrder"
        placeholder="Display Order"
        type="number"
        maxlength="10"
      />
    </mat-form-field>

    <mat-checkbox [(ngModel)]="subOptionForm.IsActive" name="IsActive" class="pr-2">Is Active</mat-checkbox>

    <button
      *ngIf="subOptionForm"
      class="PrimaryButton choiceButton"
      type="submit"
      [disabled]="!choiceForm.form.valid"
    >
      {{ GetTextButtonChoice() }}
    </button>

    <button
      *ngIf="editingSubOption()"
      class="PrimaryButton choiceButton"
      type="button"
      [disabled]="!choiceForm.form.valid"
      (click)="ArchiveChoice()"
    >
      Delete
    </button>
  </form>
</div>
