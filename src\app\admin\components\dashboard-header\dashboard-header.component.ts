import { Component, OnInit, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';

@Component({
  selector: 'dashboard-header',
  templateUrl: './dashboard-header.component.html',
  styleUrls: ['./dashboard-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboardHeaderComponent implements OnInit {
  @Output() refreshData = new EventEmitter();
  constructor() {}

  ngOnInit(): void {}

  RefreshDashboard() {
    this.refreshData.emit();
  }
}
