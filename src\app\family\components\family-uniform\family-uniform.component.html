<div class="row justify-content-center backgroundEvents">
  <div class="col-12 col-sm-6">
    <ng-container *ngIf="isLoading; else noLoading">
      <div class="row justify-content-center">
        <div class="col-4 col-sm-4 col-lg-2 spinnerBlock">
          <app-spinner [manual]="true"></app-spinner>
        </div>
      </div>
    </ng-container>

    <ng-template #noLoading>
      <div *ngIf="!hasUniformMenu; else uniformMenu" class="row">
        <div class="col-12">
          <ng-container *ngIf="!displayMessage; else showDisplayMessage">
            <p class="description">There is no active uniform shop for your school</p>
            <p class="description">
              Would you like to see your school's uniform shop here?
              <span style="cursor: pointer" (click)="triggerIntercom()">Get in touch</span>, we'd love to
              help!
            </p>
          </ng-container>
          <ng-template #showDisplayMessage>
            <p class="description">{{ displayMessage }}</p>
          </ng-template>
        </div>
      </div>

      <ng-template #uniformMenu>
        <div *ngIf="hasUniformMenu" class="row">
          <div class="col-8 colEvent">
            <h4 class="titre">Uniform Shop</h4>
            <pre class="description"></pre>
          </div>

          <div class="col-4 align-self-center">
            <family-menu-block [dayData]="dayData" [child]="selectChild"></family-menu-block>
          </div>
        </div>
      </ng-template>
    </ng-template>
  </div>
</div>
