<div *ngIf="isLoading; else eventResult" class="row justify-content-center">
  <div class="col-4 col-sm-4 col-lg-2 spinnerBlock">
    <app-spinner [manual]="true"></app-spinner>
  </div>
</div>

<ng-template #eventResult>
  <ng-container *ngIf="showEventList(); else noEvents">
    <family-event *ngFor="let event of listEvents" [event]="event"></family-event>
  </ng-container>

  <ng-template #noEvents>
    <div class="row">
      <div class="col-12">
        <h4 class="titreEvent">There are no active events for your school at this time.</h4>
        <h4 class="titreEvent">
          Would you like to see your school's event here?
          <span style="cursor: pointer" (click)="triggerIntercom()">Get in touch</span>, we'd love to help!
        </h4>
      </div>
    </div>
  </ng-template>
</ng-template>
