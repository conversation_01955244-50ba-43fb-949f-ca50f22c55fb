@import '../../../styles/cashless-theme.scss';

.optionsWrapper {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
}

.optionTitle {
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  color: #000000;
  margin: 0;
}

.option {
  margin-top: 0;
  margin-bottom: 10px;
}

.optionsWrapperList {
  padding-bottom: 15px;
  margin-top: 15px;
}

.checkbox {
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 20px;
  color: #000000;
}

.buttonWrapper {
  display: block;
}

.button {
  background: $orange-3;
  font-weight: bold;
  font-size: 20px;
  line-height: 22px;
  text-align: center;
  padding: 11px 23px 14px 23px;
  color: #ffffff;
}

.button:disabled {
  color: #ffffff;
  background: $grey-9;
}
