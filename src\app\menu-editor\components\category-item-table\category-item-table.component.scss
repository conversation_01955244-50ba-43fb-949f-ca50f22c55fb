@import '../../../../styles/cashless-theme.scss';
@import 'src/styles/schools-theme';

table {
  width: 100%;
  margin-bottom: 40px;
  & tr.line {
    height: 40px;
    &:hover {
      background-color: $grey-2;
    }
    & td {
      text-align: center;
    }
  }
  & .actionTableau {
    cursor: pointer;
  }
}

// .selected-icon {
//   background-color: $link-secondary-default;
//   color: $action-primary-activated;
// }

.selected-icon {
  background-color: $mobile-light-orange;
  color: black;
}