import { Component } from '@angular/core';
import { Location } from '@angular/common';

// Models
import { BaseComponent, RefinedOrder, SortedOrderHistory } from '../../../sharedModels';
import { OrderApiService } from 'src/app/sharedServices';
import {
  GetWeekEndDate,
  GetWeekStartDate,
  SubtractDayToDate,
  addDayToDate,
  formatDateToUniversal,
} from 'src/app/utility';
import { OrderHistoryParentRequest } from 'src/app/sharedModels/order/orderRequests';
import { SortGroupOrderHistory } from '../common-order-history/group-order-history-helper';

@Component({
  selector: 'app-canteen-history',
  templateUrl: './canteen-history.component.html',
  styleUrls: ['./canteen-history.component.scss'],
})
export class CanteenHistoryComponent extends BaseComponent {
  listOrders: SortedOrderHistory[] = [];
  loading: boolean;

  constructor(private location: Location, private orderApiService: OrderApiService) {
    super();
  }

  ngOnInit() {}

  weekChanged(weekNumber: number): void {
    this.loading = true;
    const request = this.getOrderHistoryRequest(weekNumber);

    this.orderApiService.getCanteenOrderHistoryByParent(request).subscribe({
      next: (res: RefinedOrder[]) => {
        this.listOrders = SortGroupOrderHistory(res);
        this.loading = false;
      },
      error: error => {
        this.loading = false;
        this.handleErrorFromService(error);
      },
    });
  }

  goBackClick(): void {
    this.location.back();
  }

  getOrderHistoryRequest(weekNumber: number): OrderHistoryParentRequest {
    const startDate = this.getWeekDate(weekNumber, true);
    const endDate = this.getWeekDate(weekNumber, false);

    return {
      StartDate: formatDateToUniversal(startDate),
      EndDate: formatDateToUniversal(endDate),
    };
  }

  getWeekDate(weekNumber: number, getStartOfWeekDate: boolean): Date {
    const date = getStartOfWeekDate ? GetWeekStartDate(weekNumber) : GetWeekEndDate(weekNumber);
    //Subtract day to get start of week date
    //Add date to get end of week date
    return getStartOfWeekDate ? SubtractDayToDate(date, 1) : addDayToDate(date, 1);
  }
}
