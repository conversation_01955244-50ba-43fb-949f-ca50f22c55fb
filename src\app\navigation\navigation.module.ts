import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

// material
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatToolbarModule } from '@angular/material/toolbar';

// components
import { NavBarV2Component, SchoolsLogoSvgComponent, ProfileIconSvgComponent } from './components';

@NgModule({
  declarations: [NavBarV2Component, SchoolsLogoSvgComponent, ProfileIconSvgComponent],
  imports: [CommonModule, RouterModule, MatToolbarModule, MatMenuModule, MatIconModule, MatButtonModule],
  exports: [NavBarV2Component],
})
export class NavigationModule {}
