import { Component, OnInit, Input } from '@angular/core';
import * as moment from 'moment';

import {
  Menu,
  MenuTypeEnum,
  OrderBlockInfo,
  OrderStatusEnum,
  UserCashless,
  WeekDayStatusEnum,
} from '../../../sharedModels';
import { SchoolEventHomeScreenInfo } from 'src/app/sharedModels/home/<USER>';
import { ConvertStringToDate, DateHasPassed } from 'src/app/utility';

// Ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { FamilyState } from '../../../states';
import { selectedChild } from '../../../states/children/children.selectors';

@Component({
  selector: 'family-event',
  templateUrl: './event.component.html',
  styleUrls: ['./event.component.scss'],
})
export class EventComponent implements OnInit {
  private selectedChildSubscription: Subscription;
  @Input() event: SchoolEventHomeScreenInfo;
  dateEvent: string;
  dateCutOff: string;
  menuTypeEnum = MenuTypeEnum;
  dayData: OrderBlockInfo;
  selectChild: UserCashless;
  loading: boolean;
  status: string;

  constructor(private store: Store<{ family: FamilyState }>) {}

  ngOnInit() {
    const isPassed = DateHasPassed(this.event.cutOffDate);
    this.status = this.getEventStatus(this.event.orderId, this.event.orderStatusId, isPassed, false);

    this.selectedChildSubscription = this.store
      .pipe(select(selectedChild))
      .subscribe((child: UserCashless) => {
        this.selectChild = child;
      });

    this.getOrderBlockInfo();

    this.dateEvent = moment(this.event.eventDate).format('ddd DD MMMM YYYY').toString();
    this.dateCutOff = moment(this.event.cutOffDate).format('dddd DD MMMM hh:mmA').toString();
  }

  ngOnDestroy(): void {
    if (this.selectedChildSubscription) {
      this.selectedChildSubscription.unsubscribe();
    }
  }

  getOrderBlockInfo(): void {
    this.dayData = {
      menuType: MenuTypeEnum.Event,
      menuName: MenuTypeEnum.Event,
      orderDate: ConvertStringToDate(this.event.eventDate),
      cutOffDate: ConvertStringToDate(this.event.cutOffDate),
      orderStatus: this.status,
      orderId: this.event?.orderId || null,
      menuId: this.event.menuId,
    };
  }

  getEventStatus(
    orderId: number,
    orderStatusId: number,
    passed: boolean,
    schoolIsClose: boolean
  ): WeekDayStatusEnum {
    if (orderId) {
      if (orderStatusId == OrderStatusEnum.Draft) {
        return WeekDayStatusEnum.processing;
      } else if (orderStatusId == OrderStatusEnum.Error) {
        return passed ? WeekDayStatusEnum.passedError : WeekDayStatusEnum.error;
      } else {
        return passed ? WeekDayStatusEnum.passedSucceed : WeekDayStatusEnum.success;
      }
    } else if (schoolIsClose) {
      return WeekDayStatusEnum.closed;
    } else {
      return passed ? WeekDayStatusEnum.passed : WeekDayStatusEnum.order;
    }
  }
}
