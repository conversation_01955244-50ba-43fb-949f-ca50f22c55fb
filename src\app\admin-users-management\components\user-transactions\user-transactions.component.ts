import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { select, Store } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { TransactionHistory, TransactionHistorySearchRequest } from 'src/app/sharedModels';
import { SpinnerService } from 'src/app/sharedServices';
import { UserManagementState } from 'src/app/states/user-management/user-management-state.interface';
import {
  TransactionsResultsSelector,
  LoadingSelector,
} from 'src/app/states/user-management/user-management.selectors';

@Component({
  selector: 'user-transactions',
  templateUrl: './user-transactions.component.html',
  styleUrls: ['./user-transactions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserTransactionsComponent implements OnInit, OnDestroy {
  transactions: TransactionHistory[];
  searchValue: TransactionHistorySearchRequest;
  private searchResultsSubscription: Subscription;
  private loadingSubscription: Subscription;

  constructor(
    private cd: ChangeDetectorRef,
    private store: Store<{ userManagement: UserManagementState }>,
    private spinnerService: SpinnerService
  ) {}

  ngOnInit(): void {
    this.searchResultsSubscription = this.store
      .pipe(select(TransactionsResultsSelector))
      .subscribe(results => {
        this.transactions = results;
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    if (this.searchResultsSubscription) {
      this.searchResultsSubscription.unsubscribe();
    }
    if (this.loadingSubscription) {
      this.loadingSubscription.unsubscribe();
    }
  }
}
