import { Subscription } from 'rxjs';

// material
import { MatDialog } from '@angular/material/dialog';

// components
import { BaseFormComponent } from 'src/app/schools-form/components/base-form/base-form.component';
import { DialogResultComponent } from 'src/app/shared/components';

// models
import { Merchant, ResultDialogData } from 'src/app/sharedModels';

// services
import { MerchantService } from 'src/app/sharedServices';

export class BaseMerchantFormComponent extends BaseFormComponent {
  editDetailsMode: boolean = false;
  disableMode: boolean = false;
  selectedMerchant: Merchant;
  editSubscription: Subscription;
  selectedMerchantSubscription: Subscription;

  // const
  invalidValueError: string = 'Invalid value entered';

  constructor(public dialog: MatDialog, protected merchantService: MerchantService) {
    super();
  }

  /**
   * Setup the common subscriptions
   */
  _baseOnInit(): void {
    this.selectedMerchant = this.merchantService.getSelectedMerchant();

    this.selectedMerchantSubscription = this.merchantService.selectedMerchantUpdatedEvent$.subscribe(res => {
      this.selectedMerchant = res;
    });

    this.editSubscription = this.merchantService.editMode$.subscribe(status => {
      this.disableMode = status;
    });
  }

  /**
   * Unsubscribe from the common subscriptions
   */
  _baseOnDestroy(): void {
    if (this.editSubscription) {
      this.merchantService.setDisableMode(false);
      this.editSubscription.unsubscribe();
    }
    if (this.selectedMerchantSubscription) {
      this.selectedMerchantSubscription.unsubscribe();
    }
  }

  /**
   * Check form validation
   * @returns
   */
  isFormDisabled() {
    return !this.formGroup.valid;
  }

  /**
   * Common popup to display the Success message to the user
   */
  SuccessPopUp() {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = 'Changes have been saved successfully.';
    data.ConfirmButton = 'Okay';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      //close edit
      this.editDetailsMode = false;
      this.merchantService.setDisableMode(false);
    });
  }

  /**
   * Common popup to display error message to the user
   * @returns the user choice (retry or not) to manage the retry in the component extending this component
   */
  _somethingWentWrongPopup() {
    let data = new ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = 'We were unable to save your changes.';
    data.TextLine2 = 'Please try again.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';

    return this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });
  }

  /**
   * Common popup to display the Cancel message to the user
   */
  cancelEditPopup() {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Are you sure?';
    data.TextLine1 = 'Are you sure you want to cancel your changes?';
    data.TextLine2 = 'They will not be saved.';
    data.CancelButton = 'Yes, Cancel';
    data.ConfirmButton = 'No, return';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (cancelResult) {
        //close edit
        this.editDetailsMode = false;
        this.merchantService.setDisableMode(false);
      }
    });
  }

  /**
   * Popup to display reminder message to users
   */
  ReminderPopUp() {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Reminder';
    data.TextLine1 = 'If you make any changes to a merchant please alert finance immediately.';
    data.ConfirmButton = 'Okay';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });
  }
}
