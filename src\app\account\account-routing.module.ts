import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

// // Components
import {
  AccountTopupComponent,
  AccountEditComponent,
  AccountComponent,
  BillingH<PERSON>oryComponent,
  AccountHelpComponent,
  AccountCloseComponent,
} from './components';

// Services

const routes: Routes = [
  {
    path: '',
    component: AccountComponent,
  },
  {
    path: 'account-topup',
    component: AccountTopupComponent,
  },
  {
    path: 'billing-history',
    component: BillingHistoryComponent,
  },
  {
    path: 'account-help',
    component: AccountHelpComponent,
  },
  {
    path: 'account-help/account-close',
    component: AccountCloseComponent,
  },
  {
    path: 'profile',
    component: AccountEditComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountRoutingModule {}
