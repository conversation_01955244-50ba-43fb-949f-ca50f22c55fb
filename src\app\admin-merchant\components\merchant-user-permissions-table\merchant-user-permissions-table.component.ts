import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { Subscription } from 'rxjs';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';

import { MerchantService } from '../../../sharedServices';
import { Merchant, BaseComponent, CanteenUser } from 'src/app/sharedModels';

const _columns = [
  'username',
  'menuEditor',
  'salesReport',
  'viewEvent',
  'allowUnprintedOrders',
  'emailUnprintedOrders',
  'edit',
];

@Component({
  selector: 'merchant-user-permissions-table',
  templateUrl: './merchant-user-permissions-table.component.html',
  styleUrls: ['./merchant-user-permissions-table.component.scss'],
})
export class MerchantUserPermissionsTableComponent extends BaseComponent implements OnInit, OnDestroy {
  @Output() stopLoad = new EventEmitter();
  displayedColumns = _columns;
  editSubscription: Subscription;
  trackSelectedMerchant: Subscription;
  disableMode: boolean = false;
  selectedMerchant: Merchant;
  dataSource = new MatTableDataSource<CanteenUser>();

  constructor(private merchantService: MerchantService, private router: Router) {
    super();
  }

  ngOnInit() {
    this.selectedMerchant = this.merchantService.getSelectedMerchant();
    this.loadData();

    this.trackSelectedMerchant = this.merchantService.selectedMerchantUpdatedEvent$.subscribe(res => {
      this.selectedMerchant = res;
      this.loadData();
    });

    this.editSubscription = this.merchantService.editMode$.subscribe(status => {
      this.disableMode = status;
    });
  }

  ngOnDestroy(): void {
    if (this.editSubscription) {
      this.editSubscription.unsubscribe();
    }
    if (this.trackSelectedMerchant) {
      this.trackSelectedMerchant.unsubscribe();
    }
  }

  RefreshTable(users: CanteenUser[]) {
    this.dataSource.data = users;
  }

  loadData() {
    this.merchantService.GetAdminUsersLinkedToMerchant(this.selectedMerchant.canteenId).subscribe({
      next: (res: CanteenUser[]) => {
        this.RefreshTable(res);
        this.stopLoad.emit();
      },
      error: error => {
        this.stopLoad.emit();
        this.handleErrorFromService(error);
      },
    });
  }

  isListEmpty() {
    return !this.dataSource.data || this.dataSource.data.length === 0;
  }

  addUserClick() {
    this.router.navigate([`./admin/merchants/${this.selectedMerchant.canteenId}/userSearch`]);
  }
}
