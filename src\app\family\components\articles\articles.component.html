<div class="col-12">
  <ng-container *ngIf="!articles?.length; else showArticles">
    <p class="emptyBlockText p-4">There are no articles yet</p>
  </ng-container>

  <ng-template #showArticles>
    <div class="p-4 container">
      <a *ngFor="let article of articles" (click)="onArticlePress(article)">
        <div class="articleWrapper">
          <p class="articleTitle">{{ article.Title }}</p>
          <p class="articleText">{{ article.Description }}</p>
        </div>
      </a>
    </div>
  </ng-template>
</div>
