<ng-container *ngIf="!optionAdded">
  <div class="row no-gutters paddingLine">
    <div class="col-12">
      <h3 *ngIf="!optionAdded" class="titleDialog">Add Option</h3>
      <h3 *ngIf="optionAdded" class="titleDialog">Option added!</h3>
    </div>
  </div>

  <div *ngIf="!optionAdded">
    <div class="row no-gutters paddingLine">
      <div class="col-12">
        <mat-form-field appearance="outline">
          <input matInput placeholder="Name" type="text" required />
        </mat-form-field>
      </div>
    </div>

    <div class="row no-gutters paddingLine">
      <div class="col-12">
        <button type="button" class="SecondaryButton" (click)="Close()">Cancel</button>
      </div>
    </div>
    <div class="row no-gutters paddingLine">
      <div class="col-12">
        <button type="button" class="PrimaryButton" (click)="SubmitOption()">Add</button>
      </div>
    </div>
  </div>

  <div *ngIf="optionAdded">
    <div class="row no-gutters paddingLine">
      <div class="col-12">
        <button type="button" class="PrimaryButton" (click)="Close()">Close</button>
      </div>
    </div>
  </div>
</ng-container>

<div *ngIf="isProcessing" class="spinnerDialog">
  <app-spinner [manual]="true"></app-spinner>
</div>
