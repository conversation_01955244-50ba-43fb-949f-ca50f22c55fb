@import '../../../../styles/cashless-theme.scss';
@import 'src/styles/schools-theme';

.button-container {
    text-align: right;
    margin-top: 20px;
}

.container-items{
    min-height: 320px; 
    max-height: 320px; 
    overflow: auto; 
    padding: 20px
}

mat-form-field {
    width: 100%;
}

.actionTableau {
    text-align: center;
    
    & mat-icon:hover{
        cursor: pointer;
    }
  }


.align-item-content{
    align-content: center;
}

.selected-icon {
    background-color: $mobile-light-orange;
    color: black;
  }