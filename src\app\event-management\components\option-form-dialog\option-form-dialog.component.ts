import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { OptionFormV2Component } from 'src/app/menu-editor/components/option-form-v2/option-form-v2.component';
import { SharedToolsModule } from 'src/app/shared-tools/shared-tools.module';
import { SharedModule } from 'src/app/shared/shared.module';

// Models
import { Option } from 'src/app/sharedModels';
import { MenuEditorApiService } from 'src/app/sharedServices';

@Component({
  selector: 'option-form-dialog',
  templateUrl: './option-form-dialog.component.html',
  styleUrls: ['./option-form-dialog.component.scss'],
  standalone: true, 
  imports: [MatDialogModule, CommonModule, SharedToolsModule, SharedModule, OptionFormV2Component],
})
export class OptionFormDialogComponent {
  option: Option = new Option();
  loading: boolean = true;

  constructor(
    public dialogRef: MatDialogRef<OptionFormDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private menuEditorApiService: MenuEditorApiService
  ){
    if(this.data.optionId > 0){
      this.loadOption();
    }else{
      this.option = new Option();
      this.loading = false;
    }
  }

  private loadOption(){
    this.menuEditorApiService.GetOptionByIdAPI(this.data.optionId).subscribe({
      next: res => {
        this.option = res;
        this.loading = false;
      },
      error: error => {
        this.closeModal();
      },
    });
  }
  
  closeModal(): void {
    this.dialogRef.close(false);
  }
   
}
