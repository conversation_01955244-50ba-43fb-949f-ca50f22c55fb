<div class="col-12">
  <div class="col-md-8 col-sm-12">
    <nav-back-button (navBack)="GoBackClick()" text="Go Back" class="backButton"></nav-back-button>

    <div class="titleWrapper">
      <img sizes="24" src="assets/icons/bread.svg" />
      <h1 class="title">Canteen Settings</h1>
    </div>

    <merchant-school-picker (merchantChanged)="onMerchantSelect($event)" (schoolChanged)="onSchoolSelect($event)"></merchant-school-picker>

    <div *ngIf="selectedSchool.SchoolId">

      <ng-container *ngIf="!isEventMerchant">
        <div class="optionsWrapper">
          <opening-days-form
            [school]="selectedSchool"
            (updateSchool)="updateSchool($event)"
          ></opening-days-form>
        </div>

        <div class="optionsWrapper">
          <dietary-labels-form
            [school]="selectedSchool"
            (updateSchool)="updateSchool($event)"
          ></dietary-labels-form>
        </div>
      </ng-container>

      <div class="optionsWrapper">
        <menu-categories-form></menu-categories-form>
      </div>

      <ng-container *ngIf="!isEventMerchant">

        <ng-container>
          <div class="optionsWrapper">
            <order-advance-form
              [weekValue]="selectedSchool.WeeksPreOrder"
              (updateValue)="updateWeeksPreOrder($event)"
            ></order-advance-form>
          </div>
    
          <div class="optionsWrapper">
            <allergy-alert-form
              [schoolFeatures]="selectedSchool.SchoolFeatures"
              [schoolId]="selectedSchool.SchoolId"
            ></allergy-alert-form>
          </div>
    
          <div class="optionsWrapper">
            <school-closing-date-form [schoolId]="selectedSchool.SchoolId"></school-closing-date-form>
          </div>
    
          <div class="optionsWrapper">
            <food-break-settings-form [merchantId]="selectedMerchant.CanteenId" [schoolId]="selectedSchool.SchoolId"></food-break-settings-form>
          </div>
        </ng-container>
  
        <div class="schoolSelection printerSection">
          <printer-options-form [printSettings]="printSettings"></printer-options-form>
        </div>
      </ng-container>

    </div>
  </div>
</div>
