<div class="row">
  <div class="col-lg-6 col-sm-12">
    <h2>Item Options</h2>
    <div class="cardDefaultCanteen">
      <table class="eTable">
        <thead>
          <tr>
            <th>Option</th>
            <th>Choices</th>
            <th>Is Active</th>
            <th></th>
          </tr>
        </thead>
        <tbody *ngIf="item">
          <tr *ngFor="let option of itemOptions" class="line">
            <td>{{ option.Name }}</td>
            <td>
              <option-chip-list [optionList]="option.SubOptions" />
            </td>
            <td>
              <mat-checkbox [checked]="option.IsActive" [disabled]="true"></mat-checkbox>
            </td>
            <td>
              <mat-icon matTooltip="Remove" class="actionTableau" (click)="RemoveOption(option)"
                >delete</mat-icon
              >
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="col-lg-6 col-sm-12">
    <h2>Add Options</h2>
    <div class="cardDefaultCanteen">
      <table class="eTable">
        <thead>
          <tr>
            <th>Option</th>
            <th>Choice</th>
            <th>Is Active</th>
            <th></th>
          </tr>
        </thead>
        <tbody *ngIf="item">
          <tr *ngFor="let option of listOptionsToShow" class="line">
            <td>{{ option.Name }}</td>
            <td>
              <option-chip-list [optionList]="option.SubOptions" />
            </td>
            <td>
              <mat-checkbox [checked]="option.IsActive" [disabled]="true"></mat-checkbox>
            </td>
            <td>
              <mat-icon matTooltip="Add" class="actionTableau" (click)="AddOption(option)">add</mat-icon>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
