<mat-dialog-content>
  <modal-header title="Add Existing items" (close)="closeModal()"></modal-header>

  <div class="row">
    <div class="col-4">
      <category-select-list [listCategories]="listCategories" (categorySelected)="categoryChanged($event)"></category-select-list>
    </div>
    <div class="col-4">
      <mat-form-field appearance="outline">
        <mat-label>Search</mat-label>
        <input
          matInput
          placeholder="Search"
          type="text"
          [(ngModel)]="search"
          (ngModelChange)="getItemsToDisplay()"
        />
      </mat-form-field>
    </div>
  </div>

<div class="row">
  <div class="col-2">

  </div>
  <div class="col-4">
    Item name
  </div>
  <div class="col-2">
    Price
  </div>
  <div class="col-2">
    Is Active
  </div>
  <div class="col-2">

  </div>
</div>

<div class="container-items">
  <div class="row" *ngFor="let item of menuItemsToDisplay">
    <div class="col-2">
       <item-image [url]="item.imageUrl" [isSmall]="true"></item-image>
    </div>
    <div class="col-4 align-item-content">
      {{ item.name }}
    </div>
    <div class="col-2 align-item-content">
      {{ item.price | currency }}
    </div>
    <div class="col-2 align-item-content">
      <mat-checkbox [checked]="item.isActive" [disabled]="true"></mat-checkbox>
    </div>
    <div class="col-2 align-item-content actionTableau">
      <mat-icon *ngIf="ItemIsSelected(item.menuItemId); else addIcon" matTooltip="Add" class="selected-icon" (click)="itemPress(item.menuItemId)">check</mat-icon>
                
      <ng-template #addIcon>
        <mat-icon matTooltip="Add" (click)="itemPress(item.menuItemId)">add</mat-icon>
      </ng-template>
    </div>
  </div>
</div>

  <div class="button-container">
    <basic-button-v2
      (onPress)="closeModal()"
      text="Done"
      buttonStyle="primaryOrange"
    ></basic-button-v2>
  </div>
</mat-dialog-content> 
