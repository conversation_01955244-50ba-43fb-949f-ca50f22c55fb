import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ItemOption, ItemOptionCategory, SubOption } from 'src/app/sharedModels';
import { OptionChipListComponent } from '../option-chip-list/option-chip-list.component';
import { MatDialog } from '@angular/material/dialog';
import { OptionFormDialogComponent } from 'src/app/event-management/components/option-form-dialog/option-form-dialog.component';
import { OptionChoicesDialogComponent } from 'src/app/event-management/components/option-choices-dialog/option-choices-dialog.component';

@Component({
  selector: 'grouped-option-chip-list',
  standalone: true,
  imports: [CommonModule, OptionChipListComponent],
  templateUrl: './grouped-option-chip-list.component.html',
  styleUrls: ['./grouped-option-chip-list.component.scss'],
})
export class GroupedOptionChipListComponent {
  @Input() optionCategories: ItemOptionCategory[];
  @Output() optionsChanged = new EventEmitter();

  constructor(public dialog: MatDialog)
  {}

  GetChoicesToDisplay(choices: ItemOption[]): SubOption[] {
    let res : SubOption[] = [];

    if(choices && choices.length > 0){
      choices.forEach( c => {
        res.push(this.convertEventItemOptionToSubOption(c))
      })
    }

    return res;
  }


  UpsertOptionDialog(optionId: number): void {

    const dialogRef = this.dialog.open(OptionFormDialogComponent, {
      width: '600px',
      disableClose: true,
      data: {
        optionId: optionId
      },
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result){
        this.optionsChanged.emit();
      }
    });
  }

  UpsertChoicesDialog(optionId: number): void {

    const dialogRef = this.dialog.open(OptionChoicesDialogComponent, {
      width: '1000px',
      disableClose: true,
      data: {
        optionId: optionId
      },
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result){
        this.optionsChanged.emit();
      }
    });
  }

  /**
   * Need to use the component "option-chip-list" that required "SubOption"
   * @param option 
   * @returns 
   */
  private convertEventItemOptionToSubOption(option: ItemOption): SubOption {
    return {
      MenuItemOptionId: null,
      CanteenId: null,
      MenuItemOptionsCategoryId: null,
      IsActive: null,
      OptionType: null,
      OptionName: option.name,
      ShortDesc: null,
      OptionDescription: null,
      Cost: null,
      OptionCost: option.cost,
      PrintOnlabel: null,
      OptionOrder: null,
      IsArchived: null,
    };
  }
}
