@import '../../../../styles/cashless-theme.scss';

.divOrderDetails {
  min-height: 300px;
  height: fit-content;
  width: 100%;
  background-color: #ffffff;
  padding: 10px;

  & h3 {
    text-align: center;
    color: $orange-2;
    margin-top: 5px;
    font-size: 22px;
  }

  & .dataline {
    margin-bottom: 5px;
    font-size: 10px;

    & strong {
      font-size: 16px;
      font-weight: bold;
    }
  }

  & ul {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 15px;
  }
}

.header {
  display: flex;
  width: 100%;
  justify-content: space-between;

  .printButton {
    outline: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 700;
    border-radius: 8px;
    box-shadow: 0px 1px $grey-9;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $grey-14;

    mat-icon {
      color: #ffffff;
    }
  }
}
