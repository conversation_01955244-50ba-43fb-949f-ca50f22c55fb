<div class="container-fluid">
  <div class="row">
    <div class="col-12 col-sm-12 col-lg-6">
      <div class="cardDefaultCanteen">
        <form class="cashlessForm input-container" [formGroup]="form" (ngSubmit)="onSubmit()">
          <input-text
            placeholder="School name"
            formControlName="name"
            [error]="name.invalid ? getErrorMessageName() : null"
          ></input-text>
          <input-text
            placeholder="Number of Students"
            formControlName="totalStudentEst"
            [error]="totalStudentEst.invalid ? getErrorMessageName() : null"
            step="1"
            min="0"
          ></input-text>
          <div>
            <mat-form-field appearance="outline">
              <mat-label>State</mat-label>
              <mat-select placeholder="State" formControlName="state">
                <mat-option *ngFor="let state of listStates" [value]="state.StateId">{{
                  state.ShortName
                }}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div *ngIf="!school.SchoolId || school.SchoolId == 0">
            <mat-form-field appearance="outline">
              <mat-label>Merchant</mat-label>
              <mat-select placeholder="Operator" formControlName="canteen">
                <mat-option *ngFor="let canteen of canteens" [value]="canteen.CanteenId">{{
                  canteen.CanteenName
                }}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div>
            <mat-form-field appearance="outline">
              <mat-label>Deactivated Filters</mat-label>
              <input
                matInput
                placeholder="Deactivated filters"
                type="text"
                formControlName="deactivatedFilters"
              />
            </mat-form-field>
          </div>
          <div class="pb-3">
            <mat-checkbox formControlName="active">Is Active</mat-checkbox>
          </div>

          <div class="pb-3">
            <mat-checkbox formControlName="marketingFree"
              >Hide Marketing - Requires General Manager approval to select
            </mat-checkbox>
          </div>

          <div>
            <button class="PrimaryButton submitButton" type="submit" [disabled]="!form.valid">
              {{ GetTextSubmitButton() }}
            </button>

            <div *ngIf="errorAPI">
              <mat-error>{{ WriteError() }}</mat-error>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
