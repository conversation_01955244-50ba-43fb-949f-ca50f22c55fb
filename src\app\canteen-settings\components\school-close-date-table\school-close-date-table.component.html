<table mat-table [dataSource]="dates" class="col-12">
  <ng-container matColumnDef="StartDate">
    <th mat-header-cell *matHeaderCellDef>Start Date</th>
    <td mat-cell *matCellDef="let element">{{ element.StartDate | date : 'mediumDate' }}</td>
  </ng-container>

  <ng-container matColumnDef="EndDate">
    <th mat-header-cell *matHeaderCellDef>End Date</th>
    <td mat-cell *matCellDef="let element">{{ element.EndDate | date : 'mediumDate' }}</td>
  </ng-container>

  <ng-container matColumnDef="Actions">
    <th mat-header-cell *matHeaderCellDef>Action</th>
    <td mat-cell *matCellDef="let element">
      <mat-icon style="cursor: pointer" matTooltip="Archive" (click)="archiveClicked(element.SchoolDateId)"
        >delete_outline</mat-icon
      >
    </td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>
