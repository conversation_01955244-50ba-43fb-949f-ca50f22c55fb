<div class="container-fluid">
  <nav-back-button (navBack)="GoBackClick()" text="Go back"></nav-back-button>

  <div class="row">
    <div class="col-lg-8 col-sm-12">
      <!-- item -->
      <h2>Item</h2>
      <div class="cardDefaultCanteen">
        <form *ngIf="form" [formGroup]="form" (ngSubmit)="onSubmit()" class="menuItemForm">
          <div class="formInputs">
            <mat-form-field appearance="outline">
              <mat-label>Name</mat-label>
              <input matInput placeholder="Name" formControlName="name" type="text" required />
              <mat-error *ngIf="name.invalid">{{ getErrorMessage() }}</mat-error>
            </mat-form-field>

            <input-select-list
              formControlName="categoryData"
              placeholder="Category"
              [values]="merchantCategories"
              [error]="categoryData.invalid && getErrorMessage()"
            ></input-select-list>

            <mat-form-field appearance="outline">
              <mat-label>Description</mat-label>
              <textarea
                matInput
                #description
                placeholder="Description (optional)"
                formControlName="description"
                maxlength="200"
              ></textarea>
              <mat-hint align="end">{{ description.value.length }} / 200</mat-hint>
            </mat-form-field>

            <mat-form-field appearance="outline" class="mb-3">
              <mat-label>Display Order (optional)</mat-label>
              <input
                matInput
                placeholder="Display Order (optional)"
                formControlName="sortOrder"
                type="number"
                min="1"
              />
              <mat-hint
                >Items with the display order set will be shown above items where the display order is
                empty.</mat-hint
              >
            </mat-form-field>
            <div class="mt-4">
              <mat-form-field appearance="outline">
                <mat-label>Price</mat-label>
                <span matPrefix>&nbsp; $ &nbsp;</span>
                <!-- type 'text' needed to use twoDecimal directive -->
                <input matInput placeholder="Price" type="text" formControlName="price" required twoDecimal />
                <mat-error *ngIf="price.invalid">{{ getErrorMessage() }}</mat-error>
              </mat-form-field>
            </div>
          </div>

          <input-checkbox formControlName="available" placeholder="This item is available"></input-checkbox>
          <input-checkbox formControlName="hasGst" placeholder="GST applies to this item"></input-checkbox>

          <div *ngIf="!IsUniformCanteen()" class="row">
            <item-cut-off-time-picker
              [form]="form"
              [cutOffTimeVal]="item.CutOffTime"
              [cutOffTimeTypeVal]="item.CutOffTimeType"
            ></item-cut-off-time-picker>
          </div>

          <div *ngIf="!IsUniformCanteen()" class="row">
            <div class="col-4">
              <editor-checkbox-list
                title="Dietary Labels"
                [values]="dietaryCheckBoxValues"
                [formGroup]="form"
              ></editor-checkbox-list>
            </div>

            <div class="col-4">
              <editor-checkbox-list
                title="Availability"
                [values]="weekDayCheckBoxValues"
                [formGroup]="form"
              ></editor-checkbox-list>

              <editor-checkbox-list
                title="Weekend Availability"
                [values]="weekEndCheckBoxValues"
                [formGroup]="form"
              ></editor-checkbox-list>
            </div>

            <!-- Nutritional value -->
            <div *ngIf="nutritionalValues" class="col-4">
              <div class="subTitleWrapper">
                <h3 class="subTitle">Nutritional value</h3>
              </div>
              <div *ngFor="let item of nutritionalValues; let i = index">
                <mat-checkbox
                  [checked]="selectedNutritional.Name == item.Name"
                  (change)="onNutritionalSelect(item)"
                  >{{ item.Name }}</mat-checkbox
                >
              </div>
            </div>
          </div>

          <div>
            <div>
              <h3 class="subTitle">Item Availability Dates</h3>
            </div>
            <div class="row">
              <div class="col-9 formInputs">
                <input-date
                  placeholder="Select start date (inclusive)"
                  formControlName="itemStartDate"
                ></input-date>

                <input-date
                  placeholder="Select end date (inclusive)"
                  formControlName="itemEndDate"
                ></input-date>
              </div>
            </div>

            <div>
              <table *ngIf="menuItemAvailability" mat-table [dataSource]="menuItemAvailability">
                <ng-container matColumnDef="StartDate">
                  <th mat-header-cell *matHeaderCellDef>Start Date</th>
                  <td mat-cell *matCellDef="let element">{{ element.StartDate.split('T')[0] }}</td>
                </ng-container>

                <ng-container matColumnDef="EndDate">
                  <th mat-header-cell *matHeaderCellDef>End Date</th>
                  <td mat-cell *matCellDef="let element">{{ element.EndDate.split('T')[0] }}</td>
                </ng-container>

                <ng-container matColumnDef="Actions">
                  <th mat-header-cell *matHeaderCellDef>Action</th>
                  <td mat-cell *matCellDef="let element">
                    <mat-icon matTooltip="Archive" (click)="ArchiveClicked(element)">delete_outline</mat-icon>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
              </table>
              <div *ngIf="!menuItemAvailability">
                <p>No Item Available Dates Specified</p>
              </div>
            </div>
          </div>

          <div class="divButton">
            <button class="PrimaryButton submitButton" type="submit">
              {{ GetTextSubmitButton() }}
            </button>
            <button
              *ngIf="CanDeleteItem()"
              class="PrimaryButton deleteButton"
              (click)="DeleteMenuItem($event)"
            >
              Delete Item
            </button>

            <div *ngIf="errorAPI">
              <mat-error>{{ WriteError() }}</mat-error>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- menus -->
    <div class="col-lg-4 col-sm-12">
      <item-menu-editor-list
        [merchantSchools]="merchantSchools"
        [itemMenus]="item.Menus"
        [menuItemId]="item.MenuItemId"
        [merchantId]="merchantId"
      >
      </item-menu-editor-list>
    </div>
  </div>
  <!-- images -->
  <div class="row">
    <div class="col-lg-6 col-sm-12">
      <item-image-upload-form
        [menuItemId]="item.MenuItemId"
        [schoolCode]="item.SchoolCode"
        [itemImages]="item.Images"
        (updateImages)="updateMenuItemImages($event)"
      ></item-image-upload-form>
    </div>
  </div>

  <!-- item options -->
  <ng-container *ngIf="item.MenuItemId > 0">
    <editor-item-option [item]="item"></editor-item-option>
  </ng-container>

  <!-- stock -->
  <div class="row" *ngIf="item.MenuItemId > 0">
    <div class="col-lg-6 col-sm-12 bottomSpacing">
      <item-stock-table
        [menuItemId]="item.MenuItemId"
        [stockList]="item.Stocks"
        [menuId]="item.MenuId"
        [merchantId]="item.CanteenId"
      ></item-stock-table>
    </div>
  </div>
</div>
