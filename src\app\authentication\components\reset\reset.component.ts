import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { CashlessAppInsightsService, UserService } from 'src/app/sharedServices';

@Component({
  selector: 'authentication-reset',
  templateUrl: './reset.component.html',
  styleUrls: ['./reset.component.scss'],
})
export class ResetComponent implements OnInit {
  errorReset: string;
  submitted: boolean = false;

  form = new FormGroup({
    email: new FormControl('', [Validators.required, Validators.email]),
  });

  constructor(
    public afAuth: AngularFireAuth,
    private router: Router,
    private appInsightsService: CashlessAppInsightsService,
    private userService: UserService
  ) {}

  ngOnInit() {}

  ////////////////////////////////////////
  // Form
  ////////////////////////////////////////
  get email() {
    return this.form.get('email');
  }

  getErrorMessageEmail() {
    return this.form.get('email').hasError('required')
      ? 'You must enter a value'
      : this.form.get('email').hasError('email')
      ? 'Not a valid email'
      : '';
  }

  ////////////////////////////////////////
  // Functions
  ////////////////////////////////////////

  async ResetPassword() {
    this.errorReset = '';
    this.submitted = true;
    this.userService.UserResetPasswordEmail(this.email.value).subscribe({
      next: res => {
        this.appInsightsService.TrackEvent('ResetPasswordEmailSent', { Email: this.email.value });
      },
      error: err => {
        this.submitted = false;
        this.errorReset = 'Oops, something went wrong. Please try again or contact us for assistance.';
      },
    });
  }
}
