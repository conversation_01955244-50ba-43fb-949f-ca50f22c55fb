<div class="container-fluid">
  <div class="wrapperContainer">
    <div class="row">
      <div class="col-12">
        <nav-back-button (navBack)="backClicked()" text="Account"></nav-back-button>
      </div>
    </div>

    <!-- Top Up history -->
    <div class="row">
      <div class="col-12">
        <p class="accountTitle">Top Up History</p>
      </div>
    </div>
  </div>

  <div class="row rowHistory" *ngFor="let topup of listTopUp">
    <div class="col-12">
      <p>
        <strong>{{ topup.Date | date : 'EEEE d MMMM y' }}</strong>
        <br />
        Top Up <strong class="spacerDescription">.</strong> ${{ topup.TopupAmount | number : '1.2-2' }}
      </p>
    </div>
  </div>

  <div *ngIf="!hasTopUp" class="row rowHistory">
    <div class="col-12">
      <p>
        <strong>No Top Up History</strong>
      </p>
    </div>
  </div>

  <!-- Credit history -->
  <div class="wrapperContainer">
    <div class="row">
      <div class="col-12">
        <p class="accountTitle">Credit History</p>
      </div>
    </div>
  </div>

  <div class="row rowHistory" *ngFor="let topup of listCredit">
    <div class="col-12">
      <p>
        <strong>{{ topup.Date | date : 'EEEE d MMMM y' }}</strong>
        <br />
        Account Credit <strong class="spacerDescription">.</strong> ${{
          topup.TopupAmount | number : '1.2-2'
        }}
      </p>
    </div>
  </div>

  <div *ngIf="!hasCredit" class="row rowHistory">
    <div class="col-12">
      <p>
        <strong>No Credit History</strong>
      </p>
    </div>
  </div>

  <!-- Billing history -->
  <div *ngIf="hasBilling" class="wrapperContainer">
    <div class="row">
      <div class="col-12">
        <p class="accountTitle">Billing History</p>
      </div>
    </div>
  </div>

  <div class="row rowHistory" *ngFor="let billing of listBilling">
    <div class="col-12">
      <p>
        <strong>{{ billing.OrderDate | date : 'EEEE d MMMM y' }}</strong>
        <br />
        Account Fee for
        {{ billing.StudentName }} <strong class="spacerDescription">.</strong> {{ billing.TermName }} ({{
          billing.StartDate | date : 'dd MMM'
        }}
        / {{ billing.EndDate | date : 'dd MMM' }}) <strong class="spacerDescription">.</strong> ${{
          billing.OrderAmount | number : '1.2-2'
        }}
      </p>
    </div>
  </div>
</div>
