<table *ngIf="tableData" mat-table [dataSource]="tableData" class="mat-elevation-z8 tableau table">
  <ng-container matColumnDef="title">
    <th mat-header-cell *matHeaderCellDef class="title">Title</th>
    <td mat-cell *matCellDef="let element" class="tableElement">{{ element.Title }}</td>
  </ng-container>

  <ng-container matColumnDef="body">
    <th mat-header-cell *matHeaderCellDef class="titleCenter">Body/Description</th>
    <td
      mat-cell
      *matCellDef="let element"
      class="tableElement"
      [ngClass]="{ noDescription: !element.Description.length }"
    >
      {{ element.Description || 'No description' }}
    </td>
  </ng-container>

  <ng-container matColumnDef="IsActive">
    <th mat-header-cell *matHeaderCellDef class="title">Active</th>
    <td mat-cell *matCellDef="let element">
      <div *ngIf="!element.IsActive" class="inActiveCheckbox"></div>
      <img *ngIf="element.IsActive" sizes="24" src="assets/icons/checkBox.svg" class="checkBox" />
    </td>
  </ng-container>

  <ng-container matColumnDef="Status">
    <th mat-header-cell *matHeaderCellDef class="title">Status</th>
    <td mat-cell *matCellDef="let element" style="text-align: center">
      <span *ngIf="element.Status == NoticeStatusEnum.WaitingValidation">Waiting for validation</span>
      <span *ngIf="element.Status == NoticeStatusEnum.Refused" style="color: red">Not valid</span>
      <span *ngIf="element.Status == NoticeStatusEnum.Validated" style="color: green">Approved</span>
    </td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr
    mat-row
    *matRowDef="let row; columns: displayedColumns"
    (click)="noticeRowClick(row)"
    class="rowElement"
  ></tr>
</table>
