

<form #optionForm="ngForm">
    <div class="row">
        <div class="col-12">
            <mat-form-field appearance="outline">
                <mat-label>Option name</mat-label>
                <input
                  matInput
                  [(ngModel)]="option.Name"
                  name="Name"
                  placeholder="Name"
                  type="text"
                  maxlength="200"
                  required
                />
              </mat-form-field>
              <div>
                <mat-checkbox [(ngModel)]="option.IsActive" name="IsActive">Is Active</mat-checkbox>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <h3 class="title">Selection rules</h3>
            <div *ngFor="let rule of selectionRules">
              <div class="checkBoxWrapper">
                <mat-checkbox [checked]="selectedRuleIndex === rule.id" (change)="onRuleChange(rule.id)">
                  <div class="textWrapper">
                    <p class="ruleTitle">{{ rule.title }}</p>
                    <p class="ruleDescription">{{ rule.description }}</p>
                  </div>
                </mat-checkbox>
              </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            
        </div>
    </div>

    <div class="div-button">
        <basic-button-v2
          class="pr-2"
          [text]="GetTextButton()"
          (onPress)="SubmitForm()"
          buttonStyle="primaryOrange"
        ></basic-button-v2>
    </div>
  </form>


