import { ChangeDetectionStrategy, Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { SchoolsButtonModule } from 'src/app/schools-button/schools-button.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatNativeDateModule } from '@angular/material/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { EventFormComponent } from '../event-form/event-form.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SchoolEventManagerService } from 'src/app/sharedServices';
import { BaseComponent, Canteen, EventItem, EventTemplate, EventType, School, SchoolClass, SchoolEvent } from 'src/app/sharedModels';
import { Subscription } from 'rxjs';
import { CanteenState } from 'src/app/states';
import { Store, select } from '@ngrx/store';
import { selectedCanteen, selectedSchool } from 'src/app/states/canteen/canteen.selectors';
import { ActivatedRoute, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import {MatStepper, MatStepperModule} from '@angular/material/stepper';
import { EventImageComponent } from "../event-image/event-image.component";
import { EventItemsComponent } from "../event-items/event-items.component";
import { GreenCheckComponent } from "../../../schools-common/components/green-check/green-check.component";
import { SelectEventTemplateComponent } from "../select-event-template/select-event-template.component";
import { ItemImageComponent } from "../../../menu-editor/components/item-image/item-image.component";

@Component({
  selector: 'app-create-event-flow-page',
  standalone: true,
  imports: [
    CommonModule,
    SchoolsButtonModule,
    MatFormFieldModule,
    MatNativeDateModule,
    ReactiveFormsModule,
    FormsModule,
    MatInputModule,
    MatDatepickerModule,
    MatSelectModule,
    EventFormComponent,
    SharedModule,
    MatButtonModule,
    MatStepperModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    EventImageComponent,
    EventItemsComponent,
    GreenCheckComponent,
    SelectEventTemplateComponent,
    ItemImageComponent
],
  templateUrl: './create-event-flow-page.component.html',
  styleUrls: ['./create-event-flow-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateEventFlowPageComponent extends BaseComponent implements OnInit, OnDestroy {
  schoolSubscription: Subscription;
  merchantSubscription: Subscription;
  school: School;
  merchant: Canteen;
  loading: boolean;
  schoolClasses: SchoolClass[];
  eventTypes: EventType[];
  schoolEvent: SchoolEvent = new SchoolEvent();
  items: EventItem[] = [];
  selectedTemplate: EventTemplate;


  private _formBuilder = inject(FormBuilder);

  formGroup = this._formBuilder.group({
    eventCreated: ['', Validators.required],
  });


  constructor(
    private location: Location,
    private schoolEventManagerService: SchoolEventManagerService,
    private store: Store<{ canteen: CanteenState }>,
    private route: ActivatedRoute,
    private router: Router,
  ) {
    super();
  }

  ngOnInit(): void {
    //get data from resolver
    this.route.data.subscribe(data => {
      this.schoolClasses = data['classes'].Classes;
    });

    this.route.data.subscribe(data => {
      this.eventTypes = data['templates'];
    });
    
    this.merchantSubscription = this.store.pipe(select(selectedCanteen)).subscribe((state: Canteen) => {
      this.merchant = state;
    });

    this.schoolSubscription = this.store.pipe(select(selectedSchool)).subscribe((state: School) => {
      this.school = state;
    });
  }

  ngOnDestroy(): void {
    this.schoolSubscription?.unsubscribe();
    this.merchantSubscription?.unsubscribe();
  }

  goBack(): void {
    this.location.back();
  }


  UpsertEvent(schoolEvent: SchoolEvent, stepper: MatStepper): void {

    if(schoolEvent.SchoolId > 0){
      this.editEvent(schoolEvent);
    }else{
      this.createNewEvent(schoolEvent, stepper);
    }
  }

  private createNewEvent(schoolEvent: SchoolEvent, stepper: MatStepper): void {
    this.loading = true;
    schoolEvent.MerchantId = this.merchant.CanteenId;
    schoolEvent.SchoolId = this.school.SchoolId;

    if(this.selectedTemplate){
      schoolEvent.EventTemplateId = this.selectedTemplate.eventTemplateId;
    }

    this.schoolEventManagerService.PostNewSchoolEvent(schoolEvent).subscribe({
      next: (res: SchoolEvent) => {
        this.schoolEvent = res;
        this.loading = false;
        var control = this.formGroup.get('eventCreated');
        control.setValue('true');

        stepper.next();
      },
      error: error => {
        this.loading = false;
        this.handleErrorFromService(error);
      },
    });
  }

  private editEvent(updatedSchoolEvent: SchoolEvent): void {
    this.loading = true;

    if(this.selectedTemplate){
      updatedSchoolEvent.EventTemplateId = this.selectedTemplate.eventTemplateId;
    }

    this.schoolEventManagerService.UpdateSchoolEvent(updatedSchoolEvent).subscribe({
      next: (res: SchoolEvent) => {
        this.schoolEvent = res;
        this.loading = false;
      },
      error: error => {
        this.loading = false;
        this.handleErrorFromService(error);
      },
    });
  }

  navToSelectedEvent(): void {
    this.router.navigate([`../view/${this.schoolEvent.SchoolEventId}`], { relativeTo: this.route });
  }

  templateChanged(template: EventTemplate){
    this.selectedTemplate = template;
  }
}
