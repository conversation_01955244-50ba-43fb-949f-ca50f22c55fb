@import '../../../../styles/cashless-theme.scss';

h2 {
  color: $orange-3;
}

mat-form-field {
  width: 300px;
}

.divButton {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.deleteButton {
  font-size: 16px;
  width: 100px;
  height: 35px;
  padding: 0px;
}

/* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
mat-radio-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
