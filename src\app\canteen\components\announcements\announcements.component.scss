.backButton {
  color: orange;
  font-size: 14px;
}

.cardWrapper {
  background-color: white;
  border-radius: 12px;
  padding-top: 20px;
  padding-bottom: 23px;
  padding-right: 15px;
  padding-left: 20px;
}

.crossIconWrapper {
  display: flex;
  justify-content: flex-end;
  padding-right: 2px;
  cursor: pointer;
}

.invisible {
  display: none;
}

.inputTitle:last-of-type {
  margin-bottom: 0;
}

.image {
  width: 28px;
}

.separator {
  width: 100%;
  background-color: #dadada;
  height: 1px;
  margin-top: 10px;
}

.infoBlock {
  padding-bottom: 30px;
}

.tableElement {
  padding-top: 10px;
  padding-bottom: 10px;
  padding-right: 10px;
  white-space: pre-line;
  vertical-align: top;
}

.closeBtn {
  padding: 5px;
}

.validationExplanation {
  & h4 {
    margin-bottom: 0;
    font-weight: bold;
  }

  & p {
    margin-bottom: 50px;
  }
}
