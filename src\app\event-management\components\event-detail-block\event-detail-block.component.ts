import { ChangeDetectionStrategy, Component, Input, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SchoolEvent } from 'src/app/sharedModels';
import { EventTimeFormatPipe } from 'src/app/sharedPipes';
import { SchoolsButtonModule } from 'src/app/schools-button/schools-button.module';
import { DateTimeService } from 'src/app/sharedServices';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { SpsLinkComponent } from "../../../schools-button/components/sps-link/sps-link.component";

@Component({
  selector: 'event-detail-block',
  standalone: true,
  imports: [CommonModule, EventTimeFormatPipe, SchoolsButtonModule, SpsLinkComponent, MatIconModule],
  templateUrl: './event-detail-block.component.html',
  styleUrls: ['./event-detail-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EventDetailBlockComponent {
  @Input() schoolEvent: SchoolEvent;
  @Output() deleteEvent = new EventEmitter();
  @Output() editEvent = new EventEmitter();

  constructor(private dateService: DateTimeService) {}

  deleteEventPress(): void {
    if(!this.hideDeleteButton()){
      this.deleteEvent.emit();
    }
  }

  editEventPress(): void {
    this.editEvent.emit();
  }

  hideDeleteButton(): boolean {
    return this.schoolEvent.IsActive;
  }
}
