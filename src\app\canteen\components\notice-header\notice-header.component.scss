@import '../../../../styles/cashless-theme.scss';

.titleWrapper {
  display: flex;
  flex-direction: row;
  margin-bottom: 15px;
  margin-left: 15px;
  align-items: center;
}

.titleMain {
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 22px;
  margin: 0;
  margin-left: 8px;
}

.titleDescription {
  margin: 0;
  font-size: 20px;
}

.titleDescriptionWrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
}

.titleDescriptionButton {
  display: flex;
  flex-direction: row;
  cursor: pointer;
}

.titleDescriptionButtonText {
  color: $orange-3;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: right;
  margin: 0;
  margin-right: 9px;
}
