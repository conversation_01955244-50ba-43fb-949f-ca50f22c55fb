<mat-dialog-content>
  <modal-header [title]="title" (close)="CloseModal()"></modal-header>

  <!-- Form -->
  <div *ngIf="showForm">
    <form *ngIf="form" [formGroup]="form">
      <div class="pb-1">
        <mat-radio-group formControlName="transferType" class="radio-button-wrapper">
          <mat-radio-button [value]="transferTypeEnum.Transfer" class="mr-4"
            ><span class="ml-1">Transfer from user</span></mat-radio-button
          >
          <mat-radio-button [value]="transferTypeEnum.Credit"
            ><span class="ml-1">Credit user</span></mat-radio-button
          >
        </mat-radio-group>
      </div>

      <!-- Credit -->
      <div *ngIf="selectedTransferType === transferTypeEnum.Credit">
        <div class="pb-2 pt-3">
          <input-number
            placeholder="Credit Amount"
            formControlName="creditAmount"
            [showErrorOutline]="creditAmount.invalid && creditAmount.touched"
            showDollarSign="true"
            autoFocus="true"
          ></input-number>
        </div>
        <div class="pb-2">
          <input-select-list-v2
            formControlName="schoolId"
            placeholder="School"
            [values]="listSchools"
          ></input-select-list-v2>
        </div>
        <div class="pb-2">
          <input-text-v2
            placeholder="Description"
            formControlName="creditDescription"
            [showErrorOutline]="creditDescription.invalid && creditDescription.touched"
          ></input-text-v2>
        </div>
      </div>

      <!-- Transfer -->
      <div *ngIf="selectedTransferType === transferTypeEnum.Transfer">
        <div class="pb-2 pt-3">
          <input-number
            placeholder="Amount"
            formControlName="transferAmount"
            [showErrorOutline]="transferAmount.invalid && transferAmount.touched"
            showDollarSign="true"
            autoFocus="true"
          ></input-number>
        </div>
        <div class="pb-2">
          <input-number
            placeholder="To"
            formControlName="transferTo"
            [showErrorOutline]="transferTo.invalid && transferTo.touched"
            [hintText]="transferToHintText"
          ></input-number>
        </div>
        <div class="pb-2">
          <input-text-v2
            placeholder="Description"
            formControlName="transferDescription"
            [showErrorOutline]="transferDescription.invalid && transferDescription.touched"
          ></input-text-v2>
        </div>
      </div>
    </form>

    <div class="d-flex justify-content-end align-items-end pt-3" style="gap: 10px">
      <basic-button-v2 text="Cancel" buttonStyle="secondary" (onPress)="CloseModal()"></basic-button-v2>
      <basic-button-v2
        text="{{ selectedTransferType }}"
        buttonStyle="primary"
        (onPress)="ConfirmClick()"
      ></basic-button-v2>
    </div>
  </div>

  <!-- Form Submission Result  -->
  <div *ngIf="!showForm">
    <div class="row">
      <div class="col-12">
        <p>{{ message }}</p>
      </div>
    </div>

    <div class="d-flex justify-content-end align-items-end pt-3" style="gap: 10px">
      <basic-button-v2
        *ngIf="!transferComplete && !hasError"
        text="Cancel"
        buttonStyle="secondary"
        (onPress)="CloseModal()"
      ></basic-button-v2>
      <basic-button-v2
        text="{{ confirmButtonText }}"
        buttonStyle="primary"
        (onPress)="ConfirmButtonPress()"
        [loading]="spinner"
      ></basic-button-v2>
    </div>
  </div>
</mat-dialog-content>
