<table mat-table [dataSource]="dataSource" class="mat-elevation-z8 tableau classesTable">
  <!--- Note that these columns can be defined in any order.
        The actual rendered columns are set as a property on the row definition" -->

  <!-- Id Column -->
  <ng-container matColumnDef="id">
    <th mat-header-cell *matHeaderCellDef>No.</th>
    <td mat-cell *matCellDef="let element">{{ element.ClassId }}</td>
  </ng-container>

  <!-- Organisation Column -->
  <ng-container matColumnDef="name">
    <th mat-header-cell *matHeaderCellDef>Name</th>
    <td mat-cell *matCellDef="let element">{{ element.Name }}</td>
  </ng-container>

  <ng-container matColumnDef="teacher">
    <th mat-header-cell *matHeaderCellDef>Teacher</th>
    <td mat-cell *matCellDef="let element">{{ element.Teacher }}</td>
  </ng-container>

  <ng-container matColumnDef="yearGroup">
    <th mat-header-cell *matHeaderCellDef>Year Group</th>
    <td mat-cell *matCellDef="let element">{{ element.YearGroup | stringArrayFormat }}</td>
  </ng-container>

  <ng-container matColumnDef="options" stickyEnd>
    <th mat-header-cell *matHeaderCellDef></th>
    <td mat-cell *matCellDef="let element">
      <mat-icon matTooltip="Edit" (click)="EditSchool(element)" class="actionTableau">edit</mat-icon>
    </td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>
