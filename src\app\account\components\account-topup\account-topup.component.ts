import { Component, OnInit, OnDestroy } from '@angular/core';
import { Location } from '@angular/common';

import { Subscription } from 'rxjs';

import { PayService } from '../../../sharedServices';

import { BaseComponent } from '../../../sharedModels';

@Component({
  selector: 'app-account-topup',
  templateUrl: './account-topup.component.html',
  styleUrls: ['./account-topup.component.scss'],
})
export class AccountTopupComponent extends BaseComponent implements OnInit, OnDestroy {
  accountBalance: number;
  subscriptionBalance: Subscription;
  topUpAmount: number;

  constructor(private payService: PayService, private _location: Location) {
    super();
  }

  ngOnInit(): void {
    this.subscriptionBalance = this.payService
      .SubscribeBalanceUpdate()
      .subscribe(newBalance => (this.accountBalance = newBalance));

    this.payService.UpdateBalance();
  }

  ngOnDestroy(): void {
    this.subscriptionBalance?.unsubscribe();
  }

  backClicked(): void {
    this._location.back();
  }

  TopUpAmountChanged(amount: number): void {
    this.topUpAmount = amount;
  }
}
