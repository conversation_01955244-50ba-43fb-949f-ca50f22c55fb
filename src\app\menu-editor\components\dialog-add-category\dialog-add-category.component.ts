import { Component, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

// Model
import { BaseComponent, ItemCategory } from 'src/app/sharedModels';

// Service
import { SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'dialog-add-category',
  templateUrl: './dialog-add-category.component.html',
  styleUrls: ['./dialog-add-category.component.scss'],
})
export class DialogAddCategoryComponent extends BaseComponent implements OnInit {
  categoryAdded: boolean = false;
  isProcessing: boolean = false;
  categoryName: string;
  private _categoryResult: ItemCategory;

  constructor(public dialogRef: MatDialogRef<DialogAddCategoryComponent>) {
    super();
  }

  ngOnInit() {}

  Close() {
    this.dialogRef.close();
  }

  SubmitCategory() {}
}
