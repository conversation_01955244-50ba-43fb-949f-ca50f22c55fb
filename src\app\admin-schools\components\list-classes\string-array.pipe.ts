import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'stringArrayFormat',
})

/**
 * transform an array that is formatted into a string split by commas to
 * normal sentence structure (add spaces after the commas)
 */
export class StringArrayFormatPipe implements PipeTransform {
  characterToModify = /,/g;
  characterToReplace = ', ';
  commaCharacter = ',';

  constructor() {}

  transform(value: string) {
    if (!value || value.length === 1) {
      return value;
    }

    //remove comma at the end of string
    if (value.slice(-1) === this.commaCharacter) {
      value = value.slice(0, -1);
    }

    //add a space after each comma
    let formattedVal = value.replace(this.characterToModify, this.characterToReplace);

    //remove white space
    return formattedVal.trim();
  }
}
