@import '../../../../styles/cashless-theme.scss';

.errorRow {
  border-bottom: 1px solid $grey-1;
}

h3 {
  font-family: 'Bariol';
  font-style: normal;
  font-weight: 700;
  line-height: 22px;
  align-items: center;
  color: #333b44;

  & span {
    font-size: 14px;
  }
}

h4 {
  color: $orange-2;
  margin-bottom: 2px;

  & span {
    font-size: 12px;
  }
}

.subTitle {
  margin-top: 2px;
  margin-bottom: 2px;
  font-size: 12px;

  // authorize text selection
  -webkit-user-select: text; /* Chrome 49+ */
  -moz-user-select: text; /* Firefox 43+ */
  -ms-user-select: text; /* No support yet */
  user-select: text; /* Likely future */
}
