import { Component, OnInit, OnDestroy, Output, EventEmitter, Input } from '@angular/core';
import { Subscription } from 'rxjs';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';

import { MerchantService, SpinnerService } from '../../../sharedServices';
import { BaseComponent, Merchant, School, SchoolWithBillingDetails } from '../../../sharedModels';

//dialog imports
import { MatDialog } from '@angular/material/dialog';

const _columns = ['school', 'status', 'canteen-fee', 'start-date', 'invoicing-start-date', 'chevron'];

@Component({
  selector: 'merchant-linked-schools-table',
  templateUrl: './merchant-linked-schools-table.component.html',
  styleUrls: ['./merchant-linked-schools-table.component.scss'],
})
export class MerchantLinkedSchoolsTableComponent extends BaseComponent implements OnInit, OnDestroy {
  @Output() stopLoad = new EventEmitter();
  @Input() merchantType: string;
  dataSource = new MatTableDataSource<SchoolWithBillingDetails>();
  displayedColumns = _columns;
  editSubscription: Subscription;
  trackSelectedMerchant: Subscription;
  disableMode: boolean = false;
  selectedMerchant: Merchant;

  constructor(
    private merchantService: MerchantService,
    public dialog: MatDialog,
    private router: Router
  ) {
    super();
  }
  ngOnInit() {
    this.selectedMerchant = this.merchantService.getSelectedMerchant();
    this.loadData();
    this.trackSelectedMerchant = this.merchantService.selectedMerchantUpdatedEvent$.subscribe(res => {
      this.selectedMerchant = res;
      this.loadData();
    });

    this.editSubscription = this.merchantService.editMode$.subscribe(status => {
      this.disableMode = status;
    });
  }

  ngOnDestroy(): void {
    if (this.editSubscription) {
      this.editSubscription.unsubscribe();
    }
    if (this.trackSelectedMerchant) {
      this.trackSelectedMerchant.unsubscribe();
    }
  }

  loadData() {
    //global spinner is started in admin-list-merchants component
    //stopload.emit communicates with admin-list-merchants component to turn it off

    this.merchantService.GetSchoolsLinkedToMerchant(this.selectedMerchant.canteenId).subscribe({
      next: (res: SchoolWithBillingDetails[]) => {
        this.RefreshTable(res);
        this.stopLoad.emit();
      },
      error: error => {
        this.stopLoad.emit();
        this.handleErrorFromService(error);
      },
    });
  }

  RefreshTable(schools: SchoolWithBillingDetails[]) {
    this.dataSource.data = schools;
  }

  isListEmpty() {
    return !this.dataSource.data || this.dataSource.data.length === 0;
  }

  LinkSchoolClick() {
    this.router.navigate([`./admin/merchants/${this.selectedMerchant.canteenId}/schoolSearch`]);
  }
}
