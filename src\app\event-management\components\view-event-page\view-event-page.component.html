<div class="container-fluid sticky">
  <div class="row user-header">
    <div class="col-6 col-lg-9">

      <nav-back-button
      smallText="true"
      (navBack)="goBack()"
      text="Go Back"
      class="backButton"
      smallFont="true"
      noPadding="true"
    ></nav-back-button>
    </div>
    <div class="col-6 col-lg-3 button-align">
      <active-indicator [isActive]="schoolEvent.IsActive"></active-indicator>

      <basic-button-v2
        [text]="getTextPublishButton()"
        [buttonStyle]="schoolEvent.IsActive ? 'basic' : 'primaryOrange'"
        (onPress)="PublishEvent()"
        ></basic-button-v2>

    </div>
  </div>
</div>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h2>Details</h2>
    </div>
  </div>
  
  <div class="row mb-4">
    <div class="col-12 col-md-7 col-lg-9">
      <event-detail-block
        [schoolEvent]="schoolEvent"
        (deleteEvent)="deleteEventPrompt()"
        (editEvent)="editEventPrompt()"
      ></event-detail-block>
    </div>
  
    <div class="col-12 col-md-5 col-lg-3">
      <div class="row-container">
        <h3 class="mb-3">Event Image</h3>
        <event-image [eventId]="schoolEvent.SchoolEventId" [imageUrl]="schoolEvent.ImageUrl"></event-image>
      </div>  
  </div>
</div>
  
<event-items
  [items]="items"
  [merchantId]="this.schoolEvent.MerchantId"
  [eventId]="schoolEvent.SchoolEventId"
  [eventIsPublish]="schoolEvent.IsActive"
></event-items>
  
</div>