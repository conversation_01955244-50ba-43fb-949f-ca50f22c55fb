import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Order, OrderItem } from 'src/app/sharedModels';

@Component({
  selector: 'selected-order',
  templateUrl: './selected-order.component.html',
  styleUrls: ['./selected-order.component.scss'],
})
export class SelectedOrderComponent implements OnInit {
  @Input() order: Order;
  @Input() itemsSelectedOrder: OrderItem[];
  @Input() showOrder: boolean;
  @Output() printOrder = new EventEmitter();

  constructor() {}

  ngOnInit(): void {}

  GetItemOptions(item: OrderItem): string {
    let optionsText = '(';

    //item stores modifiers in Options
    if (item.Options && item.Options.length > 0) {
      item.Options.forEach(opt => {
        // item has selected options
        if (opt.SelectedOptionList && opt.SelectedOptionList.length > 0) {
          opt.SelectedOptionList.forEach(selectedOpt => {
            optionsText += selectedOpt + ', ';
          });
        }
      });
    }

    //item stores modifiers in SelectedOptions
    if (item.SelectedOptions && item.SelectedOptions.length > 0) {
      item.SelectedOptions.forEach(opt => {
        optionsText += opt.OptionName + ', ';
      });
    }

    optionsText = optionsText.slice(0, -2);
    optionsText += ')';
    if (optionsText.length <= 1) {
      optionsText = '';
    }

    return optionsText;
  }

  printButtonPressed() {
    this.printOrder.emit();
  }
}
