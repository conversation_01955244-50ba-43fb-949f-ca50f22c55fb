.emptyBlockText {
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 24px;
  text-align: center;
}

.articleWrapper {
  background: #ffffff;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 16px 16px 16px 19px;
  margin-bottom: 16px;
  cursor: pointer;
}

.articleTitle {
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 24px;
  margin: 0 0 16px 0;
}

.articleText {
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 28px;
  margin: 0;
  max-height: 84px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.articleModalTitle {
  font-style: normal;
  font-weight: bold;
  font-size: 24px;
  line-height: 32px;
  margin: 0;
  padding-top: 32px;
  margin: 24px 0;
}

.articleModalText {
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 28px;
  margin: 0;
}

::ng-deep .mat-mdc-dialog-container {
  height: 90%;
  align-self: flex-end;
  position: relative;

  .closeLink {
    position: absolute;
    top: 0;
    right: 0;
    margin: 20px;
    cursor: pointer;
  }
}
