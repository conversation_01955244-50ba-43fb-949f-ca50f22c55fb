import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { AddSchoolClassComponent } from './add-school-class.component';

describe('AddSchoolClassComponent', () => {
  let component: AddSchoolClassComponent;
  let fixture: ComponentFixture<AddSchoolClassComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [AddSchoolClassComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AddSchoolClassComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
