<ng-container *ngIf="loading; else showCards">
  <div class="col-12 d-flex justify-content-center align-items-center p-2">
    <app-spinner [manual]="true"></app-spinner>
  </div>
</ng-container>

<ng-template #showCards>
  <div *ngIf="noPaymentMethod" class="row justify-content-center">
    <div class="col-12">
      <h3 class="text-center">No payment method connected</h3>
    </div>
  </div>

  <div
    class="row justify-content-center"
    *ngFor="let method of listPaymentMethod; index as i"
    [ngClass]="{ rowMethodSelected: isCurrentMethod(method.id) }"
  >
    <div
      class="col-10 col-md-8 col-lg-9 imageCol"
      [ngClass]="{ noPointer: canSelect }"
      (click)="setSelectedMethod(method.id)"
      id="payment-method-option-{{ i }}"
    >
      <h3>
        <span *ngIf="method.card.is_debit == 'true'">Debit </span>
        {{ method.card.card_type }} (Ending in {{ method.card.last_four }})
      </h3>
    </div>
    <div class="col-2 col-md-4 col-lg-3 deleteCol">
      <ng-container *ngIf="showDelete">
        <a class="d-none d-md-block deleteLink" (click)="deleteClick(method.id)">Delete Card</a>
        <mat-icon
          class="d-block d-md-none imgDelete"
          (click)="deleteClick(method.id)"
          id="delete-payment-method-option-{{ i }}"
          >delete</mat-icon
        >
      </ng-container>
    </div>
  </div>
</ng-template>
