<div class="row">
  <div class="col-8">
    <h2>Items <span *ngIf="items.length > 0">({{items.length}})</span></h2>
  </div>
  <div class="col-4 button-align">
      <basic-button-v2
        class="pr-2"
        (onPress)="addExistingItemDialog()"
        text="Add existing item"
        buttonStyle="secondaryOrange"
      ></basic-button-v2>
    
      <basic-button-v2
        (onPress)="CreateNewItemDialog()"
        text="Create new item"
        buttonStyle="secondaryOrange"
      ></basic-button-v2>
    </div>
</div>


<table mat-table [dataSource]="dataSource">
  <ng-container matColumnDef="Image">
    <th mat-header-cell *matHeaderCellDef>Image</th>
    <td mat-cell *matCellDef="let element">
      <item-image [url]="element.imageUrl"></item-image>
    </td>
  </ng-container>
  <ng-container matColumnDef="Item">
    <th mat-header-cell *matHeaderCellDef>Item</th>
    <td mat-cell *matCellDef="let element">
      <strong>{{ element.name }}</strong>
    </td>
  </ng-container>
  <ng-container matColumnDef="Price">
    <th mat-header-cell *matHeaderCellDef>Price</th>
    <td mat-cell *matCellDef="let element">{{ element.price | currency }}</td>
  </ng-container>
  <ng-container matColumnDef="Option">
    <th mat-header-cell *matHeaderCellDef>Options</th>
    <td mat-cell *matCellDef="let element">
      <grouped-option-chip-list [optionCategories]="element.options" (optionsChanged)="RefreshListItems()" />
    </td>
  </ng-container>

  <ng-container matColumnDef="Remaining Quantity">
    <th mat-header-cell *matHeaderCellDef>Remaining Quantity</th>
    <td mat-cell *matCellDef="let element">
      <span *ngIf="element.stock > 0">{{ element.stock }}</span>
      <strong *ngIf="element.stock != null && element.stock <= 0">Out of stock</strong>
    </td>
  </ng-container>

  <ng-container matColumnDef="Edit">
    <th mat-header-cell *matHeaderCellDef></th>
    <td mat-cell *matCellDef="let element" class="editImg">
      <div class="actions-container">
        <img
          *ngIf="isEditFlow"
          src="/assets/icons/black-pencil.svg"
          alt="pencil"
          [routerLink]="['./editor/item', element.menuItemId]"/>
        <img
          src="/assets/icons/purpleCross.svg"
          alt="cross"
          (click)="UnlinkItemPrompt(element)"/>
      </div>

    </td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  <tr *matNoDataRow>
    <td class="no-items-row p-3">No items</td>
  </tr>
</table>
