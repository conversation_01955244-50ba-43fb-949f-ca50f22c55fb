import { Component, OnInit, OnDestroy } from '@angular/core';
import * as moment from 'moment';

//Models
import {
  UserCashless,
  BaseComponent,
  WeekDayLongForm,
  DateSchool,
  MenuTypeEnum,
  MenuNames,
  GroupedOrderBlockInfo,
  OrderStatusEnum,
  WeekDayStatusEnum,
  WeekDayAbbreviation,
  OrderBlockInfo,
  A_COMPLETE_WEEK,
} from '../../../sharedModels';

// Ngrx
import { Store, select } from '@ngrx/store';
import { Observable, Subscription, timer } from 'rxjs';
import { FamilyState } from '../../../states';
import { selectedChild } from '../../../states/children/children.selectors';
import * as familyActions from '../../../states/family/family.actions';

//Services
import {
  OrderService,
  HomeService,
  DebounceService,
  MenuCustomNameService,
  ParentHomeService,
  PayService,
} from '../../../sharedServices';
import {
  GetWeekDayDate,
  addDayToDate,
  formatDateToUniversal,
  GetCurrentWeekNumber,
  subtractDayFromDate,
  DateHasPassed,
  AddTimeToDate,
  UNIVERSAL_DATE_FORMAT,
  ConvertStringToDate,
} from 'src/app/utility';

//Animations
import { trigger, transition, useAnimation } from '@angular/animations';
import { fadeIn } from 'ng-animate';
import { Notice, NoticeType } from 'src/app/sharedModels/canteen/canteen-notice';
import {
  HomeData,
  HomeRequest,
  MenuHomeScreenInfo,
  OrderHomeScreenInfo,
  OrderTableData,
  SchoolEventHomeScreenInfo,
} from 'src/app/sharedModels/home/<USER>';
import { selectedWeek } from 'src/app/states/family/family.selectors';
import { GetCustomMenuName } from 'src/app/sharedServices/menu/menu-custom-name';
import { convertSchoolDateTimeToLocalDateTime } from 'src/app/utility/timezone-helper';

/**
 * In this component the dates for the menus and events are converted to local time
 **/

@Component({
  selector: 'app-family',
  templateUrl: './family.component.html',
  styleUrls: ['./family.component.scss'],
  animations: [
    trigger('bounce', [
      transition(
        '* => *',
        useAnimation(fadeIn, {
          // Set the duration to 5seconds and delay to 2seconds
          params: { timing: 0.5, delay: 0 },
        })
      ),
    ]),
  ],
})
export class FamilyComponent extends BaseComponent implements OnInit, OnDestroy {
  private selectedSubscription: Subscription;
  private timerSubscription: Observable<number>;
  private refreshTimerSubscription: Subscription;
  selectChild: UserCashless;
  currentTab: string = 'Canteen';
  bounce: any;
  weekNumber: number;
  activeAnnouncements: Notice[];
  eventListData: SchoolEventHomeScreenInfo[] = [];
  hasUniformMenu: boolean = false;
  menuList: MenuHomeScreenInfo[];
  orderTableData: OrderTableData;
  selectedWeekSubscription: Subscription;
  menuNames: MenuNames;
  loading: boolean = true;
  groupedOrderTableData: GroupedOrderBlockInfo;
  firstRun: boolean = true;
  refreshCount: number = 0;

  constructor(
    private store: Store<{ family: FamilyState }>,
    private orderService: OrderService,
    private homeService: HomeService,
    private debounceService: DebounceService,
    private menuCustomNameService: MenuCustomNameService,
    private parentHomeService: ParentHomeService,
    private payService: PayService
  ) {
    super();
  }

  ngOnInit() {
    this.selectedSubscription = this.store.pipe(select(selectedChild)).subscribe((child: UserCashless) => {
      this.selectChild = child;
      if (child) {
        this.getHomeDataDebounce();
      }
      this.refreshOrders();
    });

    this.selectedWeekSubscription = this.store.pipe(select(selectedWeek)).subscribe((week: number) => {
      this.weekNumber = week;
      if (this.firstRun) {
        this.firstRun = false;
        return;
      }
      this.getHomeRefreshData(week);
    });

    //Inits tabs
    this.SetSelectedTab(this.orderService.GetCanteenTab());
  }

  ngOnDestroy() {
    if (this.selectedSubscription) {
      this.selectedSubscription.unsubscribe();
    }
    if (this.selectedWeekSubscription) {
      this.selectedWeekSubscription.unsubscribe();
    }
    this.unsubscribeTimer();
  }

  getHomeDataDebounce = this.debounceService.callDebounce(this.getHomeData, 450, false, true);

  private getHomeData(): void {
    this.parentHomeService.setLoading(true);
    const request = this.getHomeDataRequest(this.weekNumber, true);
    this.homeService.GetHomeData(request).subscribe({
      next: (response: HomeData) => {
        this.processHomeData(response, this.weekNumber);
      },
      error: error => {
        this.handleErrorFromService(error);
      },
      complete: () => {
        this.parentHomeService.setLoading(false);
      },
    });
  }

  private getHomeRefreshData(weekNumber: number): void {
    this.parentHomeService.setLoading(true);
    const request = this.getHomeDataRequest(weekNumber, false);
    this.homeService.GetHomeDataRefresh(request).subscribe({
      next: (response: HomeData) => {
        this.getOrderTableData(this.menuList, response.orders, response.schoolDates, weekNumber);
      },
      error: error => {
        this.handleErrorFromService(error);
      },
      complete: () => {
        this.parentHomeService.setLoading(false);
      },
    });
  }

  getHomeDataRequest(weekNumber: number, loadEvents: boolean): HomeRequest {
    if (!this.selectChild?.UserId) {
      return;
    }
    const startDate = GetWeekDayDate(weekNumber, WeekDayLongForm.Monday);
    const endDate = GetWeekDayDate(weekNumber, WeekDayLongForm.Friday);

    const request = new HomeRequest();
    request.StudentId = this.selectChild.UserId;
    request.StartOrderDate = formatDateToUniversal(subtractDayFromDate(startDate, 1));
    request.EndOrderDate = formatDateToUniversal(addDayToDate(endDate, 1));
    request.LoadEvents = loadEvents;
    return request;
  }

  processHomeData(response: HomeData, weekNumber: number): void {
    this.eventListData = this.getEventsWithLocalTime(response.events);
    this.hasUniformMenu = response.hasUniformMenu;
    this.getAvailableMenus(response.menus);
    this.processNotices(response.notices);
    this.getOrderTableData(this.menuList, response.orders, response.schoolDates, weekNumber);
  }

  getEventsWithLocalTime(eventList: SchoolEventHomeScreenInfo[]): SchoolEventHomeScreenInfo[] {
    const utcOffset = this.selectChild.SchoolTimeZoneOffSetHours;
    return eventList?.map((e: SchoolEventHomeScreenInfo) => {
      const cutOffDateString: string = convertSchoolDateTimeToLocalDateTime(e.cutOffDate, utcOffset);
      return {
        ...e,
        cutOffDate: ConvertStringToDate(cutOffDateString),
      };
    });
  }

  processNotices(notices: Notice[]): void {
    if (!notices?.length) {
      this.activeAnnouncements = [];
      return;
    }
    this.activeAnnouncements = notices.filter(
      (notice: Notice) =>
        notice.NoticeType == NoticeType.Announcement &&
        (notice.EndDate == null || !DateHasPassed(notice.EndDate))
    );
  }

  /////////////////////////////////////////////////
  // Tabs functions
  /////////////////////////////////////////////////
  tabClick(newTab: string) {
    this.SetSelectedTab(newTab);
    //set the selectedWeek to the current week
    let week = GetCurrentWeekNumber();
    this.store.dispatch(familyActions.familySetSelectedWeek({ weekNumber: week }));
  }

  SetSelectedTab(newTab: string) {
    this.currentTab = newTab;
    this.orderService.SetCanteenTab(newTab);
  }

  private refreshOrders() {
    if (this.timerSubscription) {
      return;
    }
    this.timerSubscription = timer(30000, 30000);
    this.refreshTimerSubscription = this.timerSubscription.subscribe(res => {
      if (this.refreshCount > 20) {
        return;
      }
      this.getHomeRefreshData(this.weekNumber);
      this.payService.UpdateBalance();
      this.refreshCount++;
    });
  }

  /** unsubscribe timer */
  private unsubscribeTimer() {
    this.timerSubscription = null;

    if (this.refreshTimerSubscription) {
      this.refreshTimerSubscription.unsubscribe();
    }
  }

  /////////////////////////////////////////////////
  // ORDER TABLE PROCESSING METHODS
  /////////////////////////////////////////////////

  getOrderTableData(
    menuList: MenuHomeScreenInfo[],
    orderList: OrderHomeScreenInfo[],
    schoolCloseDates: DateSchool[],
    weekNumber: number
  ): void {
    const tableData = {
      menuList: menuList,
      orderList: orderList,
      schoolClosedDates: schoolCloseDates,
      weekNumber: weekNumber,
      menuNames: this.menuNames,
      schoolOpeningDays: this.selectChild?.SchoolOpeningDays || A_COMPLETE_WEEK,
    };

    this.groupedOrderTableData = this.getWeekOrderData(this.menuNames, tableData);
  }

  getAvailableMenus(menuList: MenuHomeScreenInfo[]) {
    this.menuList = menuList;
    // if menu name is null it means the menu is unavailable
    this.menuNames = {
      Recess: this.getMenuDisplayName(MenuTypeEnum.Recess),
      Lunch: this.getMenuDisplayName(MenuTypeEnum.Lunch),
    };
  }

  getMenuDisplayName(menuType: string): string {
    if (!this.menuList?.length || !menuType) return null;
    const menuTypeIndex = this.getMenuIndex(menuType);
    if (menuTypeIndex < 0) return null;
    return GetCustomMenuName(menuType, this.menuList[menuTypeIndex].name);
  }

  getMenuIndex(menuType: string): number {
    if (!this.menuList?.length || !menuType) return -1;
    return this.menuList?.findIndex(x => x.menuType === menuType);
  }

  getWeekOrderData(menuNames: MenuNames, orderTableData: OrderTableData): GroupedOrderBlockInfo {
    let data: GroupedOrderBlockInfo = new GroupedOrderBlockInfo();
    if (menuNames?.Recess) {
      const recessDayData = this.getMenuDayData(MenuTypeEnum.Recess, orderTableData, menuNames);
      data.Recess = [];
      data.Recess = recessDayData;
    }
    if (menuNames?.Lunch) {
      const lunchDayData = this.getMenuDayData(MenuTypeEnum.Lunch, orderTableData, menuNames);
      data.Lunch = [];
      data.Lunch = lunchDayData;
    }
    return data;
  }

  getMenuDayData(
    menuType: MenuTypeEnum,
    orderTableData: OrderTableData,
    menuNames: MenuNames
  ): OrderBlockInfo[] {
    if (!orderTableData?.menuList) {
      return;
    }
    const weekDays = this.getGivenWeekDays(orderTableData.weekNumber);

    let days: OrderBlockInfo[] = [];
    weekDays.forEach((date: string) => {
      days.push(this.getDayData(menuType, date, orderTableData, menuNames));
    });
    return days;
  }

  getDayData(
    menuType: MenuTypeEnum,
    date: string,
    orderTableData: OrderTableData,
    menuNames: MenuNames
  ): OrderBlockInfo {
    const openingDays = this.convertWeekDays(orderTableData.schoolOpeningDays);
    const { schoolIsClose, order, passed, menuCutOffTime, menuId } = this.getDailyOrderInfo(
      menuType,
      orderTableData.orderList,
      date,
      openingDays,
      orderTableData.schoolClosedDates,
      orderTableData.menuList
    );
    const status = this.getWeekdayStatus(order, passed, schoolIsClose);

    return {
      menuType: menuType,
      orderStatus: status,
      orderDate: date,
      menuName: menuNames[menuType],
      cutOffDate: ConvertStringToDate(menuCutOffTime),
      orderId: order?.orderId || null,
      menuId: menuId,
    };
  }

  getGivenWeekDays(selectedWeek: number) {
    const week = [];
    for (let i = 0; i < 5; i++) {
      const date = moment()
        .week(selectedWeek)
        .weekday(i + 1);
      week.push(date.format());
    }
    return week;
  }

  convertWeekDays(openingDays: string): string[] {
    const days = openingDays?.split(',');
    return days?.map(day => WeekDayAbbreviation[day]) || [];
  }

  getDailyOrderInfo(
    menuType: string,
    orderInformation: OrderHomeScreenInfo[],
    day: string,
    schoolOpenDays: string[],
    schoolCloseDates: DateSchool[],
    menus: MenuHomeScreenInfo[]
  ) {
    if (menus?.length <= 0) {
      return;
    }
    const schoolIsClose: boolean = this.isSchoolClosed(schoolOpenDays, schoolCloseDates, day);
    const order = orderInformation?.find(
      x => x.menuType === menuType && moment(x.orderDate).date() === moment(day).date()
    );

    const menu: MenuHomeScreenInfo = menus.find(m => m.menuType === menuType);
    const passed: boolean = this.hasPassedLateCutOffTime(menu, day);
    const menuCutOffDateTime: string = this.getLocalMenuCutOffTime(day, menu.cutOffTime);
    return { schoolIsClose, order, passed, menuCutOffTime: menuCutOffDateTime, menuId: menu.menuId };
  }

  getLocalMenuCutOffTime(day: string, menuCutOffTime: Date): string {
    const menuCutOffDateTime = AddTimeToDate(day, menuCutOffTime);
    return convertSchoolDateTimeToLocalDateTime(
      menuCutOffDateTime,
      this.selectChild.SchoolTimeZoneOffSetHours
    );
  }

  hasPassedLateCutOffTime(menu: MenuHomeScreenInfo, day: string): boolean {
    const lateCutOffTime = menu?.lateCutOffTime ? menu.lateCutOffTime : menu.cutOffTime;
    const dateTime = AddTimeToDate(day, lateCutOffTime);
    const localDateTime = convertSchoolDateTimeToLocalDateTime(
      dateTime,
      this.selectChild.SchoolTimeZoneOffSetHours
    );
    return DateHasPassed(localDateTime);
  }

  isSchoolClosed(schoolOpenDays: string[], schoolCloseDates: DateSchool[], date: string): boolean {
    return (
      Boolean(
        schoolCloseDates?.find(
          x =>
            moment(x.StartDate).format(UNIVERSAL_DATE_FORMAT) <= moment(date).format(UNIVERSAL_DATE_FORMAT) &&
            moment(x.EndDate).format(UNIVERSAL_DATE_FORMAT) >= moment(date).format(UNIVERSAL_DATE_FORMAT)
        )
      ) || schoolOpenDays.findIndex(openDay => openDay === moment(date).format('ddd')) < 0
    );
  }

  getWeekdayStatus(order: OrderHomeScreenInfo, passed: boolean, schoolIsClose: boolean): WeekDayStatusEnum {
    if (order) {
      if (order.orderStatusId == OrderStatusEnum.Draft) {
        return WeekDayStatusEnum.processing;
      } else if (order.orderStatusId == OrderStatusEnum.Error) {
        return passed ? WeekDayStatusEnum.passedError : WeekDayStatusEnum.error;
      } else {
        return passed ? WeekDayStatusEnum.passedSucceed : WeekDayStatusEnum.success;
      }
    } else if (schoolIsClose) {
      return WeekDayStatusEnum.closed;
    } else {
      return passed ? WeekDayStatusEnum.passed : WeekDayStatusEnum.order;
    }
  }
}
