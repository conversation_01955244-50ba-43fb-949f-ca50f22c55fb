import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { DialogAddOptionComponent } from './dialog-add-option.component';

describe('DialogAddOptionComponent', () => {
  let component: DialogAddOptionComponent;
  let fixture: ComponentFixture<DialogAddOptionComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [DialogAddOptionComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogAddOptionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
