import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

// components
import {
  AdminListMerchantsComponent,
  LinkSchoolToMerchantPageComponent,
  AddMerchantUserSearchComponent,
  MerchantUserFormComponent,
  CreateMerchantSearchComponent,
  CreateMerchantFormComponent,
  MerchantLinkedSchoolsDetailsComponent,
  FinanceReportComponent,
  FinanceReportWithHistoryComponent,
} from './components';

import {
  ListMerchantsAdminResolver,
  currentMerchantResolver,
  UserDetailsResolver,
  MerchantSchoolResolver,
  MerchantUserFormResolver,
} from '../sharedServices';

import { GeneratedInvoiceList } from './components/finance-report-with-history/generatedInvoiceList.resolver';

// routes
const routes: Routes = [
  {
    path: '',
    component: AdminListMerchantsComponent,
    resolve: { merchants: ListMerchantsAdminResolver },
  },
  {
    path: 'financeReport',
    component: FinanceReportComponent,
  },
  {
    path: 'financeReportv2',
    component: FinanceReportWithHistoryComponent,
    resolve: {
      generatedInvoiceList: GeneratedInvoiceList,
    },
  },
  {
    path: ':merchantId/schoolSearch',
    component: LinkSchoolToMerchantPageComponent,
    resolve: { merchant: currentMerchantResolver },
  },
  {
    path: ':merchantId/userSearch',
    component: AddMerchantUserSearchComponent,
    resolve: { merchant: currentMerchantResolver },
  },
  {
    path: ':merchantId/userSearch/:id',
    component: MerchantUserFormComponent,
    resolve: {
      user: UserDetailsResolver,
      merchant: currentMerchantResolver,
      schools: MerchantSchoolResolver,
    },
  },
  {
    path: ':merchantId/editmerchantuser/:id',
    component: MerchantUserFormComponent,
    resolve: {
      user: MerchantUserFormResolver,
      merchant: currentMerchantResolver,
      schools: MerchantSchoolResolver,
    },
  },
  {
    path: 'createmerchant',
    component: CreateMerchantSearchComponent,
  },
  {
    path: ':merchantId/school/:schoolId',
    component: MerchantLinkedSchoolsDetailsComponent,
    // resolve: {
    //   feeData: FeeCalculatorResolver,
    // },
  },
  {
    path: 'createmerchant/:id',
    component: CreateMerchantFormComponent,
    resolve: { user: UserDetailsResolver },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminMerchantRoutingModule {}
