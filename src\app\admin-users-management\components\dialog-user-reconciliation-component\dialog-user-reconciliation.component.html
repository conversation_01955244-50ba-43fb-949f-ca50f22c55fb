<mat-dialog-content>
  <modal-header title="Reconcile User's Balance" (close)="closeModal()"></modal-header>

  <span *ngIf="isReconciliationFail" class="redText">Unable to reconcile user balance</span>

  <ul class="infoList">
    <li><strong class="noTextHighlight">Account Balance:</strong>{{ this.balance }}</li>
    <li><strong class="noTextHighlight">Last Reconciled (UTC):</strong><span [ngClass]="{'greenText': isReconciliationSuccess}">{{ this.reconciliationDateUtc }}</span></li>
  </ul>

  <div class="rangeSelector">
    <mat-checkbox formControlName="selectRange" (change)="updateSelectRange($event)">Select Range (Optional)</mat-checkbox>
  </div>

  <form *ngIf="selectRange" [formGroup]="form">
    <div class="reconciliationStyle">
      <input-date placeholder="Select Start Date" formControlName="selectedStartDate"></input-date>
      <input-date placeholder="Select End Date" formControlName="selectedEndDate"></input-date>
    </div>
  </form>

  <div class="reconciliationStyle">
    <basic-button-v2 text="Cancel" buttonStyle="secondary" (onPress)="closeModal()"></basic-button-v2>
    <basic-button-v2 text="Reconcile" buttonStyle="primary" (onPress)="onSubmit()"></basic-button-v2>
  </div>
</mat-dialog-content>

