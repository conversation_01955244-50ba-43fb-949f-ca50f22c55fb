<ng-container>
  <h2>Stock</h2>
  <div class="cardDefaultCanteen">
    <div class="row">
      <div class="col-12">
        <p *ngIf="!stockList">
          <a [routerLink]="['../../stocks']">Link stock</a>
        </p>
        <table *ngIf="stockList">
          <tr>
            <th>Id</th>
            <th>Stock</th>
            <th>Daily quantity</th>
            <th></th>
            <th></th>
          </tr>
          <tr *ngFor="let stock of stockList" class="line">
            <td>{{ stock.StockId }}</td>
            <td>{{ stock.StockName }}</td>
            <td>{{ stock.DailyStock }}</td>
            <td>
              <mat-icon matTooltip="Edit" class="actionTableau" [routerLink]="['../../stock', stock.StockId]"
                >edit</mat-icon
              >
            </td>
            <td>
              <mat-icon matTooltip="Remove" class="actionTableau" (click)="RemoveStock()">delete</mat-icon>
            </td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</ng-container>
