import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UniformOrderStatusPickerComponent } from './uniform-order-status-picker.component';

describe('UniformOrderStatusPickerComponent', () => {
  let component: UniformOrderStatusPickerComponent;
  let fixture: ComponentFixture<UniformOrderStatusPickerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ UniformOrderStatusPickerComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(UniformOrderStatusPickerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
