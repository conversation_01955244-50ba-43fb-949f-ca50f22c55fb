import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MenuEditorModule } from 'src/app/menu-editor/menu-editor.module';
import { SharedToolsModule } from 'src/app/shared-tools/shared-tools.module';
import { SharedModule } from 'src/app/shared/shared.module';

// Models
import { Option } from 'src/app/sharedModels';
import { OptionFormDialogComponent } from '../option-form-dialog/option-form-dialog.component';
import { MenuEditorApiService } from 'src/app/sharedServices';

@Component({
  selector: 'option-choices-dialog',
  templateUrl: './option-choices-dialog.component.html',
  styleUrls: ['./option-choices-dialog.component.scss'],
  standalone: true, 
  imports: [MatDialogModule, CommonModule, SharedToolsModule, SharedModule, MenuEditorModule],
})
export class OptionChoicesDialogComponent {
  option: Option = new Option();
  loading: boolean = true;

  constructor(
    public dialogRef: MatDialogRef<OptionFormDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private menuEditorApiService: MenuEditorApiService
  ){
    if(this.data.optionId > 0){
      this.loadOption();
    }else{
      this.option = new Option();
      this.loading = false;
    }
  }

  private loadOption(){
    this.menuEditorApiService.GetOptionByIdAPI(this.data.optionId).subscribe({
      next: res => {
        this.option = res;
        this.loading = false;
      },
      error: error => {
        this.closeModal();
      },
    });
  }
  
  closeModal(): void {
    this.dialogRef.close(true);
  }
}
