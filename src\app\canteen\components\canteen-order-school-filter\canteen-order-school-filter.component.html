<mat-accordion>
  <mat-expansion-panel [expanded]="false">
    <mat-expansion-panel-header>
      <mat-panel-title> Schools </mat-panel-title>
      <mat-panel-description>
        <span>{{ selectedValueSchools.length }}/{{ schools.length }}</span>
      </mat-panel-description>
    </mat-expansion-panel-header>
    <div class="row">
      <div class="col-6 col-md-3" *ngFor="let school of schools">
        <mat-checkbox
          [name]="school.SchoolId"
          [checked]="IsChecked(school.SchoolId)"
          (change)="CheckboxChanged($event)"
          >{{ school.Name }}</mat-checkbox
        >
      </div>
    </div>
    <div class="row">
      <div class="col-12 col-md-6">
        <div class="blockAction">
          <a (click)="Clear()" [ngClass]="{ active: !IsNoSchoolsSelected() }">Clear</a>
          <a (click)="SelectAll()" [ngClass]="{ active: !IsAllSelected() }">Select All</a>
        </div>
      </div>
    </div>
  </mat-expansion-panel>
</mat-accordion>
