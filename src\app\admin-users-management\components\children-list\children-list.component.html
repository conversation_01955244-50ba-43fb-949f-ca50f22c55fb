<school-panel fullHeight="true" [title]="'Children (' + children.length + ') '">
  <div class="child-wrapper">
    <table mat-table [dataSource]="dataSource" class="children-table">
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef>Name:</th>
        <td mat-cell *matCellDef="let element">{{ element.FirstName + ' ' + element.Lastname }}</td>
      </ng-container>

      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef>User ID:</th>
        <td mat-cell *matCellDef="let element">
          <a [routerLink]="['../../', element.UserId]">{{ element.UserId }}</a>
        </td>
      </ng-container>

      <ng-container matColumnDef="school">
        <th mat-header-cell *matHeaderCellDef>School:</th>
        <td mat-cell *matCellDef="let element">{{ element.SchoolName }}</td>
      </ng-container>

      <ng-container matColumnDef="class">
        <th mat-header-cell *matHeaderCellDef>Class:</th>
        <td mat-cell *matCellDef="let element">{{ element.ClassName }}</td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
  </div>
</school-panel>
