<mat-accordion>
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title> {{ title }} </mat-panel-title>
      <mat-panel-description> </mat-panel-description>
    </mat-expansion-panel-header>

    <div *ngIf="showOrderFilters()">
      <div class="row">
        <div class="d-none d-md-block col-md-6 col-lg-4 noPadding">
          <children-list label="Child" [noShadow]="true"></children-list>
        </div>

        <div *ngIf="menuType" class="col-12 col-md-12 col-lg-4 noPadding">
          <select-menu-type
            label="Ordering"
            [noShadow]="true"
            [menuType]="menuType"
            [schoolId]="student?.SchoolId"
          ></select-menu-type>
        </div>

        <div class="col-12 col-md-12 col-lg-4 noPadding">
          <select-date
            [noShadow]="true"
            label="For"
            [selectedDate]="date"
            [preOrderWeeksNumber]="schoolWeeksPreOrder"
          ></select-date>
        </div>
      </div>
    </div>

    <div *ngIf="isEdit" class="row">
      <div class="col-12">
        <p>You can't change the selection when editing an order</p>
      </div>
    </div>
  </mat-expansion-panel>
</mat-accordion>
