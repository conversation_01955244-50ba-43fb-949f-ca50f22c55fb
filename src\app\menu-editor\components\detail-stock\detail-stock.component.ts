import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Location } from '@angular/common';

// Services
import { SpinnerService, MenuEditorApiService, StockApiService } from 'src/app/sharedServices';

// Models
import { BaseComponent, Stock, UpsertStockRequest, Canteen } from 'src/app/sharedModels';

// ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { CanteenState } from '../../../states';
import { selectedCanteen } from '../../../states/canteen/canteen.selectors';
import { MatRadioChange } from '@angular/material/radio';
import { MatDialog } from '@angular/material/dialog';
import { DialogDeleteMenuItemComponent } from '../dialog-delete-menu-item/dialog-delete-menu-item.component';

@Component({
  selector: 'editor-detail-stock',
  templateUrl: './detail-stock.component.html',
  styleUrls: ['./detail-stock.component.scss'],
})
export class DetailStockComponent extends BaseComponent implements OnInit, OnDestroy {
  stock: Stock;
  private subscription: Subscription;
  selectedCanteen: Canteen;
  stockType: string = '1';

  constructor(
    private activatedRoute: ActivatedRoute,
    private _location: Location,
    private spinnerService: SpinnerService,
    private menuEditorAPIService: MenuEditorApiService,
    private stockApiService: StockApiService,
    public dialog: MatDialog,
    private store: Store<{ canteen: CanteenState }>
  ) {
    super();
  }

  ngOnInit() {
    this.subscription = this.store.pipe(select(selectedCanteen)).subscribe((canteen: Canteen) => {
      this.selectedCanteen = canteen;
      this.stock = this.activatedRoute.snapshot.data['stock'];
      if (!this.stock) {
        this.stock = new Stock();
        this.stock.IsActive = true;
      } else {
        if (!this.stock.DailyStock && this.stock.StockQuantity) {
          this.stockType = '2';
        }
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  GetTextButton(): string {
    return this.stock.StockId > 0 ? 'Save' : 'Add';
  }

  SubmitForm() {
    this.spinnerService.start();

    if (this.stockType == '1') {
      this.stock.StockQuantity = null;
    } else {
      this.stock.DailyStock = null;
    }

    let request: UpsertStockRequest = new UpsertStockRequest();
    request.Stock = this.stock;
    request.CanteenId = this.selectedCanteen.CanteenId;

    this.menuEditorAPIService.UpsertStockAPI(request).subscribe({
      next: (response: Stock) => {
        // success
        this.stock = response;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  TypeChanged(event: MatRadioChange) {
    this.stockType = event.value;
  }

  GoBackClick() {
    this._location.back();
  }

  ShowDeleteButton() {
    return this.stock.StockId > 0;
  }

  ArchiveStock(){
    this.dialog.open(DialogDeleteMenuItemComponent, {
      data: {
        displayOption: 'stock',
        onDeletePress: () => this.ConfirmArchive(),
      },
    });
  }

  ConfirmArchive(){
    this.spinnerService.start();
    this.stockApiService.ArchiveStock(this.stock.StockId).subscribe({
      next: () => {
        this.spinnerService.stop();
        this.GoBackClick();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }
}
