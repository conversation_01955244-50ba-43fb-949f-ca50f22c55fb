<div class="row">
  <div class="col-12">
    <h2>Menu & Date</h2>
  </div>
</div>

<div class="row rowSheet">
  <div class="col-12">
    <select-menu-type
      label="Ordering"
      [noShadow]="true"
      [menuType]="menuType"
      [schoolId]="student?.SchoolId"
    ></select-menu-type>
  </div>
</div>

<div *ngIf="this.menuType != 'Event'" class="row rowSheet">
  <div class="col-12">
    <select-date
      [noShadow]="true"
      label="For"
      [selectedDate]="date"
      [preOrderWeeksNumber]="schoolWeeksPreOrder"
    ></select-date>
  </div>
</div>

<div class="row no-gutters paddingLine sheetButton">
  <div class="col-12">
    <button type="button" class="PrimaryButton" (click)="CloseSheet()">Done</button>
  </div>
</div>
