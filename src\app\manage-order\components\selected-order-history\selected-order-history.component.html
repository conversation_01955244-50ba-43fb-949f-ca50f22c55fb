<div class="container-fluid">
  <nav-back-button (navBack)="GoBackClick()" [text]="titlePage"></nav-back-button>
</div>

<div class="container">
  <div class="row">
    <div class="col-12">
      <h4 class="StatusOrder">
        Status:
        <span
          [ngClass]="{
            completedOrder: !hasError && !isProcessing,
            processingOrder: isProcessing,
            errorOrder: hasError
          }"
          >{{ orderStatusDisplay }}</span
        >
      </h4>
    </div>
  </div>

  <div *ngFor="let item of listProducts" class="row itemRow">
    <div class="col-2 colQuantity">
      <span>{{ item.Quantity }} x</span>
    </div>
    <div class="col-10">
      <div class="row">
        <div class="col-12">
          <h6 class="itemName">{{ item.Name }}</h6>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <p>
            <span *ngFor="let option of item.SelectedOptions">
              {{ option.OptionName }}
              <strong class="spacerDescription">. </strong>
            </span>

            {{ [item] | calculateOrderItemsPrice }}
          </p>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="orderFee" class="row itemRow">
    <div class="col-2 colQuantity">
      <span></span>
    </div>
    <div class="col-10">
      <div class="row">
        <div class="col-12">
          <h6 class="itemName">Order fee</h6>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <p>{{ orderFee | currency }}</p>
        </div>
      </div>
    </div>
  </div>

  <div class="row totalRow">
    <div class="col-12">
      <h5>Total: {{ orderPrice | currency }}</h5>
    </div>
  </div>

  <div *ngIf="hasError">
    <div class="row justify-content-center">
      <div class="col-12">
        <p class="errorOrder">
          {{ errorMessage }}
        </p>
      </div>
    </div>
  </div>

  <div>
    <div class="row justify-content-center">
      <div *ngIf="canEditOrder()" class="col-12 col-md-6 col-lg-3 buttonCol">
        <primary-button text="Edit Order" (onPress)="ClickEditOrder()"></primary-button>
      </div>

      <div *ngIf="canTryAgainOrder()" class="col-12 col-md-6 col-lg-3 buttonCol">
        <button type="button" class="PrimaryButton smaller" (click)="ClickEditOrder()">Try again</button>
      </div>

      <div *ngIf="canCancelOrder()" class="col-12 col-md-6 col-lg-3 buttonCol">
        <button mat-button color="warn" class="WarnLink" (click)="ClickCancel()">Cancel Order</button>
      </div>
    </div>

    <div *ngIf="canOrderAgain()" class="row justify-content-center">
      <div class="col-12 col-md-6 col-lg-4 buttonCol">
        <primary-button text="Order Again" (onPress)="ClickOrderAgain()"></primary-button>
      </div>
    </div>
  </div>
</div>
