import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'orderErrorTableHeading',
  pure: true,
})
export class OrderErrorTableHeadingPipe implements PipeTransform {
  constructor() {}

  transform(rowAmount: number, selectedAmount: number) {
    if (!rowAmount) {
      return 'No order errors';
    }

    if (!selectedAmount) {
      return `${rowAmount} orders`;
    }

    return `${selectedAmount} selected of ${rowAmount} Orders`;
  }
}
