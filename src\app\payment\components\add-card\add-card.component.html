<form class="cashlessForm">
  <div class="row justify-content-center">
    <div class="col-12 col-md-10 col-lg-10">
      <mat-radio-group
        aria-label="Select an option"
        class="card-radio-group"
        (change)="SelectedChoiceChanged($event)"
        value="1"
      >
        <mat-radio-button class="card-radio-button" value="1">Debit Card</mat-radio-button>
        <mat-radio-button class="card-radio-button" value="2"
          >Credit Card (Mastercard, VISA)</mat-radio-button
        >
      </mat-radio-group>
    </div>
  </div>

  <div class="row justify-content-center">
    <div class="col-12 col-md-10 col-lg-10">
      <div class="textfield--float-label informationsPayment">
        <p *ngIf="selectedPaymentChoice == 2">
          <u>Important: </u>We recommend linking your debit card to avoid the possibility of your bank
          charging a Cash Advance fees for using a credit card
        </p>
      </div>
    </div>
  </div>

  <ng-container *ngIf="selectedPaymentChoice == 1 || selectedPaymentChoice == 2">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-10">
        <div class="textfield--float-label">
          <!-- Begin hosted fields section -->
          <label class="hosted-field--label" for="card-number">
            <span class="icon">
              <mat-icon>credit_card</mat-icon>
            </span>
            Card Number</label
          >
          <div id="card-number" class="hosted-field"></div>
          <!-- End hosted fields section -->
        </div>
      </div>
    </div>

    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-10">
        <div class="textfield--float-label">
          <!-- Begin hosted fields section -->
          <label class="hosted-field--label" for="expiration-date">
            <span class="icon">
              <mat-icon>date_range</mat-icon>
            </span>
            Expiration Date</label
          >
          <div id="expiration-date" class="hosted-field"></div>
          <!-- End hosted fields section -->
        </div>
      </div>
    </div>

    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-10">
        <div class="textfield--float-label cvvField">
          <!-- Begin hosted fields section -->
          <label class="hosted-field--label" for="cvv">
            <span class="icon">
              <mat-icon>lock</mat-icon>
            </span>
            cvv</label
          >
          <div id="cvv" class="hosted-field"></div>
          <!-- End hosted fields section -->
        </div>
      </div>
    </div>

    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-10">
        <mat-checkbox [(ngModel)]="bRemember" name="Remember" class="rememberCheck"
          >Remember this card</mat-checkbox
        >
      </div>
    </div>

    <div class="row justify-content-center">
      <div *ngIf="errorAddCard" class="col-12 col-md-10 col-lg-10">
        <mat-error>{{ errorAddCard }}</mat-error>
      </div>
    </div>

    <div class="row justify-content-center rowButton">
      <div class="col-12 col-md-10 col-lg-10">
        <button class="PrimaryButton" type="button" (click)="ConfirmClick()">
          {{ textSubmitButton }}
        </button>
      </div>

      <div class="col-12 col-md-10 col-lg-10">
        <button mat-flat-button class="SecondaryButton" type="button" (click)="CancelClick()">Cancel</button>
      </div>
    </div>
  </ng-container>
</form>
