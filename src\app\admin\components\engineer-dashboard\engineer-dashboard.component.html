<div class="container-fluid">
  <div class="row mt-2">
    <div class="col-3">
      <form [formGroup]="form">
        <input-date placeholder="Select date" formControlName="selectedDate"></input-date>
      </form>
    </div>
    <div class="col-1 offset-8">
      <basic-button-v2 (onPress)="RefreshDashboard()" text="Refresh" buttonStyle="primary"></basic-button-v2>
    </div>
  </div>
  <div class="row mt-4">
    <div class="col-12 col-md-4 col-lg-3 col-xl-2">
      <school-panel>
        <span class="mt-2">Total Orders</span>
        <p class="successNumber">{{ totalOrders }}</p>
      </school-panel>
    </div>
    <div class="col-12 col-md-4 col-lg-3 col-xl-2">
      <!-- <school-panel>
                <p>4 Events today</p>
            </school-panel> -->
    </div>
  </div>

  <div class="row mt-4">
    <div class="col-6">
      <school-panel title="Schools">
        <admin-dashboard-schools [ordersSchool]="ordersSchool"></admin-dashboard-schools>
      </school-panel>
    </div>
    <div class="col-3">
      <school-panel>
        <admin-dashboard-processing [orders]="ordersProcessing"></admin-dashboard-processing>
      </school-panel>
    </div>
    <div class="col-3">
      <school-panel>
        <admin-dashboard-errors [errors]="ordersWithError"></admin-dashboard-errors>
      </school-panel>
    </div>
  </div>
</div>
