<div class="announcements">
  <div
    *ngFor="let announcement of announcements"
    id="{{ announcement.NoticeId }}"
    class="announcementContainer"
  >
    <div class="title">
      <img src="assets/icons/horn.svg" />
      <p class="titleText">{{ announcement.Title }}</p>
    </div>
    <div *ngIf="announcement.Description" class="description">
      <p
        [ngClass]="{
          descriptionTextOpened: openedAnnouncements.includes(announcement.NoticeId),
          descriptionText: !openedAnnouncements.includes(announcement.NoticeId)
        }"
      >
        {{ announcement.Description }}
      </p>
      <a (click)="OnMoreInfoPress(announcement.NoticeId)">
        <p *ngIf="!openedAnnouncements.includes(announcement.NoticeId); else less" class="moreText">
          See more
        </p>
        <ng-template #less>
          <img class="topLink" src="assets/icons/arrow-top.svg" />
        </ng-template>
      </a>
    </div>
  </div>
</div>
