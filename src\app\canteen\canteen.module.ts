import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { NgxPrintModule } from 'ngx-print';

// google material
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';

import { CanteenRoutingModule } from './canteen-routing.module';
import { AccountModule } from '../account/account.module';
import { SharedModule } from '../shared/shared.module';
import { SharedToolsModule } from '../shared-tools/shared-tools.module';
import { SchoolsFormModule } from '../schools-form/schools-form.module';
import { SchoolsButtonModule } from '../schools-button/schools-button.module';

// Components
import {
  CanteenComponent,
  LabelPrintComponent,
  BlockPrintComponent,
  IosLabelsPrintingComponent,
  NoticeBoardComponent,
  AnnouncementsComponent,
  ArticlesComponent,
  CanteenOrderFilterComponent,
  CanteenOrderSchoolFilterComponent,
  CanteenOrderTypeFilterComponent,
  CanteenOrderCategoryFilterComponent,
  A4PrintFormComponent,
  NoticeHeaderComponent,
  NoticeTableComponent,
  UniformOrderStatusPickerComponent,
  SelectedOrderComponent,
  LabelTemplateComponent,
  LabelTemplateIosComponent,
} from './components';

@NgModule({
  declarations: [
    CanteenComponent,
    LabelPrintComponent,
    BlockPrintComponent,
    IosLabelsPrintingComponent,
    NoticeBoardComponent,
    AnnouncementsComponent,
    ArticlesComponent,
    CanteenOrderFilterComponent,
    CanteenOrderSchoolFilterComponent,
    CanteenOrderTypeFilterComponent,
    CanteenOrderCategoryFilterComponent,
    A4PrintFormComponent,
    NoticeHeaderComponent,
    NoticeHeaderComponent,
    NoticeTableComponent,
    UniformOrderStatusPickerComponent,
    SelectedOrderComponent,
    LabelTemplateComponent,
    LabelTemplateIosComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NgxPrintModule,
    CanteenRoutingModule,
    AccountModule,
    SharedModule,
    SharedToolsModule,
    SchoolsButtonModule,
    // material
    MatFormFieldModule,
    MatRadioModule,
    MatCheckboxModule,
    MatExpansionModule,
    MatSelectModule,
    MatTableModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
    MatSortModule,
    MatPaginatorModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatTooltipModule,
    MatMenuModule,
    SchoolsFormModule,
  ],
  exports: [CanteenOrderTypeFilterComponent, MatTooltipModule, CanteenOrderCategoryFilterComponent],
  providers: [DatePipe],
})
export class CanteenModule {}
