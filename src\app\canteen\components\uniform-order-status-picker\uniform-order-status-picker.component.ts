import { Component, Input, EventEmitter, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { DialogResultComponent } from 'src/app/shared/components';
import { OrderApiService, SpinnerService } from 'src/app/sharedServices';
import { BaseComponent, CanteenStatusEnum, Order, ResultDialogData } from '../../../sharedModels';

@Component({
  selector: 'uniform-order-status-picker',
  templateUrl: './uniform-order-status-picker.component.html',
  styleUrls: ['./uniform-order-status-picker.component.scss'],
})
export class UniformOrderStatusPickerComponent extends BaseComponent {
  @Input() selectedOrders: Order[];
  @Output() loadTableData = new EventEmitter();
  @Output() clearRowSelection = new EventEmitter();
  CanteenStatusEnum = CanteenStatusEnum;

  constructor(
    private spinnerService: SpinnerService,
    private orderAPIService: OrderApiService,
    public dialog: MatDialog
  ) {
    super();
  }

  showDialog(newStatus: CanteenStatusEnum): void {
    if (this.completedOrdersSelected()) {
      this.showWarningDialog();
      return;
    }
    this.showUpdateStatusDialog(newStatus);
  }

  completedOrdersSelected(): boolean {
    return this.selectedOrders.some((order: Order) => order.CanteenStatus === CanteenStatusEnum.Completed);
  }

  showWarningDialog(): void {
    const data = this.getWarningDialogData();

    this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });
  }

  showUpdateStatusDialog(newStatus: CanteenStatusEnum): void {
    const orderCountText =
      this.selectedOrders.length === 1 ? '1 order' : `${this.selectedOrders.length} orders`;
    const data = this.getUpdateDialogData(orderCountText, newStatus);

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.updateOrderStatus(newStatus);
      }
    });
  }

  updateOrderStatus(newStatus: CanteenStatusEnum): void {
    this.spinnerService.start();
    const selectedOrderIds = this.selectedOrders.map(el => el.OrderId);

    this.orderAPIService.UpdateListCanteenStatusOrder(selectedOrderIds, newStatus).subscribe({
      next: response => {
        // Give time for orders table in the replica db to update before refreshing orders
        setTimeout(() => {
          this.loadTableData.emit();
        }, 1000);

        this.clearRowSelection.emit();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  getWarningDialogData(): ResultDialogData {
    const data = new ResultDialogData();
    data.TitleLine1 = 'Completed orders cannot have their status updated';
    data.TextLine2 = 'Please unselect completed orders to continue';
    data.ConfirmButton = 'Ok';
    return data;
  }

  getUpdateDialogData(orderCount: string, newStatus: CanteenStatusEnum): ResultDialogData {
    const data = new ResultDialogData();
    data.TitleLine1 = 'Are you sure you want to update the order status? ';
    data.TextLine2 = this.getDialogMessage(orderCount, newStatus);
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, change status';
    return data;
  }

  getDialogMessage(orderCount: string, newStatus: CanteenStatusEnum): string {
    return newStatus === CanteenStatusEnum.Completed
      ? `Once an order's status is updated to "${CanteenStatusEnum.Completed}" the status cannot be changed again`
      : `${orderCount} will be updated to “${newStatus}”`;
  }
}
