import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { A4PrintSettings, A4PrintPositions } from 'src/app/sharedModels';
import { CanteenService } from 'src/app/sharedServices';

@Component({
  selector: 'a4-print-form',
  templateUrl: './a4-print-form.component.html',
  styleUrls: ['./a4-print-form.component.scss'],
})
export class A4PrintFormComponent implements OnInit {
  @Output() generate: EventEmitter<A4PrintPositions> = new EventEmitter();
  @Output() updateCustomValue: EventEmitter<boolean> = new EventEmitter();
  @Output() sliceLabels = new EventEmitter();
  @Output() updateDisplaySchool: EventEmitter<boolean> = new EventEmitter();
  form: FormGroup;
  printSettings = A4PrintSettings;

  constructor(private canteenService: CanteenService) {}

  ngOnInit(): void {
    this._createForm();
  }

  private _createForm() {
    let settings = 'default';
    this.updateCustomValue.emit(false);

    let savedSetting = this.canteenService.GetPrintingSetting();
    let schoolNameSetting = this.canteenService.GetSchoolNamesOnA4Labels() === 'true';
    this.updateDisplaySchool.emit(schoolNameSetting);

    if (savedSetting && savedSetting != settings) {
      settings = savedSetting;
      this.updateCustomValue.emit(false);
    }

    this.form = new FormGroup({
      line: new FormControl('1', [Validators.required]),
      column: new FormControl('1', [Validators.required]),
      settings: new FormControl(settings),
      displaySchoolName: new FormControl(schoolNameSetting),
    });

    this.sliceLabels.emit();

    this.line.valueChanges.subscribe(val => {
      this.generateLabels();
    });

    this.column.valueChanges.subscribe(val => {
      this.generateLabels();
    });

    this.settings.valueChanges.subscribe(val => {
      this.updateCustomValue.emit(Boolean(val === 'custom'));
      this.canteenService.SetPrintingSetting(val);
    });

    this.displaySchoolName.valueChanges.subscribe(val => {
      this.updateDisplaySchool.emit(val);
      this.canteenService.setSchoolNamesOnA4Labels(val);
    });
  }

  generateLabels() {
    const data = new A4PrintPositions();
    data.Line = this.line.value;
    data.Column = this.column.value;
    this.generate.emit(data);
  }

  get line() {
    return this.form.get('line');
  }

  get column() {
    return this.form.get('column');
  }

  get settings() {
    return this.form.get('settings');
  }

  get displaySchoolName() {
    return this.form.get('displaySchoolName');
  }
}
