import { Component, OnInit } from '@angular/core';
import { SPRIGGY_SCHOOLS_TERMS_LINK } from 'src/constants';
// Models
import { UserCashless } from '../../../sharedModels';
// Services
import { UserService } from '../../../sharedServices';

@Component({
  selector: 'app-account-close',
  templateUrl: './account-close.component.html',
  styleUrls: ['./account-close.component.scss'],
})
export class AccountCloseComponent implements OnInit {
  private user: UserCashless;
  userName: string;
  termsLink: string = SPRIGGY_SCHOOLS_TERMS_LINK;

  constructor(private userService: UserService) {}

  ngOnInit() {
    this.user = this.userService.GetUserConnected();

    if (!this.user) {
      this.user = new UserCashless();
    }

    this.userName = this.user.FirstName;
  }

  triggerIntercom() {
    this.userService.openIntercom();
  }
}
