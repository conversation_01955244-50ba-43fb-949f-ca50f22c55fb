import { Component, OnInit, Input, OnDestroy } from '@angular/core';

//ngrx
import { select, Store } from '@ngrx/store';
import { menuCategories } from 'src/app/states/canteen/canteen.selectors';
import { CanteenState } from 'src/app/states';

// Models
import {
  MenuItem,
  Option,
  CategoryEditor,
  AddRemoveOptionRequest,
  LinkedItemsComponent,
} from 'src/app/sharedModels';

// Service
import { SpinnerService, MenuEditorApiService } from 'src/app/sharedServices';

@Component({
  selector: 'editor-detail-option-items',
  templateUrl: './detail-option-items.component.html',
  styleUrls: ['./detail-option-items.component.scss'],
})
export class DetailOptionItemsComponent extends LinkedItemsComponent implements OnInit, OnDestroy {
  @Input() option: Option;
  @Input() canteenId: number;

  constructor(
    protected spinnerService: SpinnerService,
    protected menuEditorAPIService: MenuEditorApiService,
    protected store: Store<{ canteen: CanteenState }>
  ) {
    super(spinnerService, menuEditorAPIService, store);
  }

  ngOnInit() {
    this.canteenSubscription = this.store
      .pipe(select(menuCategories))
      .subscribe((menuCategories: CategoryEditor[]) => {
        this.listCategories = menuCategories;
        this.selectedCategory = this.listCategories[0];
        this._UpdateListItems();
        this.createForm();
      });

    this.getMenuItems(this.canteenId);
  }

  ngOnDestroy() {
    if (this.canteenSubscription) {
      this.canteenSubscription.unsubscribe();
    }
  }

  private _UpdateListItems() {
    this.spinnerService.start();

    this.menuEditorAPIService.GetItemsByOptionIdAPI(this.option.MenuItemOptionsCategoryId).subscribe({
      next: (response: MenuItem[]) => {
        this.linkedItems = response;

        this.filterItemsByCategory();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  addItem(item: MenuItem) {
    this.spinnerService.start();
    let request = this.getOptionRequest(item);

    this.menuEditorAPIService.AddOptionToItemAPI(request).subscribe({
      next: (response: any) => {
        this._UpdateListItems();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  removeItem(item: MenuItem) {
    this.spinnerService.start();
    let request = this.getOptionRequest(item);

    this.menuEditorAPIService.RemoveOptionFromItemAPI(request).subscribe({
      next: (response: any) => {
        this._UpdateListItems();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  getOptionRequest(item: MenuItem) {
    let request: AddRemoveOptionRequest = new AddRemoveOptionRequest();
    request.ItemId = item.MenuItemId;
    request.OptionId = this.option.MenuItemOptionsCategoryId;
    request.CanteenId = item.CanteenId;
    request.MenuId = item.MenuId;

    return request;
  }
}
