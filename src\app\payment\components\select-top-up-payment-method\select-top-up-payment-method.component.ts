import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'select-top-up-payment-method',
  templateUrl: './select-top-up-payment-method.component.html',
  styleUrls: ['./select-top-up-payment-method.component.scss'],
})
export class SelectTopUpPaymentMethodComponent {
  @Output() cardSelected = new EventEmitter<string>();
  @Output() confirmTopup = new EventEmitter();
  @Output() cancel = new EventEmitter();
  @Output() addCard = new EventEmitter();
  @Input() topUpAmount: number;
  @Input() isNestedTopUp: boolean;
  @Input() showTopUpButton: boolean;

  cardSelectedClick(event: string): void {
    this.cardSelected.emit(event);
  }

  addCardClick(): void {
    this.addCard.emit();
  }

  cancelClick(): void {
    this.cancel.emit();
  }

  confirmTopUpClick(): void {
    this.confirmTopup.emit();
  }
}
