import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  standalone: true,
  selector: '[twoDecimal]',
})
export class TwoDecimalDirective {
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g); //unlimited digit number with maximum of 2 decimal places
  private specialKeys: string[] = [
    'Backspace',
    'Tab',
    'End',
    'Home',
    'ArrowLeft',
    'ArrowRight',
    'Del',
    'Delete',
    'Enter',
  ];

  constructor(private el: ElementRef) {}

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent) {
    // Allow Backspace and any text editor keys
    if (this.specialKeys.indexOf(event.key) !== -1) {
      return;
    }
    const currentValueOfInput: string = this.el.nativeElement.value;
    const positionOfNewValue: number = this.el.nativeElement.selectionStart;

    const newValue: string = this.getNewInputValue(currentValueOfInput, positionOfNewValue, event.key);
    if (newValue && !String(newValue).match(this.regex)) {
      event.preventDefault();
    }
  }

  getNewInputValue(currentValueOfInput: string, positionOfNewValue: number, key: string): string {
    return [
      currentValueOfInput.slice(0, positionOfNewValue),
      key == 'Decimal' ? '.' : key,
      currentValueOfInput.slice(positionOfNewValue),
    ].join('');
  }
}
