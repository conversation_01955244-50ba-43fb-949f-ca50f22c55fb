import { KeyValue } from '@angular/common';
import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { BaseComponent, FoodBreakSettings, ListMenu, Menu, MenuTypeEnum } from 'src/app/sharedModels';
import { FeatureFlagService, MenuService, SpinnerService } from 'src/app/sharedServices';
import { CUSTOM_MENU_NAME_OPTIONS } from 'src/app/sharedServices/menu/menu-custom-name';
import { FeatureFlags } from 'src/constants';

@Component({
  selector: 'food-break-settings-form',
  templateUrl: './food-break-settings-form.component.html',
  styleUrls: ['./food-break-settings-form.component.scss'],
})
export class FoodBreakSettingsFormComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() schoolId: number;
  @Input() merchantId: number;
  menuList: Menu[] = [];
  selectedMenu: Menu = new Menu();
  form: FormGroup;
  customMenuNameList: KeyValue<string, string>[] = [];
  showCustomMenuPicker: boolean;
  customMenuNames: string[] = CUSTOM_MENU_NAME_OPTIONS;

  constructor(
    private spinnerService: SpinnerService,
    private menuService: MenuService,
    protected featureFlagService: FeatureFlagService
  ) {
    super();
  }

  ngOnInit(): void {
    this.featureFlagService.getFlag(FeatureFlags.showCustomMenuName, false).then(res => {
      this.showCustomMenuPicker = res;
    });

    this.customMenuNames.forEach((menuName: string, i: number) =>
      this.customMenuNameList.push({ key: i.toString(), value: menuName })
    );
    this._createForm();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.schoolId?.currentValue) {
      this.getMenus(this.schoolId);
    }
  }

  private _createForm(): void {
    const menuIsDisabled = !this.selectedMenu?.IsActive;

    this.form = new FormGroup({
      menuIsDisabled: new FormControl(menuIsDisabled),
      customMenuName: new FormControl(null),
      cutOffTime: new FormControl(null), // will be set via api result
      breakTime: new FormControl(null), // will be set via api result
    });
  }

  saveFoodBreakSettings(): void {
    const request = this.getFoodBreakSettingsRequest();

    this.selectedMenu.IsActive = !this.menuIsDisabled.value;
    this.spinnerService.start();

    this.menuService.UpdateFoodBreakSettingsAPI(request).subscribe({
      next: () => {
        this.selectedMenu.IsActive = request.IsActive;
        this.selectedMenu.FriendlyName = request.FriendlyName;
        this.selectedMenu.CutOffTime = new Date();
        this.selectedMenu.CutOffTime.setHours(parseInt(this.cutOffTime.value.split(':')[0]));
        this.selectedMenu.CutOffTime.setMinutes(parseInt(this.cutOffTime.value.split(':')[1]));
        this.selectedMenu.BreakTime = request.BreakTime;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  getFoodBreakSettingsRequest() {
    const customMenuName = this.findMenuNameKeyValueByKey(this.customMenuName.value);
    const request = new FoodBreakSettings();
    request.MenuId = this.selectedMenu.MenuId;
    request.CutOffTime = this.cutOffTime.value;
    request.BreakTime = this.breakTime.value;
    request.IsActive = !this.menuIsDisabled.value;
    request.FriendlyName = customMenuName.value;
    return request;
  }

  get menuIsDisabled() {
    return this.form.get('menuIsDisabled');
  }
  get customMenuName() {
    return this.form.get('customMenuName');
  }

  get cutOffTime() {
    return this.form.get('cutOffTime');
  }

  get breakTime() {
    return this.form.get('breakTime');
  }

  isStringValue(obj: any): boolean {
    return typeof obj === 'string';
  }

  setSelectedMenu(selectedMenu: Menu): void {
    this.selectedMenu = selectedMenu;
    const canteenCutOffTime = this.isStringValue(this.selectedMenu.CutOffTime)
      ? this.selectedMenu.CutOffTime.toString()
      : this.calculateCutOffTime(this.selectedMenu.CutOffTime);
    const breakTime = this.selectedMenu.BreakTime;

    this.breakTime.setValue(breakTime);
    this.cutOffTime.setValue(canteenCutOffTime);

    const friendlyMenuName = this.findMenuNameKeyValueByName(this.selectedMenu.FriendlyName);
    this.customMenuName.setValue(friendlyMenuName.key);
    this.menuIsDisabled.setValue(!this.selectedMenu.IsActive);
  }

  findMenuNameKeyValueByName(name: string): KeyValue<string, string> {
    return this.customMenuNameList.find(x => {
      return x.value === name;
    });
  }

  findMenuNameKeyValueByKey(key: string): KeyValue<string, string> {
    return this.customMenuNameList.find(x => {
      return x.key === key;
    });
  }

  calculateCutOffTime(cutOffTime: Date): string {
    const hours = cutOffTime.getHours().toString().padStart(2, '0');
    const minutes = cutOffTime.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  //triggered by new school
  getMenus(schoolId: number): void {
    this.menuService.GetConfiguredMenuBySchoolAPI(this.merchantId, schoolId).subscribe({
      next: menuListItems => {
        this.clearMenuList();
        this.menuList = menuListItems.Menus;
      },
      error: error => {
        this.handleErrorFromService(error);
      },
    });
  }

  clearMenuList(): void {
    this.menuList = [];
  }

  hasMenu(): boolean {
    return this.menuList?.length > 0;
  }
}
