<form [formGroup]="form">
  <div formArrayName="dates">
    <div *ngFor="let alias of dateFormArray.controls; let i = index" class="checkboxContainer">
      <div>
        <span [ngClass]="{ disabled: !dateList[i].OrderDateAvailable || !dateList[i].AllItemsAvailable }"
          >{{ dateList[i].Title }} ({{ menuName }})</span
        >
        <span
          *ngIf="!dateList[i].AllItemsAvailable && dateList[i].OrderDateAvailable"
          class="unavailableMessage"
          >One or more items not available</span
        >
      </div>
      <mat-checkbox [formControlName]="i"></mat-checkbox>
    </div>
  </div>
</form>
