<div class="row">
  <div class="col-12">
    <h2>Filters</h2>
  </div>
</div>

<div *ngFor="let filter of availableFilters" trackby:filter.code class="row rowSheet">
  <div class="col-12">
    <mat-checkbox [(ngModel)]="filter.selected">{{ filter.title }}</mat-checkbox>
  </div>
</div>

<div class="row no-gutters paddingLine sheetButton">
  <div class="col-12">
    <button mat-flat-button class="SecondaryButton" type="button" (click)="clearAllSelectedFilters()">
      Clear all
    </button>
    <button type="button" class="PrimaryButton" (click)="clickFilter()">Apply</button>
  </div>
</div>
