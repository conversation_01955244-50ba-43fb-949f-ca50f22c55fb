<div class="col-12">
  <div class="col-12 col-md-8 col-lg-4">
    <nav-back-button (navBack)="GoBackClick()" text="Go Back" class="backButton"></nav-back-button>

    <div class="titleWrapper">
      <img sizes="24" src="assets/icons/admin-settings.svg" />
      <h1 class="title">Admin Settings</h1>
    </div>

    <div *ngIf="canteenListVisible" class="schoolSelection">
      <canteen-select-list
        title="Select Canteen"
        (isVisible)="CanteenListVisibleChanged($event)"
        (selectedChanged)="onCanteenSelect($event)"
      ></canteen-select-list>
    </div>
  </div>

  <div class="titleDescriptionWrapper">
    <p class="tableTitle">Members</p>
  </div>

  <div class="col-12">
    <table *ngIf="isListLoaded" mat-table [dataSource]="users" class="mat-elevation-z8 tableau usersTable">
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef>Name</th>
        <td mat-cell *matCellDef="let user">
          {{ user.FirstName }} {{ user.Lastname }}
          <p class="adminLabel">{{ user.IsAdmin ? '(Admin)' : '' }}</p>
        </td>
      </ng-container>

      <ng-container matColumnDef="email">
        <th mat-header-cell *matHeaderCellDef class="emailHeader">Email</th>
        <td mat-cell *matCellDef="let user">{{ user.Email }}</td>
      </ng-container>

      <ng-container matColumnDef="menuEditor">
        <th mat-header-cell *matHeaderCellDef class="centerAlignment">View Menu Editor</th>
        <td mat-cell *matCellDef="let user">
          <div class="checkboxWrapper">
            <div *ngIf="user.UserId == currentUser.UserId; else notAdminMenu">
              <img
                *ngIf="user.IsMenuEditorAvailable"
                sizes="24"
                src="assets/icons/checkBox-disable.svg"
                class="checkBox"
              />
              <div *ngIf="!user.IsMenuEditorAvailable" class="inActiveCheckbox"></div>
            </div>
            <ng-template #notAdminMenu>
              <a (click)="onMarkClick(user, 'menuEditor')">
                <div *ngIf="!user.IsMenuEditorAvailable" class="inActiveCheckbox"></div>
                <img
                  *ngIf="user.IsMenuEditorAvailable"
                  sizes="24"
                  src="assets/icons/checkBox.svg"
                  class="checkBox"
                />
              </a>
            </ng-template>
          </div>
        </td>
      </ng-container>

      <ng-container matColumnDef="salesReport">
        <th mat-header-cell *matHeaderCellDef class="centerAlignment">View Sales Reports</th>
        <td mat-cell *matCellDef="let user">
          <div class="checkboxWrapper">
            <div *ngIf="user.UserId == currentUser.UserId; else notAdminSales">
              <img
                *ngIf="user.IsSaleReportsAvailable"
                sizes="24"
                src="assets/icons/checkBox-disable.svg"
                class="checkBox"
              />
              <div *ngIf="!user.IsSaleReportsAvailable" class="inActiveCheckbox"></div>
            </div>
            <ng-template #notAdminSales>
              <a (click)="onMarkClick(user, 'salesReport')">
                <div *ngIf="!user.IsSaleReportsAvailable" class="inActiveCheckbox"></div>
                <img
                  *ngIf="user.IsSaleReportsAvailable"
                  sizes="24"
                  src="assets/icons/checkBox.svg"
                  class="checkBox"
                />
              </a>
            </ng-template>
          </div>
        </td>
      </ng-container>

      <ng-container matColumnDef="viewEvent">
        <th mat-header-cell *matHeaderCellDef class="centerAlignment">View Event management</th>
        <td mat-cell *matCellDef="let user">
          <div class="checkboxWrapper">
            <div *ngIf="user.UserId == currentUser.UserId; else notViewEvent">
              <img
                *ngIf="user.IsEventManagementAvailable"
                sizes="24"
                src="assets/icons/checkBox-disable.svg"
                class="checkBox"
              />
              <div *ngIf="!user.IsEventManagementAvailable" class="inActiveCheckbox"></div>
            </div>
            <ng-template #notViewEvent>
              <a (click)="onMarkClick(user, 'viewEvent')">
                <div *ngIf="!user.IsEventManagementAvailable" class="inActiveCheckbox"></div>
                <img
                  *ngIf="user.IsEventManagementAvailable"
                  sizes="24"
                  src="assets/icons/checkBox.svg"
                  class="checkBox"
                />
              </a>
            </ng-template>
          </div>
        </td>
      </ng-container>

      <ng-container matColumnDef="notPrintedReport">
        <th mat-header-cell *matHeaderCellDef class="centerAlignment">Allow Unprinted Orders</th>
        <td mat-cell *matCellDef="let user">
          <div class="checkboxWrapper">
            <div *ngIf="user.UserId == currentUser.UserId; else notAdminAlert">
              <img
                *ngIf="user.IsOrdersNotPrintedReportsAvailable"
                sizes="24"
                src="assets/icons/checkBox-disable.svg"
                class="checkBox"
              />
              <div *ngIf="!user.IsOrdersNotPrintedReportsAvailable" class="inActiveCheckbox"></div>
            </div>
            <ng-template #notAdminAlert>
              <a (click)="onMarkClick(user, 'notPrintedReport')">
                <div *ngIf="!user.IsOrdersNotPrintedReportsAvailable" class="inActiveCheckbox"></div>
                <img
                  *ngIf="user.IsOrdersNotPrintedReportsAvailable"
                  sizes="24"
                  src="assets/icons/checkBox.svg"
                  class="checkBox"
                />
              </a>
            </ng-template>
          </div>
        </td>
      </ng-container>

      <ng-container matColumnDef="notPrintedAlert">
        <th mat-header-cell *matHeaderCellDef class="centerAlignment">Email Unprinted Orders</th>
        <td mat-cell *matCellDef="let user">
          <div class="checkboxWrapper">
            <div *ngIf="user.UserId == currentUser.UserId; else notAdminAlert">
              <img
                *ngIf="user.NotifyOrdersNotPrinted"
                sizes="24"
                src="assets/icons/checkBox-disable.svg"
                class="checkBox"
              />
              <div *ngIf="!user.NotifyOrdersNotPrinted" class="inActiveCheckbox"></div>
            </div>
            <ng-template #notAdminAlert>
              <a (click)="onMarkClick(user, 'notPrinted')">
                <div *ngIf="!user.NotifyOrdersNotPrinted" class="inActiveCheckbox"></div>
                <img
                  *ngIf="user.NotifyOrdersNotPrinted"
                  sizes="24"
                  src="assets/icons/checkBox.svg"
                  class="checkBox"
                />
              </a>
            </ng-template>
          </div>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
  </div>
</div>
