import { Component, OnInit, Inject } from '@angular/core';
import { Notice } from 'src/app/sharedModels/canteen/canteen-notice';
import { NoticeService, SpinnerService } from 'src/app/sharedServices';
import { Store, select } from '@ngrx/store';
import { FamilyState } from 'src/app/states';
import { selectedChild } from 'src/app/states/children/children.selectors';
import { UserCashless } from 'src/app/sharedModels';
import { Subscription } from 'rxjs';
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-modal-article',
  templateUrl: './modal-article.component.html',
  styleUrls: ['./articles.component.scss'],
})
export class ArticleModalComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: Notice,
    public dialogRef: MatDialogRef<ArticleModalComponent>
  ) {}

  onNoClick(): void {
    this.dialogRef.close();
    this.data = new Notice();
  }
}

@Component({
  selector: 'app-articles',
  templateUrl: './articles.component.html',
  styleUrls: ['./articles.component.scss'],
})
export class FamilyArticlesComponent implements OnInit {
  private selectedSubscription: Subscription;
  articles: Notice[] = [];

  constructor(
    private store: Store<{ family: FamilyState }>,
    private spinnerService: SpinnerService,
    private noticeService: NoticeService,
    public dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.selectedSubscription = this.store.pipe(select(selectedChild)).subscribe((child: UserCashless) => {
      if (child) {
        this.getArticles(child);
      }
    });
  }

  getArticles(child: UserCashless): void {
    this.spinnerService.start();
    this.noticeService.GetActiveSchoolNoticesAPI(child.SchoolId).subscribe({
      next: response => {
        let articles = [];
        if (response != null) {
          articles = response.filter(notice => notice.NoticeType == 'Article');
        }

        this.articles = articles;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
      },
    });
  }

  ngOnDestroy(): void {
    this.selectedSubscription?.unsubscribe();
  }

  onArticlePress(article: Notice): void {
    this.dialog.open(ArticleModalComponent, {
      data: article,
      width: '100%',
      maxWidth: '100vw',
      height: '100%',
      id: 'modalWindowArticle',
    });
  }
}
