import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SelectedOrderHistoryComponent } from './selected-order-history.component';

describe('SelectedOrderHistoryComponent', () => {
  let component: SelectedOrderHistoryComponent;
  let fixture: ComponentFixture<SelectedOrderHistoryComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SelectedOrderHistoryComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SelectedOrderHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
