<mat-accordion>
  <mat-expansion-panel [expanded]="rowExpanded">
    <mat-expansion-panel-header>
      <mat-panel-title> Categories </mat-panel-title>
      <mat-panel-description>
        <span>{{ selectedValueCategories.length }}/{{ categories.length }}</span>
      </mat-panel-description>
    </mat-expansion-panel-header>
    <div class="row">
      <div class="col-6 col-md-3" *ngFor="let cat of categories">
        <mat-checkbox
          [name]="cat.MenuCategoryId"
          [checked]="IsChecked(cat.MenuCategoryId)"
          (change)="CheckboxChanged($event)"
          >{{ cat.CategoryName }}</mat-checkbox
        >
      </div>
    </div>
    <div class="row">
      <div class="col-12 col-md-6">
        <div class="blockAction">
          <a (click)="Clear()" [ngClass]="{ active: !IsNoCategoriessSelected() }">Clear</a>
          <a (click)="SelectAll()" [ngClass]="{ active: !IsAllSelected() }">Select All</a>
        </div>
      </div>
    </div>
  </mat-expansion-panel>
</mat-accordion>
