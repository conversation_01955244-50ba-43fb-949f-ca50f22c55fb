import { KeyValue } from '@angular/common';
import { Component, Input, OnInit, SimpleChanges, EventEmitter, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

//Models
import { Days, School, DietaryFiltersShortForm, DietaryFiltersLongForm } from 'src/app/sharedModels';
import { getDietaryKeyValue } from 'src/app/sharedModels/base/KeyValueConversion';

@Component({
  selector: 'dietary-labels-form',
  templateUrl: './dietary-labels-form.component.html',
  styleUrls: ['./dietary-labels-form.component.scss'],
})
export class DietaryLabelsFormComponent implements OnInit {
  @Input() school: School;
  @Output() updateSchool: EventEmitter<School> = new EventEmitter();
  form: FormGroup;
  daysHelper: Days;
  dietaryFilters = DietaryFiltersLongForm;
  checkBoxValues: KeyValue<string, string>[] = [];

  constructor() {}

  ngOnInit(): void {
    this.checkBoxValues = getDietaryKeyValue();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.school?.currentValue) {
      if (this.school.Name) {
        this._createForm();
      }
    }
  }

  getLabelValue(dietaryLabel: string) {
    if (!this.school.DeactivatedFilters) {
      return true;
    }
    return !this.school?.DeactivatedFilters.includes(dietaryLabel);
  }

  private _createForm() {
    this.form = new FormGroup({
      Vegetarian: new FormControl(this.getLabelValue(`${DietaryFiltersShortForm['Vegetarian']},`)),
      Vegan: new FormControl(this.getLabelValue(DietaryFiltersShortForm['Vegan'])),
      GlutenFree: new FormControl(this.getLabelValue(DietaryFiltersShortForm['GlutenFree'])),
      Halal: new FormControl(this.getLabelValue(DietaryFiltersShortForm['Halal'])),
      LactoseFree: new FormControl(this.getLabelValue(DietaryFiltersShortForm['LactoseFree'])),
      NutsFree: new FormControl(this.getLabelValue(DietaryFiltersShortForm['NutsFree'])),
      FastingFriendly: new FormControl(this.getLabelValue(DietaryFiltersShortForm['FastingFriendly'])),
      DairyFree: new FormControl(this.getLabelValue(DietaryFiltersShortForm['DairyFree'])),
    });
  }

  submitForm() {
    let deactivatedFilters = '';
    Object.keys(this.form.controls).forEach(key => {
      if (!this.form.get(key).value) {
        deactivatedFilters += `${DietaryFiltersShortForm[key]},`;
      }
    });

    const data = { ...this.school, DeactivatedFilters: deactivatedFilters };
    this.updateSchool.emit(data);
  }
}
