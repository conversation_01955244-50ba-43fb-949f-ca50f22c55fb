import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';

// models
import { BillingHistory, BaseComponent, TopUp } from 'src/app/sharedModels';

// services
import { UserService, BillingApiService } from 'src/app/sharedServices';

@Component({
  selector: 'app-billing-history',
  templateUrl: './billing-history.component.html',
  styleUrls: ['../account-topup/account-topup.component.scss', './billing-history.component.scss'],
})
export class BillingHistoryComponent extends BaseComponent implements OnInit {
  listBilling: BillingHistory[] = [];
  listTopUp: TopUp[] = [];
  listCredit: TopUp[] = [];
  hasTopUp: boolean = false;
  hasBilling: boolean = false;
  hasCredit: boolean = false;

  constructor(
    private _location: Location,
    private userService: UserService,
    private billingAPIService: BillingApiService
  ) {
    super();
  }

  ngOnInit() {
    let user = this.userService.GetUserConnected();

    if (user) {
      // top up history
      this.billingAPIService.GetTopUpHistory(user.UserId).subscribe({
        next: (res: TopUp[]) => {
          this.listTopUp = res;

          if (res && res.length > 0) {
            this.hasTopUp = true;
          }
        },
        error: error => {
          this.handleErrorFromService(error);
        },
      });

      // credit history
      this.billingAPIService.GetCreditHistory(user.UserId).subscribe({
        next: (res: TopUp[]) => {
          this.listCredit = res;

          if (res && res.length > 0) {
            this.hasCredit = true;
          }
        },
        error: error => {
          this.handleErrorFromService(error);
        },
      });

      // billing history
      this.billingAPIService.GetBillingHistory(user.UserId).subscribe({
        next: (res: BillingHistory[]) => {
          this.listBilling = res;

          if (res && res.length > 0) {
            this.hasBilling = true;
          }
        },
        error: error => {
          this.handleErrorFromService(error);
        },
      });
    }
  }

  backClicked() {
    this._location.back();
  }
}
