<form *ngIf="form" [formGroup]="form" class="form">
  <div class="picker mr-2">
    <input-select-list
      formControlName="menuOption"
      [placeholder]="placeholder"
      [values]="values"
    ></input-select-list>
  </div>
  <basic-button
    *ngIf="initialVal != menuOption.value"
    text="Save"
    [buttonStyle]="0"
    (click)="saveFeeCalculator()"
    [disabled]="form.invalid"
    class="mr-2"
  ></basic-button>
</form>
