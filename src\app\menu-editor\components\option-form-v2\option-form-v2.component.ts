import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule, Location } from '@angular/common';

// Services
import { SpinnerService, MenuEditorApiService } from 'src/app/sharedServices';

// Models
import { BaseComponent, Option } from 'src/app/sharedModels';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { MenuEditorModule } from '../../menu-editor.module';
import { SchoolsButtonModule } from "../../../schools-button/schools-button.module";

@Component({
  selector: 'option-form-v2',
  templateUrl: './option-form-v2.component.html',
  styleUrls: ['./option-form-v2.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    FormsModule,
    MatInputModule,
    MatCheckboxModule,
    MenuEditorModule,
    SchoolsButtonModule
]
})
export class OptionFormV2Component extends BaseComponent implements OnInit {
  @Input() option: Option;
  @Output() optionChange = new EventEmitter();

  selectionRules = [
    {
      id: 0,
      title: 'Required - 1 selection',
      description: 'Must select 1 option',
    },
    {
      id: 1,
      title: 'Required - Multiple selections ',
      description: 'Must select 1 or more options',
    },
    {
      id: 2,
      title: 'Optional - 1 selection',
      description: 'Can only select 1 option',
    },
    {
      id: 3,
      title: 'Optional - Multiple selections',
      description: 'Can select 1 or more options',
    },
  ];
  selectedRuleIndex: number = null;

  constructor(
    private spinnerService: SpinnerService,
    private menuEditorAPIService: MenuEditorApiService,
    public dialog: MatDialog,
    private _location: Location
  ) {
    super();
  }

  onRuleChange(index) {
    if (this.selectedRuleIndex === index) {
      this.selectedRuleIndex = null;
    } else {
      this.selectedRuleIndex = index;
    }
  }

  ngOnInit() {
    if (this.option.IsRequired == true && this.option.IsOnlyOne == true) {
      this.selectedRuleIndex = 0;
    }
    if (this.option.IsRequired == true && this.option.IsOnlyOne == false) {
      this.selectedRuleIndex = 1;
    }
    if (this.option.IsRequired == false && this.option.IsOnlyOne == true) {
      this.selectedRuleIndex = 2;
    }
    if (this.option.IsRequired == false && this.option.IsOnlyOne == false) {
      this.selectedRuleIndex = 3;
    }
  }

  GetTextButton(): string {
    return this.option.MenuItemOptionsCategoryId > 0 ? 'Save' : 'Add';
  }

  SubmitForm() {
    this.spinnerService.start();

    this.option.IsRequired = this.selectedRuleIndex === 0 || this.selectedRuleIndex === 1 ? true : false;
    this.option.IsOnlyOne = this.selectedRuleIndex === 0 || this.selectedRuleIndex === 2 ? true : false;

    this.menuEditorAPIService.UpsertOptionAPI(this.option).subscribe({
      next: (response: Option) => {
        // success
        this.option = response;
        this.optionChange.emit(this.option);
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

}
