<editor-nav></editor-nav>

<div class="container-fluid">
  <div class="row">
    <!-- <div class="col-12 col-sm-1">
              <h4>Canteen</h4>
          </div> -->
    <div class="col-12 col-sm-3">
      <canteen-select-list (selectedChanged)="CanteenChanged($event)"></canteen-select-list>
    </div>
  </div>

  <div class="row">
    <div class="col-2 offset-md-2">
      <button class="PrimaryButton addButton" type="button" [routerLink]="['../option/add']">
        Add Option
      </button>
    </div>
  </div>

  <div class="row">
    <div class="col-8 offset-md-2">
      <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 tableau eTable">
        <!-- Image Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef>Id</th>
          <td mat-cell *matCellDef="let element">{{ element.MenuItemOptionsCategoryId }}</td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Option</th>
          <td mat-cell *matCellDef="let element">{{ element.Name }}</td>
        </ng-container>

        <!-- Price Column -->
        <ng-container matColumnDef="choices">
          <th mat-header-cell *matHeaderCellDef>Choices</th>
          <td mat-cell *matCellDef="let element">
            <option-chip-list [optionList]="element.SubOptions" />
          </td>
        </ng-container>

        <!-- location Column -->
        <ng-container matColumnDef="IsActive">
          <th mat-header-cell *matHeaderCellDef>Visibility</th>
          <td mat-cell *matCellDef="let element">
            <mat-checkbox [checked]="element.IsActive" [disabled]="true"></mat-checkbox>
          </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let element">
            <mat-icon
              matTooltip="Edit"
              [routerLink]="['../option', element.MenuItemOptionsCategoryId]"
              class="actionTableau"
              >edit</mat-icon
            >
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </div>
  </div>
</div>
