<div class="container-fluid">
  <div class="wrapperContainer">
    <ng-container *ngIf="!deactivatedUser">
      <div class="row">
        <p class="accountTitle">Balance</p>
      </div>

      <div class="row cardDefaultParent cardBalance">
        <p class="col-12 col-sm-9 balanceAmount align-items-center">
          ${{ accountBalance | number : '1.2-2' }}
        </p>

        <div class="col-12 col-sm-3">
          <primary-button text="Top up" (onPress)="TopUp()" id="top-up-flow-button"></primary-button>
        </div>
      </div>

      <div class="row">
        <div class="col-12">
          <p class="accountTitle">Payment Method</p>
        </div>
      </div>

      <div class="row cardDefaultParent">
        <div class="col-12">
          <payment-list-payment-methods
            [showDelete]="true"
            [canSelect]="false"
          ></payment-list-payment-methods>
        </div>
      </div>
    </ng-container>

    <div *ngIf="deactivatedUser" class="row">
      <div class="col-12">
        <app-warning
          title="Your account is deactivated"
          description="Please get in touch with us if you would like to reopen your account."
        ></app-warning>
      </div>
    </div>

    <!-- Settings -->
    <div class="row">
      <div class="col-12">
        <p class="accountTitle">Settings</p>
      </div>
    </div>
    <div class="row cardDefaultParent">
      <button class="col-12 listItem top" [routerLink]="['profile']">
        <img class="listItemImage text-center" src="assets/icons/parentconfig.svg" />
        <span class="listItemText text-center">Profile</span>
      </button>

      <button class="col-12 listItem single" [routerLink]="['billing-history']">
        <img class="listItemImage billing text-center" height="30" src="assets/icons/attach_money.svg" />
        <span class="listItemText text-center">Billing History</span>
      </button>

      <button class="col-12 listItem single" [routerLink]="['account-help']">
        <img class="listItemImage text-center" height="30" src="assets/icons/help-icon.svg" />
        <span class="listItemText text-center">Help</span>
      </button>

      <button class="col-12 listItem single" (click)="SignOut()">
        <img class="listItemImage text-center" src="assets/icons/logout.svg" />
        <span class="listItemText text-center">Log Out</span>
      </button>
    </div>
    <div class="align-items-center">
      <a target="_blank" [href]="termsLink">Terms of Service</a>
      -
      <a target="_blank" [href]="termsLink">Privacy Policy</a>
      -
      <a target="_blank" href="https://spriggyprodstorage.blob.core.windows.net/docs/cashlesslicence.html"
        >Software Licences</a
      >
    </div>

    <div *ngIf="isDesktop" class="align-items-center">
      <p class="appLinks">
        Check out our mobile apps!
        <a target="_blank" href="https://apps.apple.com/us/app/cashless/id1474589312?ls=1">iOS</a>
        <a target="_blank" href="https://play.google.com/store/apps/details?id=com.cashless.cashlessapp"
          >Android</a
        >
      </p>
    </div>
  </div>
</div>
