<mat-accordion *ngIf="createOrderSummary">
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title>
        <h4>
          Orders: (<span class="subHeader">{{ createOrderSummary.totalAmount | currency }}</span
          >)
        </h4>
      </mat-panel-title>
      <mat-panel-description> </mat-panel-description>
    </mat-expansion-panel-header>

    <ul class="detailList">
      <li *ngFor="let order of createOrderSummary.createOrdersInfo">
        {{ order.studentName }} - {{ order.menuFriendlyName }} - {{ order.orderDate | date : 'EE dd/LL' }}:
        <span class="subHeader">{{ order.price | currency }}</span>
      </li>
    </ul>
  </mat-expansion-panel>

  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title>
        <h4>
          Order Fees: (
          <span class="subHeader">{{ totalFees | currency }}</span>
          )
        </h4>
      </mat-panel-title>
    </mat-expansion-panel-header>

    <ul class="detailList">
      <li *ngFor="let fee of feesGroupedByStudent">
        Order Fee ({{ fee.name }})
        <span class="subHeader">+{{ fee.fee | currency }}</span>
      </li>
    </ul>

    <ng-template #perOrderModel>
      <p class="feeInformationLink">A simple transaction fee applies on each order per child.</p>
    </ng-template>
  </mat-expansion-panel>
</mat-accordion>

<h4 class="totalOrder">
  Total:
  <span class="subHeader">{{ createOrderSummary.totalAmount + totalFees | currency }}</span>
</h4>
