import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EventDetailBlockComponent } from '../event-detail-block/event-detail-block.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent, EventItem, EventView, ResultDialogData, Roles, SchoolEvent } from 'src/app/sharedModels';
import { ActivatedRoute, Router } from '@angular/router';
import { DialogResultV2Component } from 'src/app/shared/components';
import { MatDialog } from '@angular/material/dialog';
import { FeatureFlagService, SchoolEventManagerService, SpinnerService, UserService } from 'src/app/sharedServices';
import { EditEventDialog } from '../edit-event-dialog/edit-event-dialog.component';
import { EventItemsComponent } from '../event-items/event-items.component';
import { EventImageComponent } from '../event-image/event-image.component';
import { SchoolsButtonModule } from "../../../schools-button/schools-button.module";
import { ActiveIndicatorComponent } from "../active-indicator/active-indicator.component";
import * as moment from 'moment';
import { FeatureFlags } from 'src/constants';

@Component({
  selector: 'app-view-event-page',
  standalone: true,
  imports: [CommonModule, EventDetailBlockComponent, SharedModule, EditEventDialog, EventItemsComponent, EventImageComponent, SchoolsButtonModule, ActiveIndicatorComponent],
  templateUrl: './view-event-page.component.html',
  styleUrls: ['./view-event-page.component.scss'],
  // changeDetection: ChangeDetectionStrategy.OnPush, //TODO: bring back
})
export class ViewEventPageComponent extends BaseComponent implements OnInit {
  schoolEvent: SchoolEvent;
  items: EventItem[];
  private isAdmin: boolean = false;
  private publishEventSendComms : boolean = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    public dialog: MatDialog,
    private schoolEventManagerService: SchoolEventManagerService,
    private userService: UserService,
    private spinnerService: SpinnerService,
    private featureFlagService: FeatureFlagService
  ) {
    super();
  }

  ngOnInit(): void {
    //get data from resolver
    this.route.data.subscribe(data => {
      let resolverData: EventView = data['eventData'];
      this.schoolEvent = resolverData.event;
      this.items = resolverData.items;
    });

    const user = this.userService.GetUserConnected();
    this.isAdmin = user.Role == Roles.Admin;

    this.featureFlagService.getFlag(FeatureFlags.publishEventSendComms, false).then(res => {
      this.publishEventSendComms = res;
    });
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route }); 
  }

  editEventPrompt(): void {
    const dialogRef = this.dialog.open(EditEventDialog, {
      width: '900px',
      disableClose: true,
      data: this.schoolEvent,
    });

    dialogRef.afterClosed().subscribe((result: SchoolEvent) => {
      if (result) {
        const updatedEvent = this.getUpdatedSchoolEvent(result);
        this.editEvent(updatedEvent);
      }
    });
  }

  PublishEvent(forceCancel: boolean = false): void {

    if(this.publishEventSendComms && this.schoolEvent.IsActive == false)
    {
      // show popup confirm
      this.ShowPublishWithCommPopUp();

    }else{
      this.TriggerPublishCall(forceCancel);
    }
  }

  private TriggerPublishCall(forceCancel: boolean = false){
    this.spinnerService.start();
    this.schoolEventManagerService.PublishSchoolEvent(this.schoolEvent.SchoolEventId, forceCancel).subscribe({
      next: (res: number) => {
        var copyEvent = {...this.schoolEvent}
        copyEvent.IsActive = !copyEvent.IsActive;
        this.schoolEvent = copyEvent;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();

        if(error && error.error && !forceCancel){
          if(Array.isArray(error.error)){

            let message: string = error.error[0];

            if(message.indexOf("order") > -1){

              if(this.isAdmin){
                this.showConfirmpublishModalAdmin(message);
              }else{
                this.showConfirmpublishModal(message);
              }
            }

          }
        }else{
          this.handleErrorFromService(error);
        }
      },
    });
  }

  ShowPublishWithCommPopUp(){

    let data = new ResultDialogData();
    data.TitleLine1 = 'Notification';
    data.TextLine1 = 'Would you like to notify parents about the new event?';
    data.TextLine2 = 'If yes, a notification will be sent automatically';
    data.CancelButton = 'No';
    data.ConfirmButton = 'Yes';

    const dialogRef = this.dialog.open(DialogResultV2Component, {
      width: '500px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe((result: boolean) => {
      if (!result) {
        this.spinnerService.start();

        this.schoolEventManagerService.PublishSchoolEventWithNotification(this.schoolEvent.SchoolEventId, true).subscribe({
          next: (res: number) => {
            var copyEvent = {...this.schoolEvent}
            copyEvent.IsActive = !copyEvent.IsActive;
            this.schoolEvent = copyEvent;
            this.spinnerService.stop();
          },
          error: error => {
            this.spinnerService.stop();
            this.handleErrorFromService(error);   
          },
        });

      }else{
        this.TriggerPublishCall();
      }
    });


  }

  showConfirmpublishModalAdmin(message: string) {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Are you sure?';
    data.TextLine1 = `${message}.`;
    data.TextLine2 = 'Unpublishing this event will automatically cancel and refund all the order(s).';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, cancel all orders';

    const dialogRef = this.dialog.open(DialogResultV2Component, {
      width: '500px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe((result: boolean) => {
      if (!result) {
        // unpublishing + cancel orders
       this.PublishEvent(true);
      }
    });
  }

  getTextPublishButton(){
    if(!this.schoolEvent.IsActive){
      return 'Publish event';
    }else{

      let dateToCheck = moment(this.schoolEvent.EventDate); 
      let today = moment().format();

      if (dateToCheck.isBefore(today)) {
          return 'Deactivate event';
      } else {
          return 'Cancel event';
      }
    }
  }

  showConfirmpublishModal(message: string) {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Are you sure?';
    data.TextLine1 = `${message}.`;
    data.TextLine2 = 'Unpublishing this event will automatically cancel and refund all the order(s). Please contact a CSM member to confirm this action';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Ok';

    const dialogRef = this.dialog.open(DialogResultV2Component, {
      width: '500px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe((result: boolean) => {
      if (!result) {
        // unpublishing + cancel orders
       //this.PublishEvent(true);
      }
    });
  }

  getUpdatedSchoolEvent(updatedSchoolEvent: SchoolEvent): SchoolEvent {
    updatedSchoolEvent.SchoolEventId = this.schoolEvent.SchoolEventId;
    updatedSchoolEvent.MerchantId = this.schoolEvent.MerchantId;
    updatedSchoolEvent.SchoolId = this.schoolEvent.SchoolId;
    updatedSchoolEvent.MenuId = this.schoolEvent.MenuId;
    updatedSchoolEvent.EventTemplateId = this.schoolEvent.EventTemplateId;
    return updatedSchoolEvent;
  }

  editEvent(updatedSchoolEvent: SchoolEvent): void {
    this.spinnerService.start();

    this.schoolEventManagerService.UpdateSchoolEvent(updatedSchoolEvent).subscribe({
      next: (res: SchoolEvent) => {
        this.schoolEvent = res;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  deleteEventPrompt(): void {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Are you sure?';
    data.TextLine1 = 'Are you sure you want to delete this event:';
    data.TextLine2 = `'${this.schoolEvent.Name}'?`;
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, delete';

    const dialogRef = this.dialog.open(DialogResultV2Component, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe((cancelResult: boolean) => {
      if (!cancelResult) {
        this.deleteEvent();
      }
    });
  }

  deleteEvent(): void {
    this.spinnerService.start();

    this.schoolEventManagerService.ArchiveSchoolEvent(this.schoolEvent.SchoolEventId).subscribe({
      next: res => {
        this.goBack();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }
}
