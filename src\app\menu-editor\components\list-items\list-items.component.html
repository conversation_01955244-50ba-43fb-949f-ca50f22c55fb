<editor-nav></editor-nav>

<div class="container-fluid">
  <!-- filter section -->
  <form>
    <div class="row pt-4">
      <div [ngClass]="{ notVisible: listCanteens.length <= 1 }" class="col-12 col-sm-4">
        <canteen-select-list (selectedChanged)="CanteenChanged($event)"></canteen-select-list>
      </div>
      <ng-container *ngIf="form" [formGroup]="form">
        <div class="col-12 col-sm-4">
          <mat-form-field *ngIf="listMenus" appearance="outline">
            <mat-label>Menu</mat-label>
            <mat-select formControlName="menu">
              <mat-option *ngFor="let menu of listMenus" [value]="menu">{{
                menu | merchantMenuName
              }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-12 col-sm-4">
          <mat-form-field *ngIf="listCategories" appearance="outline">
            <mat-label>Category</mat-label>
            <mat-select formControlName="category">
              <mat-option *ngFor="let category of listCategories" [value]="category.MenuCategoryId">{{
                category.CategoryName
              }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </ng-container>
    </div>
  </form>

  <!-- Add Item Button -->
  <div class="row">
    <div class="col-2">
      <button class="PrimaryButton addButton" type="button" [routerLink]="['../item/add']">Add Item</button>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 tableau eTable">
        <!-- Image Column -->
        <ng-container matColumnDef="image">
          <th mat-header-cell *matHeaderCellDef>Image</th>
          <td mat-cell *matCellDef="let element"></td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Item</th>
          <td mat-cell *matCellDef="let element">{{ element.Name }}</td>
        </ng-container>

        <!-- Price Column -->
        <ng-container matColumnDef="price">
          <th mat-header-cell *matHeaderCellDef>Price</th>
          <td mat-cell *matCellDef="let element">${{ element.Price | number : '1.2-2' }}</td>
        </ng-container>

        <!-- location Column -->
        <ng-container matColumnDef="IsActive">
          <th mat-header-cell *matHeaderCellDef>Visibility</th>
          <td mat-cell *matCellDef="let element">
            <mat-checkbox [checked]="element.IsActive" [disabled]="true"></mat-checkbox>
          </td>
        </ng-container>

        <ng-container matColumnDef="Menus">
          <th mat-header-cell *matHeaderCellDef>Menus</th>
          <td mat-cell *matCellDef="let element">
            <!-- <mat-chip-list aria-label="List Menus"> -->
            <!-- class="menuChip" -->
            <div *ngFor="let menu of GetMenusToDisplay(element).menusDisplay; index as i">
              <p>{{ menu.SchoolName }} - {{ menu.MenuName | merchantMenuName }}</p>
            </div>
            <p *ngIf="GetMenusToDisplay(element).length > 5">+ more</p>
            <!-- </mat-chip-list> -->
          </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let element">
            <mat-icon matTooltip="Edit" [routerLink]="['../item', element.MenuItemId]" class="actionTableau"
              >edit</mat-icon
            >
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns" class="eRow"></tr>
      </table>
    </div>
  </div>
</div>
