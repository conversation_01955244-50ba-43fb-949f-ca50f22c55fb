import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';

// models
import { BaseMenuDateFilterComponent } from '../base-filter-menu-date.component';
import { FamilyState } from 'src/app/states';
import { Store } from '@ngrx/store';

@Component({
  selector: 'order-filters',
  templateUrl: './filters.component.html',
  styleUrls: ['./filters.component.scss'],
})
export class FiltersComponent extends BaseMenuDateFilterComponent implements OnInit, OnDestroy {
  constructor(protected store: Store<{ family: FamilyState }>) {
    super(store);
  }

  ngOnInit() {
    this.OnInitFunction();
  }

  ngOnDestroy() {
    this.OnDestroyFunction();
  }

  showOrderFilters() {
    const notCanteenOrder = !(this.isUniformOrder || this.isEventOrder);
    return !this.isEdit && notCanteenOrder;
  }
}
