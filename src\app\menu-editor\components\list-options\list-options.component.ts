import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';

// Models
import { School, Canteen, ListOptionsResponse, BaseComponent, Option } from 'src/app/sharedModels';

// Services
import { MenuEditorApiService, SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'app-list-options',
  templateUrl: './list-options.component.html',
  styleUrls: ['../editor-table-page.scss'],
})
export class ListOptionsComponent extends BaseComponent implements OnInit {
  listSchools: School[] = [];
  listOptions: Option[] = [];
  form: FormGroup;
  selectedCanteen: Canteen;

  // table
  displayedColumns: string[] = ['id', 'name', 'choices', 'IsActive', 'actions'];
  dataSource = new MatTableDataSource<Option>();

  defaultValueSchool: number;

  constructor(private menuEditorAPIService: MenuEditorApiService, private spinnerService: SpinnerService) {
    super();
  }

  ngOnInit() {}

  private _GetListOptions() {
    this.spinnerService.start();
    this.menuEditorAPIService.GetOptionsAPI(this.selectedCanteen.CanteenId).subscribe({
      next: (response: ListOptionsResponse) => {
        this.RefreshTable(response.Options);
        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.RefreshTable(null);
        this.spinnerService.stop();
      },
    });
  }

  private RefreshTable(listOption: Option[]) {
    this.listOptions = listOption;
    this.dataSource.data = this.listOptions;
  }

  CanteenChanged(canteen: Canteen) {
    this.selectedCanteen = canteen;
    this._GetListOptions();
  }
}
