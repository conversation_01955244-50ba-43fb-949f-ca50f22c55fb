import { Component, OnInit, Inject } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { SchoolClass, BaseComponent, ConfirmModal } from '../../../sharedModels';
import { SchoolClassesService, SpinnerService } from '../../../sharedServices';
import { DialogConfirmComponent } from 'src/app/shared/components/dialog-confirm/dialog-confirm.component';

@Component({
  selector: 'canteen-add-school-class',
  templateUrl: './add-school-class.component.html',
  styleUrls: ['./add-school-class.component.scss'],
})
export class AddSchoolClassComponent extends BaseComponent implements OnInit {
  form: FormGroup;
  loading = false;

  constructor(
    private schoolClassService: SchoolClassesService,
    private spinnerService: SpinnerService,
    public dialogRef: MatDialogRef<DialogConfirmComponent>,
    @Inject(MAT_DIALOG_DATA) public initSchoolData: SchoolClass,
    public dialog: MatDialog
  ) {
    super();
  }

  ngOnInit() {
    this.CreateForm(this.initSchoolData);
  }

  ////////////////////////////////////////
  // Form
  ////////////////////////////////////////
  get name() {
    return this.form.get('name');
  }

  getErrorMessageName() {
    return this.form.get('name').hasError('required') ? 'You must enter a value' : '';
  }

  CreateForm(schoolClass: SchoolClass): void {

    this.form = new FormGroup({
      id: new FormControl(schoolClass.ClassId),
      schoolId: new FormControl(schoolClass.SchoolId),
      name: new FormControl(schoolClass.Name, [Validators.required]),
      teacher: new FormControl(schoolClass.Teacher || ''),
      sortOrder: new FormControl(schoolClass.SortOrder),
    });
  }

  ////////////////////////////////////////
  // UPSERT class
  ////////////////////////////////////////
  onSubmit(): void {
    this.loading = true;

    this.initSchoolData.Name = this.form.get('name').value;
    this.initSchoolData.Teacher = this.form.get('teacher').value;
    this.initSchoolData.SortOrder = this.form.get('sortOrder').value;

    if (this.initSchoolData.ClassId) {
      this.updateClass(this.initSchoolData);
    } else {
      this.addClass(this.initSchoolData);
    }
  }

  addClass(data: SchoolClass): void {
    this.schoolClassService.CreateClassApi(data).subscribe({
      next: (response: SchoolClass) => {
        this.apiCallSuccess(response);
      },
      error: error => {
        this.apiCallError(false, error);
      },
    });
  }

  updateClass(data: SchoolClass): void {
    this.schoolClassService.UpdateClassApi(data).subscribe({
      next: (response: SchoolClass) => {
        this.apiCallSuccess(response);
      },
      error: error => {
        this.apiCallError(true, error);
      },
    });
  }

  apiCallSuccess(response: SchoolClass): void {
    this.dialogRef.close(response);
    this.spinnerService.stop();
    this.loading = false;
  }

  apiCallError(editClass: boolean, error): void {
    this.showErrorDialog(editClass);
    this.spinnerService.stop();
    this.loading = false;
    this.handleErrorFromService(error);
  }

  showErrorDialog(editClass: boolean): void {
    const keyWord = editClass ? 'Editing' : 'Creating';
    let data = new ConfirmModal();
    data.Title = `Something went wrong`;
    data.Text = `${keyWord} a class was unsuccessful. Please try again.`;
    data.ConfirmButton = 'Ok';

    this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });
  }

  GetTitle(): string {
    return this.initSchoolData.ClassId > 0 ? 'Edit Class' : 'Add Class';
  }

  closeModal(): void {
    this.dialogRef.close();
  }
}
