@import '../../../../styles/cashless-theme.scss';
@import '../../../../styles/cashless-font.scss';

.divWrapper {
  margin-top: 16px;
  margin-left: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  @media (min-width: $breakpoint-md) {
    padding-left: 37px;
    padding-right: 37px;
  }
}

button {
  border: none;
  background-color: #f2f2f2;
}

.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
}

.headerTextLeft {
  color: $charcoal-1;
  font-size: 32px;
  font-family: 'bariol_regular';
  text-align: start;
  padding: 0px;
  visibility: hidden;
  @media (min-width: $breakpoint-md) {
    visibility: visible;
  }
}

.headerTextCentre {
  color: $charcoal-1;
  font-size: 20px;
  font-family: 'bariol_bold';
  text-align: center;
  padding: 0px;
  visibility: visible;
  @media (min-width: $breakpoint-md) {
    visibility: hidden;
  }
}

.buttonContainer {
  display: flex;
  justify-content: flex-end;
}

.buttonAddMobile {
  margin-top: 30px;
  margin-bottom: 20px;
  right: 0px;
  padding: 0px;
  margin: 0px;
  font-size: 20px;
  color: $orange-3;
  font-family: 'bariol_bold';
  display: block;
  @media (min-width: $breakpoint-md) {
    display: none;
  }
}

.PrimaryButton {
  display: none;
  @media (min-width: $breakpoint-md) {
    display: block;
    right: 0;
    max-width: 138px;
  }
}

.child-image {
  height: 64px;
  width: 64px;
  margin-left: 15px;
  margin-top: 23px;
  margin-bottom: 23px;
  margin-right: 23px;
  // vertical-align: middle;
}

.childInfoContainer {
  display: flex;
  align-items: center;
}

.cardChild {
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: $grey-3;
  cursor: pointer;
}

.childName {
  font-family: 'bariol_bold';
  font-size: 20px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.noChild {
  padding-left: 23px;
}

.firstChild {
  padding: 23px;
  font-size: 18px;
  line-height: 24px;

  & a {
    color: $orange-3;
    text-decoration: none;
    cursor: pointer;
  }
}
