import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { DetailOptionChoicesComponent } from './detail-option-choices.component';

describe('DetailOptionChoicesComponent', () => {
  let component: DetailOptionChoicesComponent;
  let fixture: ComponentFixture<DetailOptionChoicesComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [DetailOptionChoicesComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DetailOptionChoicesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
