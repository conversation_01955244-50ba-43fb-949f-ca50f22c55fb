import { Component, OnInit, OnDestroy } from '@angular/core';
import { Location } from '@angular/common';
import { Canteen, ListClasses, SchoolClass } from 'src/app/sharedModels';
import { Subscription } from 'rxjs';
import { FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { CanteenState } from 'src/app/states';
import { SchoolClassesService, SpinnerService } from 'src/app/sharedServices';
import { MatDialog } from '@angular/material/dialog';
import { AddSchoolClassComponent } from 'src/app/canteen-settings/components';

@Component({
  selector: 'app-school-settings',
  templateUrl: './school-settings.component.html',
  styleUrls: ['./school-settings.component.scss'],
})
export class SchoolSettingsComponent implements OnInit, OnDestroy {
  listCanteens: Canteen[] = [];
  selectedSchoolId: number;
  form: FormGroup;
  isListLoaded: boolean;
  addClass: boolean;
  private subscription: Subscription;
  listClasses: SchoolClass[] = [];
  selectedClass: SchoolClass;
  classAdded: boolean = false;
  canteenListVisible: boolean = true;

  constructor(
    private _location: Location,
    private store: Store<{ canteen: CanteenState }>,
    private classesService: SchoolClassesService,
    private spinnerService: SpinnerService,
    public dialog: MatDialog
  ) {}

  ngOnInit() {}

  ngOnDestroy(): void {}

  GoBackClick() {
    this._location.back();
  }

  onSchoolSelect(event: number) {
    this.selectedSchoolId = event;
    this.LoadClass(event);
  }

  private LoadClass(schoolId: number) {
    if (schoolId) {
      this.spinnerService.start();
      this.classesService.GetClassesBySchoolAPI(schoolId, true).subscribe({
        next: (response: ListClasses) => {
          this.listClasses = response.Classes;
          this.isListLoaded = true;
          this.spinnerService.stop();
        },
        error: error => {
          this.spinnerService.stop();
        },
      });
    }
  }

  CanteenListVisibleChanged(isVisible: boolean) {
    this.canteenListVisible = isVisible;
  }

  OpenClassModal(schoolClass: SchoolClass) {
    if (!schoolClass) {
      schoolClass = new SchoolClass();
      schoolClass.SchoolId = this.selectedSchoolId;
      schoolClass.IsActive = true;
    }

    const dialogRef = this.dialog.open(AddSchoolClassComponent, {
      width: '500px',
      disableClose: true,
      data: schoolClass,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.LoadClass(result.schoolId);
      }
    });
  }
}
