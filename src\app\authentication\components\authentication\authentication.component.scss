@import '../../../../styles/cashless-breakpoints.scss';
@import '../../../../styles/cashless-theme.scss';

.containerLogo {
  padding-bottom: 24px;
  text-align: center;
  margin-top: 20%;
  // max-width: 720px;
  //border: 1px solid;
  //padding: 10px;
  // border: 1px solid #DDDDDD;
  // border-radius: 12px;
  // box-sizing: border-box;
  // box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  //margin-bottom:20px;

  // @media (min-width: $breakpoint-sm) {
  //    margin-top: 10%;
  //   //  .containerLogo{
  //   //   max-width: 90%;
  //   //  }
  //   & .logo{
  //   max-width: 90%;

  //   }
  //   }

  & .logo {
    margin: 0px;
    padding: 0px;
    max-width: 70%;
  }
}

.versionRow {
  text-align: center;

  & .appVersion {
    font-size: 12px;
  }
}
