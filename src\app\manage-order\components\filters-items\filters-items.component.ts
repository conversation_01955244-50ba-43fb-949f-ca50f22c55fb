import { Component, OnInit, Input } from '@angular/core';
import { MatBottomSheetRef, MatBottomSheet } from '@angular/material/bottom-sheet';

// models
import { MenuTypeEnum } from 'src/app/sharedModels';

// components
import { FiltersItemsSheetComponent } from './filters-items-sheet.component';

@Component({
  selector: 'filters-items',
  templateUrl: './filters-items.component.html',
  styleUrls: ['./filters-items.component.scss'],
})
export class FiltersItemsComponent implements OnInit {
  @Input() deactivatedFilters: string;
  @Input() menuType: string;
  hasActiveSettings: boolean;
  isUniformOrder: boolean = false;

  constructor(private _bottomSheet: MatBottomSheet) {}

  ngOnInit() {
    this.isUniformOrder = this.menuType == MenuTypeEnum.Uniform;
  }

  ClickIconFilter() {
    let bottomSheetRef = this._bottomSheet.open(FiltersItemsSheetComponent, {
      data: this.deactivatedFilters,
    });

    bottomSheetRef.afterDismissed().subscribe(result => {
      this.hasActiveSettings = result;
    });
  }
}
