import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';

// Ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { ChildrenState, FamilyState } from '../../../states';
import { SetSelectedChild } from '../../../states/children/children.actions';
import { children } from '../../../states/children/children.selectors';

import { UserCashless } from '../../../sharedModels';
import { SpinnerService, UserService, StudentService, DebounceService } from '../../../sharedServices';

//Animations
import { trigger, transition, useAnimation } from '@angular/animations';
import { bounce, fadeIn, flipInX, shake, jello, flip } from 'ng-animate';
import { connectedUser } from 'src/app/states/user/user.selectors';

@Component({
  selector: 'manage-children-list-children',
  templateUrl: './list-children.component.html',
  styleUrls: ['./list-children.component.scss'],
  animations: [
    trigger('bounce', [
      transition(
        '* => *',
        useAnimation(fadeIn, {
          // Set the duration to 5seconds and delay to 2seconds
          params: { timing: 0.5, delay: 0 },
        })
      ),
    ]),
  ],
})
export class ListChildrenComponent implements OnInit, OnDestroy {
  private childrenListSubscription: Subscription;
  listStudents: UserCashless[] = [];
  bounce: any;
  addedFirstChild: boolean = false;
  private connectedUserSubscription: Subscription;
  deactivatedUser: boolean = false;

  constructor(
    private store: Store<{ family: FamilyState }>,
    private router: Router,
    private spinnerService: SpinnerService,
    private userService: UserService,
    private studentService: StudentService,
    private debounceService: DebounceService
  ) {}

  ngOnInit() {
    this.childrenListSubscription = this.store.pipe(select(children)).subscribe((state: ChildrenState) => {
      this.spinnerService.start();
      this.listStudents = state.list;
      this.UpdateIntercomDebounce();

      // Set text to explain what to do after adding the first student
      if (!this.listStudents || this.listStudents.length < 1) {
        this.studentService.SetNoStudent(true);
      } else {
        if (this.studentService.GetNoStudent()) {
          this.addedFirstChild = true;
          this.studentService.SetNoStudent(false);
        }
      }

      this.spinnerService.stop();
    });

    this.connectedUserSubscription = this.store
      .pipe(select(connectedUser))
      .subscribe((user: UserCashless) => {
        this.deactivatedUser = !user.IsActive;
      });
  }

  ngOnDestroy(): void {
    if (this.childrenListSubscription) {
      this.childrenListSubscription.unsubscribe();
    }

    if (this.connectedUserSubscription) {
      this.connectedUserSubscription.unsubscribe();
    }
  }

  UpdateIntercomDebounce = this.debounceService.callDebounce(this.updateIntercom, 350, false, true);

  updateIntercom(): void {
    // get schools info into Intercom
    if (this.listStudents) {
      const schoolNameString = this.listStudents.map(s => s.SchoolName).toString();
      const schoolIdArray = this.listStudents.map(s => s.SchoolId.toString());

      this.userService.UpdateUser(schoolNameString, schoolIdArray);
    }
  }

  ShowListStudents(): boolean {
    if (!this.listStudents) {
      return false;
    } else {
      return this.listStudents.length > 0;
    }
  }

  EditChildClick(student: UserCashless) {
    this.store.dispatch(SetSelectedChild({ child: student }));
    this.router.navigate(['family/children/edit']);
  }
}
