import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';

// models
import { SchoolRecord } from 'src/app/sharedModels/admin/dashboard';

@Component({
  selector: 'admin-dashboard-schools',
  templateUrl: './admin-dashboard-schools.component.html',
  styleUrls: ['./admin-dashboard-schools.component.scss'],
})
export class AdminDashboardSchoolsComponent implements OnInit {
  @Input() ordersSchool: SchoolRecord[];

  constructor() {}

  ngOnInit(): void {}

  // ngOnChanges(changes: SimpleChanges): void {
  //   this.dataSource.data = this.ordersSchool;
  // }

  trackBy(index, item) {
    return item;
  }
}
