import { Component, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { KeyValue, Location } from '@angular/common';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import * as moment from 'moment';

// Service
import { SpinnerService, MenuEditorApiService } from 'src/app/sharedServices';

// Models
import {
  BaseComponent,
  MenuItem,
  Canteen,
  MenuItemImage,
  Days,
  MenuTypeEnum,
  NutritionalValue,
  WeekEndLongForm,
  CanteenSchool,
  CategoryEditor,
} from 'src/app/sharedModels';
import {
  convertEnumToKeyValue,
  getDietaryKeyValue,
  getWeekDayKeyValue,
} from 'src/app/sharedModels/base/KeyValueConversion';

// ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { CanteenState } from '../../../states';
import { canteenStateSelector } from '../../../states/canteen/canteen.selectors';

//Mat dialog
import { ConfirmModal, MenuItemAvailability } from '../../../sharedModels';
import { DialogConfirmComponent } from '../../../shared/components';
import { MatDialog } from '@angular/material/dialog';
import { DialogDeleteMenuItemComponent } from '../dialog-delete-menu-item/dialog-delete-menu-item.component';

@Component({
  selector: 'editor-item',
  templateUrl: './editor-item.component.html',
  styleUrls: ['./editor-item.component.scss'],
})
export class EditorItemComponent extends BaseComponent implements OnInit, OnDestroy {
  private subscription: Subscription;
  private _currentSelectedCanteen: Canteen;
  item: MenuItem;
  form: FormGroup;
  showError: boolean = false;
  merchantSchools: CanteenSchool[];
  merchantId: number;
  daysHelper: Days;
  selectedNutritional: NutritionalValue = new NutritionalValue();
  nutritionalValues: NutritionalValue[] = [];
  merchantCategories: KeyValue<string, string>[] = [];

  menuItemAvailability: MenuItemAvailability[];
  displayedColumns: string[] = ['StartDate', 'EndDate', 'Actions'];
  menuItemAvailabilitySubmit = new MenuItemAvailability();

  dietaryCheckBoxValues: KeyValue<string, string>[] = [];
  weekDayCheckBoxValues: KeyValue<string, string>[] = [];
  weekEndCheckBoxValues: KeyValue<string, string>[] = [];

  constructor(
    private activatedRoute: ActivatedRoute,
    private _location: Location,
    private spinnerService: SpinnerService,
    private menuEditorAPIService: MenuEditorApiService,
    public dialog: MatDialog,
    private store: Store<{ canteen: CanteenState }>
  ) {
    super();
  }

  ngOnInit() {
    this.subscription = this.store.pipe(select(canteenStateSelector)).subscribe((state: CanteenState) => {
      if (state.selected) {
        this._currentSelectedCanteen = state.selected;
        this.merchantSchools = this._currentSelectedCanteen?.Schools;
        this.merchantId = this._currentSelectedCanteen.CanteenId;
        this.getCategoryData(state.menuCategories);

        this.item = this.activatedRoute.snapshot.data['item'] || new MenuItem();
        this._createForm();
      }

      if (state.selected && state.nutritionalValues) {
        this.nutritionalValues = state.nutritionalValues;

        if (this.item.NutritionalValue) {
          this.selectedNutritional = this.nutritionalValues.find(n => n.Name == this.item.NutritionalValue);

          if (!this.selectedNutritional) {
            this.selectedNutritional = new NutritionalValue();
          }
        }
      }
    });

    this.dietaryCheckBoxValues = getDietaryKeyValue();
    this.weekDayCheckBoxValues = getWeekDayKeyValue();
    this.weekEndCheckBoxValues = convertEnumToKeyValue(WeekEndLongForm);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  getCategoryData(categories: CategoryEditor[]) {
    if (!categories) {
      return;
    }
    categories.forEach(category =>
      this.merchantCategories.push({ key: category.MenuCategoryId.toString(), value: category.CategoryName })
    );
  }

  IsUniformCanteen() {
    return this._currentSelectedCanteen && this._currentSelectedCanteen.CanteenType == MenuTypeEnum.Uniform;
  }

  ///////////////////////////////////
  // Form
  ///////////////////////////////////

  get name() {
    return this.form.get('name');
  }
  get categoryData() {
    return this.form.get('categoryData');
  }
  get description() {
    return this.form.get('description');
  }
  get sortOrder() {
    return this.form.get('sortOrder');
  }
  get price() {
    return this.form.get('price');
  }
  get available() {
    return this.form.get('available');
  }
  get hasGst() {
    return this.form.get('hasGst');
  }
  get hasCutOffTime() {
    return this.form.get('hasCutOffTime');
  }
  get cutOffTimeType() {
    return this.form.get('cutOffTimeType');
  }
  get cutOffTime() {
    return this.form.get('cutOffTime');
  }
  get Vegetarian() {
    return this.form.get('Vegetarian');
  }
  get Vegan() {
    return this.form.get('Vegan');
  }
  get GlutenFree() {
    return this.form.get('GlutenFree');
  }
  get Halal() {
    return this.form.get('Halal');
  }
  get LactoseFree() {
    return this.form.get('LactoseFree');
  }
  get NutsFree() {
    return this.form.get('NutsFree');
  }
  get FastingFriendly() {
    return this.form.get('FastingFriendly');
  }
  get DairyFree() {
    return this.form.get('DairyFree');
  }

  get Monday() {
    return this.form.get('Monday');
  }
  get Tuesday() {
    return this.form.get('Tuesday');
  }
  get Wednesday() {
    return this.form.get('Wednesday');
  }
  get Thursday() {
    return this.form.get('Thursday');
  }
  get Friday() {
    return this.form.get('Friday');
  }
  get Saturday() {
    return this.form.get('Saturday');
  }
  get Sunday() {
    return this.form.get('Sunday');
  }

  get itemStartDate() {
    return this.form.get('itemStartDate');
  }

  get itemEndDate() {
    return this.form.get('itemEndDate');
  }

  private _createForm() {
    if (!this.item || !this.item.MenuItemId) {
      this.item = new MenuItem();
      this.item.IsActive = true;
      this.item.Images = [];
      this.item.AvailabilityDays = 'M,T,W,Th,F';
    }

    this.menuItemAvailability = this.item.Availabilities;
    this.daysHelper = new Days(this.item.AvailabilityDays);
    const categoryKeyValue = this.merchantCategories.find(x => parseInt(x.key) === this.item.CategoryId);
    const selectedCategoryId = categoryKeyValue?.key ? categoryKeyValue.key : null;

    this.form = new FormGroup({
      name: new FormControl(this.item.Name, [Validators.required]),
      categoryData: new FormControl(selectedCategoryId, [Validators.required]),
      description: new FormControl(this.item.Desc),
      sortOrder: new FormControl(this.item.SortOrder, [Validators.min(1)]),
      price: new FormControl(this.item.Price, [Validators.required]),
      available: new FormControl(this.item.IsActive),
      hasGst: new FormControl(this.item.IsGstApplied),

      Vegetarian: new FormControl(this.item.IsVeg),
      Vegan: new FormControl(this.item.IsVegan),
      GlutenFree: new FormControl(this.item.IsGF),
      Halal: new FormControl(this.item.IsHalal),
      LactoseFree: new FormControl(this.item.IsLactoseFree),
      NutsFree: new FormControl(this.item.IsNutsFree),
      FastingFriendly: new FormControl(this.item.IsFastingFriendly),
      DairyFree: new FormControl(this.item.IsDairyFree),

      Monday: new FormControl(this.daysHelper.IsAvailableMonday()),
      Tuesday: new FormControl(this.daysHelper.IsAvailableTuesday()),
      Wednesday: new FormControl(this.daysHelper.IsAvailableWednesday()),
      Thursday: new FormControl(this.daysHelper.IsAvailableThursday()),
      Friday: new FormControl(this.daysHelper.IsAvailableFriday()),
      Saturday: new FormControl(this.ItemAvailableSaturday()),
      Sunday: new FormControl(this.ItemAvailableSunday()),

      itemStartDate: new FormControl(),
      itemEndDate: new FormControl(),
    });
  }

  updateMenuItemImages(images: MenuItemImage[]) {
    this.item.Images = images;
    this.onSubmit();
  }

  onSubmit() {
    if (this.form.invalid) {
      return;
    }
    this.spinnerService.start();

    this._convertToItemObject();

    this.menuEditorAPIService.UpsertMenuItemAPI(this.item).subscribe({
      next: (response: MenuItem) => {
        this.item = response;
        this.menuItemAvailability = this.item.Availabilities;
        this.setItemAvailableDates();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  /** Put the values of the form into the MenuItem */
  private _convertToItemObject(): void {
    this.item.Name = this.name.value;
    this.item.CanteenId = this._currentSelectedCanteen.CanteenId;
    this.item.CategoryId = this.categoryData.value;

    this.item.Desc = this.description.value;
    this.item.SortOrder = this.sortOrder.value;
    this.item.Price = this.price.value;
    this.item.IsActive = this.available.value;
    this.item.IsGstApplied = this.hasGst.value;

    this.processItemDateAvailability();

    if (this.IsUniformCanteen()) {
      return;
    }

    this.processCutOffTime();
    this.processDietaryLabels();
    this.processNutritionalInformation();
    this.processItemWeekdayAvailability();
  }

  processItemDateAvailability() {
    this.item.Availabilities = [];

    if (this.itemStartDate.value && this.itemEndDate.value) {
      this.menuItemAvailabilitySubmit.StartDate = new Date(
        moment(this.itemStartDate.value).format('YYYY-MM-DD')
      );
      this.menuItemAvailabilitySubmit.EndDate = new Date(moment(this.itemEndDate.value).format('YYYY-MM-DD'));
      this.menuItemAvailabilitySubmit.MenuItemId = this.item.MenuItemId;
      this.item.Availabilities.push(this.menuItemAvailabilitySubmit);
    }
  }

  processDietaryLabels() {
    this.item.IsVeg = this.Vegetarian.value;
    this.item.IsVegan = this.Vegan.value;
    this.item.IsGF = this.GlutenFree.value;
    this.item.IsHalal = this.Halal.value;
    this.item.IsLactoseFree = this.LactoseFree.value;
    this.item.IsNutsFree = this.NutsFree.value;
    this.item.IsFastingFriendly = this.FastingFriendly.value;
    this.item.IsDairyFree = this.DairyFree.value;
  }

  processCutOffTime() {
    if (this.hasCutOffTime.value) {
      this.item.CutOffTime = +this.cutOffTime.value;
      this.item.CutOffTimeType = this.cutOffTimeType.value;
    } else {
      this.item.CutOffTime = null;
      this.item.CutOffTimeType = null;
    }
  }

  processNutritionalInformation() {
    this.item.NutritionalValue = this.selectedNutritional.Name;
    this.item.NutritionalColor = this.selectedNutritional.Color;
  }

  processItemWeekdayAvailability() {
    let availableDayString = '';

    if (this.Monday.value == true) {
      availableDayString += 'M,';
    }

    if (this.Tuesday.value == true) {
      availableDayString += 'T,';
    }

    if (this.Wednesday.value == true) {
      availableDayString += 'W,';
    }

    if (this.Thursday.value == true) {
      availableDayString += 'Th,';
    }

    if (this.Friday.value == true) {
      availableDayString += 'F,';
    }
    if (this.Saturday.value == true) {
      availableDayString += 'S,';
    }
    if (this.Sunday.value == true) {
      availableDayString += 'Su,';
    }

    if (availableDayString.length > 0) {
      this.item.AvailabilityDays = availableDayString.substring(0, availableDayString.length - 1);
    } else {
      this.item.AvailabilityDays = '';
    }
  }

  ///////////////////////////////////
  // view functions
  ///////////////////////////////////
  ArchiveClicked(menuItemAvail: MenuItemAvailability) {
    let data = new ConfirmModal();
    data.Title = 'Delete Item Available Date';
    data.Text = 'Deleting this record cannot be undone, are you sure you want to delete it?';
    data.CancelButton = 'No';
    data.ConfirmButton = 'Yes';

    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.deleteItemAvailableDate(menuItemAvail);
      }
    });
  }

  deleteItemAvailableDate(menuItemAvail: MenuItemAvailability) {
    this.spinnerService.start();
    this.menuEditorAPIService
      .DeleteItemAvailabilityDates(menuItemAvail.AvailabilityId, menuItemAvail.MenuItemId)
      .subscribe(
        (response: MenuItemAvailability[]) => {
          this.menuItemAvailability = response;
          this.spinnerService.stop();
        },
        error => {
          this.spinnerService.stop();
          this.handleErrorFromService(error);
        }
      );
  }

  setItemAvailableDates() {
    this.itemStartDate.setValue(null);
    this.itemEndDate.setValue(null);
  }

  ItemAvailableSaturday() {
    if (this.IsUniformCanteen()) {
      return true;
    } else {
      return this.daysHelper.IsAvailableSaturday();
    }
  }

  ItemAvailableSunday() {
    if (this.IsUniformCanteen()) {
      return true;
    } else {
      return this.daysHelper.IsAvailableSunday();
    }
  }

  getErrorMessage() {
    return 'Required';
  }

  GetTextSubmitButton() {
    return this.item.MenuItemId ? 'Save' : 'Add';
  }

  GoBackClick() {
    this._location.back();
  }

  CanDeleteItem() {
    return this.item.MenuItemId > 0;
  }

  DeleteMenuItem(e) {
    e.preventDefault();
    this.dialog.open(DialogDeleteMenuItemComponent, {
      data: {
        displayOption: 'item',
        onDeletePress: () => {
          const deletedItemData = Object.assign(new MenuItem(), {
            ...this.item,
            IsDeleted: true,
          });
          this.spinnerService.start();

          this.menuEditorAPIService.DeleteMenuItemAPI(deletedItemData).subscribe(
            (response: MenuItem) => {
              this.item = response;
              this.spinnerService.stop();
              this.GoBackClick();
            },
            error => {
              this.spinnerService.stop();
              this.handleErrorFromService(error);
            }
          );
        },
      },
    });
  }

  onNutritionalSelect(item: NutritionalValue) {
    if (item.Name === this.selectedNutritional.Name) {
      this.selectedNutritional = new NutritionalValue();
    } else {
      this.selectedNutritional = item;
    }
  }
}
