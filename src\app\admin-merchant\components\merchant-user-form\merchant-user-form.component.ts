import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { FormControl, FormGroup, Validators, FormBuilder, FormArray } from '@angular/forms';
import { SpinnerService, MerchantService } from '../../../sharedServices';
import {
  ResultDialogData,
  BaseComponent,
  Merchant,
  MerchantAdmin,
  CanteenUser,
  School,
  MerchantPermissions,
  MerchantFormType,
  MerchantFormPermissions,
} from '../../../sharedModels';

//dialog imports
import { MatDialog } from '@angular/material/dialog';
import { DialogResultComponent } from 'src/app/shared/components/';

@Component({
  selector: 'app-merchant-user-form',
  templateUrl: './merchant-user-form.component.html',
  styleUrls: ['./merchant-user-form.component.scss'],
})
export class MerchantUserFormComponent extends BaseComponent implements OnInit {
  private routeSubscription: Subscription;
  form: FormGroup;
  schoolForm: FormGroup;
  submitError: boolean = false;
  editUserInput: boolean = false;
  formTouched: boolean = false;
  editMode: boolean;
  pageTitle: string;
  currentRoute: any;
  selectedMerchant: Merchant;
  currentUser: CanteenUser;
  schoolData: School[];
  checkedSchoolIndex: number[] = [];

  constructor(
    private spinnerService: SpinnerService,
    private router: Router,
    private merchantService: MerchantService,
    private formBuilder: FormBuilder,
    public dialog: MatDialog,
    private route: ActivatedRoute
  ) {
    super();

    //get current route
    this.routeSubscription = router.events.subscribe(route => (this.currentRoute = route));
  }

  ngOnInit() {
    // get data from resolver
    this.route.data.subscribe(data => {
      this.selectedMerchant = data['merchant'];
      this.currentUser = data['user'];
      this.schoolData = data['schools'];
    });

    //set state of page
    this.editMode = this.currentRoute.url.includes('userSearch') ? false : true;
    this.pageTitle = this.currentRoute.url.includes('userSearch')
      ? 'Add merchant user'
      : 'Edit merchant user';

    this.FindIndexOfCheckedSchools();
    this.CreateForm();
  }

  ngOnDestroy(): void {
    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }

    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }

    if (!this.editMode) {
      //clear search filter if navigating off search result pages
      if (!this.currentRoute.url.includes('userSearch')) {
        this.merchantService.setMerchantSearchFilters(null);
      }
    }

    this.merchantService.setDisableMode(false);
  }

  GoBackClick() {
    if (this.editMode) {
      let data = new ResultDialogData();
      data.TitleLine1 = 'Are you sure?';
      data.TextLine1 = 'Are you sure you want to leave this page?';
      data.TextLine2 = 'Changes you made will not be saved.';
      data.CancelButton = 'Stay on Page';
      data.ConfirmButton = 'Leave Page';

      const dialogRef = this.dialog.open(DialogResultComponent, {
        width: '400px',
        disableClose: true,
        data: data,
      });

      dialogRef.afterClosed().subscribe(stayResult => {
        if (!stayResult) {
          this.spinnerService.start();
          this.router.navigate(['./admin/merchants']);
        }
      });
    } else {
      this.spinnerService.start();
      this.router.navigate(['./admin/merchants/' + this.selectedMerchant.canteenId + '/userSearch']);
    }
  }

  isSchoolListEmpty() {
    return !this.schoolData || this.schoolData.length === 0;
  }

  get schoolFormArray() {
    return this.schoolForm.controls.schools as FormArray;
  }

  //return array of indexes of selected schools in schoolData
  FindIndexOfCheckedSchools() {
    if (this.currentUser && this.currentUser.Schools && this.editMode) {
      this.currentUser.Schools.forEach(selectedSchool => {
        let index = this.schoolData.findIndex(
          schoolOption => schoolOption.SchoolId === selectedSchool.SchoolId
        );
        this.checkedSchoolIndex.push(index);
      });
    }
  }

  //function to see if current index matches a selected school index
  matchIndex(count: number) {
    let result = false;
    this.checkedSchoolIndex.forEach(selectedSchool => {
      if (count === selectedSchool) {
        result = true;
      }
    });
    return result;
  }

  CreateForm() {
    const name = this.currentUser.FirstName + ' ' + this.currentUser.Lastname;

    this.form = new FormGroup({
      name: new FormControl(name, [Validators.required]),
      email: new FormControl(this.currentUser.Email, [Validators.required, Validators.email]),
      isAdmin: new FormControl(this.currentUser?.IsAdmin),
      menuEditor: new FormControl(this.currentUser?.IsMenuEditorAvailable),
      salesReport: new FormControl(this.currentUser?.IsSaleReportsAvailable),
      viewEvent: new FormControl(this.currentUser?.IsEventManagementAvailable),
      allowUnprintedOrders: new FormControl(this.currentUser?.IsOrdersNotPrintedReportsAvailable),
      emailUnprintedOrders: new FormControl(this.currentUser?.NotifyOrdersNotPrinted),
    });

    //generate dynamic schools form
    this.schoolForm = this.formBuilder.group({
      schools: new FormArray([]),
    });

    // Create form control for each school checkbox value
    if (this.schoolData) {
      this.schoolData.forEach((el, count) => {
        //check if school index matches the index of currentUsers selected school
        if (this.editMode && this.matchIndex(count)) {
          this.schoolFormArray.push(new FormControl(true));
        } else {
          this.schoolFormArray.push(new FormControl(false));
        }
      });
    }
  }

  //function for 'Select all' and 'Clear' buttons
  formSelect(formType: string, setValue: boolean) {
    this.formTouched = true;
    if (formType === MerchantFormPermissions.user) {
      this.setUserPermissions(setValue);
    } else {
      this.schoolFormArray.setValue(this.setAllSchoolValues(setValue));
    }
  }

  //function to set all user permission to a boolean val
  setUserPermissions(value: boolean) {
    this.form.patchValue({
      emailUnprintedOrders: value,
      allowUnprintedOrders: value,
      salesReport: value,
      menuEditor: value,
      viewEvent: value
    });
  }

  // function to generate an array of values to mimic select all or clear all for the schools form
  setAllSchoolValues(value: boolean) {
    let valueArray = [];
    this.schoolData.forEach(() => {
      valueArray.push(value);
    });
    return valueArray;
  }

  //function to genrate a list of all selected schools in form
  getSelectedSchools() {
    const selectedSchoolIds = this.schoolForm.value.schools
      .map((v, i) => (v ? this.schoolData[i].SchoolId : null))
      .filter(v => v !== null);

    return selectedSchoolIds;
  }

  getSchoolError() {
    if (this.isSchoolListEmpty()) {
      return 'Merchant must have at least one school to make user changes';
    } else {
      return 'Selected school permissions';
    }
  }

  getPermissionsData() {
    let permissions: MerchantPermissions = {
      isAdmin: this.form.get('isAdmin').value,
      isMenuEditorAvailable: this.form.get('menuEditor').value,
      isSaleReportsAvailable: this.form.get('salesReport').value,
      isOrdersNotPrintedReportsAvailable: this.form.get('allowUnprintedOrders').value,
      isEventManagementAvailable: this.form.get('viewEvent').value,
      notifyOrdersNotPrinted: this.form.get('emailUnprintedOrders').value,
    };

    let selectedSchools = this.getSelectedSchools();

    let data: MerchantAdmin = {
      userId: this.currentUser.UserId,
      schools: selectedSchools,
      permissions: permissions,
    };
    return data;
  }

  isFormInvalid() {
    return this.form.invalid || this.getSelectedSchools().length === 0;
  }

  disableSaveChanges() {
    if (this.formTouched) {
      return !this.formTouched;
    } else {
      return !(this.form.dirty || this.schoolForm.dirty);
    }
  }

  ///////////////////////
  // Add User
  ///////////////////////
  addUser() {
    if (this.isFormInvalid()) {
      this.submitError = true;
    } else {
      this.submitError = false;

      let data = new ResultDialogData();
      data.TitleLine1 = 'Add merchant user';
      data.TextLine1 = `Are you sure you want to add ${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}) as a Merchant User?`;
      data.TextLine2 = '';
      data.CancelButton = 'Cancel';
      data.ConfirmButton = 'Yes, add now';

      const dialogRef = this.dialog.open(DialogResultComponent, {
        width: '400px',
        disableClose: true,
        data: data,
      });

      dialogRef.afterClosed().subscribe(cancelResult => {
        if (!cancelResult) {
          this.ConfirmAddUser();
        }
      });
    }
  }

  ConfirmAddUser() {
    let newUser = this.getPermissionsData();

    this.spinnerService.start();
    this.merchantService.AddUserToMerchantAdmin(this.selectedMerchant.canteenId, newUser).subscribe(
      res => {
        this.spinnerService.stop();
        this.SuccessPopUp(
          `${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}) added successfully.`
        );
      },
      error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
        this.SomethingWentWrongPopup(
          `We were unable to add ${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}).`,
          MerchantFormType.Add
        );
      }
    );
  }

  ///////////////////////
  // Update User
  ///////////////////////
  updateUser() {
    if (this.isFormInvalid()) {
      this.submitError = true;
    } else {
      this.submitError = false;
      this.spinnerService.start();

      let data = this.getPermissionsData();

      this.merchantService
        .UpdateMerchantAdminPermissions(this.selectedMerchant.canteenId, this.currentUser.UserId, data)
        .subscribe({
          next: res => {
            this.spinnerService.stop();
            this.SuccessPopUp(
              `${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}) updated successfully.`
            );
          },
          error: error => {
            this.SomethingWentWrongPopup(
              `We were unable to update ${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}).`,
              MerchantFormType.Update
            );
            this.spinnerService.stop();
            this.handleErrorFromService(error);
          },
        });
    }
  }

  ///////////////////////
  // Remove User
  ///////////////////////
  removeUser() {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Remove merchant user';
    data.TextLine1 = `Are you sure you want to remove ${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}) as a Merchant User?`;
    data.TextLine2 = '';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, remove';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelRemove => {
      if (!cancelRemove) {
        this.ConfirmRemoveUser();
      }
    });
  }

  ConfirmRemoveUser() {
    this.spinnerService.start();
    this.merchantService
      .RemoveMerchantAdminFromMerchant(this.selectedMerchant.canteenId, this.currentUser.UserId)
      .subscribe({
        next: res => {
          this.spinnerService.stop();
          this.SuccessPopUp(`Merchant removed successfully.`);
        },
        error: error => {
          this.spinnerService.stop();
          this.handleErrorFromService(error);
          this.SomethingWentWrongPopup(
            `We were unable to remove ${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}).`,
            MerchantFormType.Remove
          );
        },
      });
  }

  ///////////////////////
  // Pop ups
  ///////////////////////
  SomethingWentWrongPopup(text: string, type) {
    let data = new ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = text;
    data.TextLine2 = 'Please try again.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        if (type === MerchantFormType.Add) {
          this.addUser();
        } else if (type === MerchantFormType.Update) {
          this.updateUser();
        } else {
          this.removeUser();
        }
      }
    });
  }

  SuccessPopUp(text: string) {
    let data = new ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = text;
    data.ConfirmButton = 'Okay';

    const dialogRef = this.dialog.open(DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      this.spinnerService.start();
      this.router.navigate(['./admin/merchants']);
      this.merchantService.setUpdateMerchantUserPermissions(true);
    });
  }
}
