<div class="container-fluid">
  <nav-back-button (navBack)="GoBackClick()" text="Go back"></nav-back-button>

  <ng-container *ngIf="stock">
    <div class="row">
      <div class="col-6 col-lg-4">
        <h2>Stock</h2>

        <div class="cardDefaultCanteen">
          <form (ngSubmit)="SubmitForm()" #stockForm="ngForm">
            <div>
              <mat-form-field appearance="outline">
                <mat-label>Stock name</mat-label>
                <input
                  matInput
                  [(ngModel)]="stock.StockName"
                  name="StockName"
                  placeholder="Name"
                  type="text"
                  maxlength="200"
                  required
                />
              </mat-form-field>
            </div>

            <div class="pb-4">
              <mat-radio-group
                aria-label="Select stock type"
                [value]="stockType"
                (change)="TypeChanged($event)"
              >
                <mat-radio-button value="1">Daily limit</mat-radio-button>
                <mat-radio-button value="2">Stock limit</mat-radio-button>
              </mat-radio-group>
            </div>

            <div *ngIf="stockType == '1'">
              <mat-form-field appearance="outline">
                <mat-label>Daily stock</mat-label>
                <input
                  matInput
                  [(ngModel)]="stock.DailyStock"
                  name="DailyStock"
                  placeholder="Daily quantity"
                  type="number"
                  required
                />
              </mat-form-field>
            </div>

            <div *ngIf="stockType == '2'">
              <mat-form-field appearance="outline">
                <mat-label>Remaining quantity</mat-label>
                <input
                  matInput
                  [(ngModel)]="stock.StockQuantity"
                  name="StockQuantity"
                  placeholder="Remaining quantity"
                  type="number"
                  required
                />
              </mat-form-field>
            </div>

            <div>
              <mat-checkbox [(ngModel)]="stock.IsActive" name="IsActive">Is Active</mat-checkbox>
            </div>

            <div class="divButton">
              <button class="PrimaryButton action" type="submit" [disabled]="!stockForm.form.valid">
                {{ GetTextButton() }}
              </button>

              <button
                *ngIf="ShowDeleteButton()"
                [disabled]="!stock.IsActive"
                class="PrimaryButton deleteButton" 
                type="button"
                (click)="ArchiveStock()"
              >
                Delete Stock
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <editor-detail-stock-items
      *ngIf="stock.StockId > 0 && selectedCanteen"
      [stock]="stock"
      [canteenId]="selectedCanteen.CanteenId"
    ></editor-detail-stock-items>
  </ng-container>
</div>
