import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';

//services
import { SpinnerService, MerchantService } from '../../../sharedServices';

//models
import { BasePaginatorComponent, Merchant } from '../../../sharedModels';

const _columns = ['id', 'firstName', 'lastName', 'phone', 'email', 'select'];

@Component({
  selector: 'app-add-merchant-user-search',
  templateUrl: './add-merchant-user-search.component.html',
  styleUrls: ['./add-merchant-user-search.component.scss'],
})
export class AddMerchantUserSearchComponent
  extends BasePaginatorComponent<Merchant>
  implements OnInit, OnDestroy
{
  currentRoute: any;
  private routeSubscription: Subscription;
  noResultsMessage: string = '';
  showResultsTable: boolean = false;
  selectedMerchant: Merchant;

  constructor(
    private spinnerService: SpinnerService,
    private router: Router,
    private route: ActivatedRoute,
    private merchantService: MerchantService
  ) {
    super(_columns);

    //get current route
    this.routeSubscription = router.events.subscribe(route => (this.currentRoute = route));
  }

  ngOnInit() {
    // get data from resolver
    this.route.data.subscribe(data => {
      this.selectedMerchant = data['merchant'];
    });

    // get current filters
    this.listfilters = this.merchantService.getMerchantSearchFilters();
    if (this.listfilters?.Filter) {
      this._requestUsers();
    } else {
      this.initFilters();
    }
  }

  ngOnDestroy(): void {
    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }

    //clear search filter if navigating off search result pages
    if (!this.currentRoute.url.includes('userSearch')) {
      this.clearFilter();
    }

    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }

  goBackClick() {
    this.spinnerService.start();
    this.router.navigate(['./admin/merchants']);
  }

  clearFilter() {
    this.clearFiltersAndResults();
    //reset saved filter
    this.merchantService.setMerchantSearchFilters(this.listfilters);
  }

  fetchData(searchInput: string) {
    this.listfilters.Filter = searchInput;
    this._requestUsers();
  }

  /** Call the user service to get the users */
  private _requestUsers() {
    this.spinnerService.start();

    // save current filters
    this.merchantService.setMerchantSearchFilters(this.listfilters);

    this.merchantService
      .GetUsersToAddToCanteen(this.selectedMerchant.canteenId, this.listfilters.Filter)
      .subscribe({
        next: (res: any) => {
          this._ProcessResponseData(res);
        },
        error: error => {
          this.spinnerService.stop();
          this.handleErrorFromService(error);
        },
      });
  }

  /** Process the list of users to be used in the search results */
  private _ProcessResponseData(response: Merchant[]) {
    if (response) {
      this.listObjects = response;

      if (this.listObjects && this.listObjects.length > 0) {
        this.totalRows = this.listObjects[0].TotalRows;
        this.showResults();
      } else {
        this.totalRows = 0;
      }
    } else {
      this.noSearchResults(this.listfilters.Filter);
    }
    this.dataSource.data = this.listObjects;

    this.spinnerService.stop();
  }
}
