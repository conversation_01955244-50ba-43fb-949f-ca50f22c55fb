import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { saveAs } from 'file-saver';
import moment from 'moment';
import * as _ from 'lodash';

// models
import { BasePaginatorComponent, TransactionHistory } from 'src/app/sharedModels';

const _columns = ['id', 'type', 'date', 'amount', 'updatedBalance', 'status', 'error', 'message', 'options'];

@Component({
  selector: 'user-management-transactions-table',
  templateUrl: './user-transactions-table.component.html',
  styleUrls: ['./user-transactions-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserTransactionsTableComponent
  extends BasePaginatorComponent<TransactionHistory>
  implements OnInit, OnChanges
{
  @Input() data: TransactionHistory[];
  selectedTransaction: TransactionHistory;
  selectedTransactionId: number;

  constructor() {
    super(_columns);
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    for (const propName in changes) {
      switch (propName) {
        case 'data':
          if (this.data) {
            this.processData(this.data);
          }
          break;

        default:
          break;
      }
    }
  }

  processData(data: TransactionHistory[]) {
    const updatedData = _.cloneDeep(data);

    if (updatedData) {
      //Conversion from from utc to local time
      updatedData.map(x => {
        x.dateCreatedUtc
          ? (x.dateCreatedUtc = new Date(moment.utc(x.dateCreatedUtc).local().format('YYYY-MM-DD HH:mm:ss')))
          : null;
      });
    }
    this.dataSource.data = updatedData;
  }

  clickRow(row: TransactionHistory) {
    this.selectedTransaction = row;
    this.selectedTransactionId = row.transactionId;
  }

  closeSideBar() {
    this.selectedTransaction = null;
    this.selectedTransactionId = null;
  }

  downloadCsv() {
    const replacer = (key, value) => (value === null ? '' : value);
    const header = [
      'transactionId',
      'dateCreatedUtc',
      'description',
      'amount',
      'debit',
      'currentBalance',
      'declined',
    ];
    let csv = this.data.map(row => header.map(field => JSON.stringify(row[field], replacer)).join(','));

    header[1] = 'DateCreated';
    header[6] = 'Status';

    csv.unshift(header.join(','));
    let csvArray = csv.join('\r\n');
    const finalCsv = new Blob([csvArray], { type: 'text/csv' });
    saveAs(finalCsv, `TransactionHistory.csv`);
  }
}
