// import theme
@import 'src/styles/schools-theme';
.sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.schools-toolbar {
  position: sticky;
  position: -webkit-sticky; /* For macOS/iOS Safari */
  top: 0; /* Sets the sticky toolbar to be on top */
  z-index: 1000; /* Ensure that your app's content doesn't overlap the toolbar */
  background-color: $text-dark-strong;
  width: 100%;
  height: 65px;
}

.logo-desktop {
  margin-left: 26px;
}

.logo-mobile {
  margin-right: 26px;
}

ul {
  // margin: auto;
  padding: 0;
  height: 100%;

  & li {
    display: inline-block;
    margin-top: 18px;
    cursor: pointer;

    & a {
      text-decoration: none;
      font-weight: 500;
      font-size: 18px;
      color: $icon-light-strong;
      padding: 0 15px 0 15px;

      &.activeLink + .tab-selected {
        height: 6px;
        background-color: $secondary-pressed;
        border-radius: 29px;
        display: block;
        margin: 8px 1px 0 1px;
      }
    }
  }
}

.profile-icon {
  padding: 0 24px 0 40px;
}

.example-spacer {
  flex: 1 1 auto;
}

// leaving it for the merchant side in the future
//  img{
//     vertical-align: middle;
//     height: 24px;
//     width: 24px;
//     margin-right: 10px;
//     margin-top:auto;
//     margin-bottom: auto;
//  }

.disable-nav {
  left: 0;
  position: absolute;
  max-width: 100%;
  width: 100%;
  height: 100%;
  background-color: rgb(255, 255, 255);
  opacity: 0.5;
  z-index: 100;
}
