<div class="col-12">
  <nav-back-button (navBack)="GoBackClick()" text="Go Back" class="backButton"></nav-back-button>

  <!-- School Selection Bar -->
  <div class="titleWrapper">
    <img sizes="24" src="assets/icons/settings.svg" />
    <h1 class="title">School Settings</h1>
  </div>

  <merchant-school-picker (schoolChanged)="onSchoolSelect($event)"></merchant-school-picker>

  <div class="row">
    <div class="col-12">
      <div class="titleDescriptionWrapper">
        <h3 class="titleDescription">Class List</h3>
        <a class="titleDescriptionButton" (click)="OpenClassModal(null)">
          <p class="titleDescriptionButtonText">Add Class</p>
          <img sizes="24" src="assets/icons/plus.svg" />
        </a>
      </div>
    </div>
    <div class="col-12">
      <classes-list
        *ngIf="isListLoaded"
        [listClasses]="listClasses"
        (editClicked)="OpenClassModal($event)"
      ></classes-list>
    </div>
  </div>
</div>
