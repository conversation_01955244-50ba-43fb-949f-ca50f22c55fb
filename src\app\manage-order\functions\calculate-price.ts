import { CartItem, CartOption, RefinedOrderItem } from '../../sharedModels';
import * as _ from 'lodash';

//////////////////////
// CART PRICES
//////////////////////
export function GetCartItemsPrice(orderItems: CartItem[], getSingleItemPrice: boolean = false): number {
  return orderItems?.reduce(
    (accumulator, currentValue) => accumulator + GetTotalItemAmount(currentValue, getSingleItemPrice),
    0
  );
}

function GetTotalItemAmount(item: CartItem, getSingleItemPrice: boolean) {
  const quantity = getSingleItemPrice ? 1 : item.quantity;
  const totalItemPrice = item.itemPriceIncGst * quantity;
  const totalOptionPrice = getTotalOptionPrice(item.selectedOptions, quantity);
  return totalItemPrice + totalOptionPrice;
}

export const getTotalOptionPrice = (selectedOptions: CartOption[], itemQuantity: number): number => {
  return selectedOptions?.length > 0
    ? selectedOptions
        .map((option: CartOption) => option.optionCost * itemQuantity)
        .reduce((prev: number, next: number) => prev + next)
    : 0;
};

//////////////////////
// ORDER PRICES
//////////////////////

export function GetOrderItemsPrice(
  orderItems: RefinedOrderItem[],
  getSingleItemPrice: boolean = false
): number {
  return orderItems.reduce(
    (accumulator, currentValue) => accumulator + GetOrderItemPrice(currentValue, getSingleItemPrice),
    0
  );
}

function GetOrderItemPrice(orderItem: RefinedOrderItem, getSingleItemPrice: boolean): number {
  const selectedOptionPrice = orderItem.SelectedOptions.reduce(
    (accumulator, currentValue) => accumulator + currentValue.OptionCost,
    0
  );
  const quantity = getSingleItemPrice ? 1 : +orderItem.Quantity;
  return (orderItem.ItemPriceIncGst + selectedOptionPrice) * quantity;
}
