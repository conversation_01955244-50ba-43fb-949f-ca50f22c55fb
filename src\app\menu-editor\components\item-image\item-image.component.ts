import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'item-image',
  templateUrl: './item-image.component.html',
  styleUrls: ['./item-image.component.scss'],
  standalone: true,
  imports: [
    CommonModule
  ]
})
export class ItemImageComponent implements OnInit {
  @Input() url: string;
  @Input() isSmall: boolean = false;
  imageSize = 100;
  placeholderSize = 60;

  ngOnInit(): void {
      if(this.isSmall){
        this.imageSize = 60;
        this.placeholderSize = 30;
      }
  }
}
