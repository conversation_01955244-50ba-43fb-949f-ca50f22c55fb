<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h3>Schools</h3>
    </div>
  </div>

  <search-panel
    (triggerSearch)="fetchData($event)"
    (triggerClear)="clearFilter()"
    [searchInput]="listfilters.Filter"
    placeholder="Filter"
  ></search-panel>

  <div class="row">
    <div class="col-12 col-md-6 col-lg-5 mt-3">
      <a [routerLink]="['./add']">Create new school</a>
    </div>
  </div>

  <div class="row">
    <div class="col-12 schoolsArray">
      <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 tableau accountTable">
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef>School Id</th>
          <td mat-cell *matCellDef="let element" [routerLink]="['./', element.SchoolId]">
            {{ element.SchoolId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Name</th>
          <td mat-cell *matCellDef="let element">{{ element.Name }}</td>
        </ng-container>

        <ng-container matColumnDef="options" stickyEnd>
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let element" style="text-align: right">
            <mat-icon class="actionTableau">chevron_right</mat-icon>
          </td>
        </ng-container>

        <ng-container matColumnDef="deactivatedFilters">
          <th mat-header-cell *matHeaderCellDef>Deactivated Filters</th>
          <td mat-cell *matCellDef="let element">{{ element.DeactivatedFilters }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr
          mat-row
          *matRowDef="let row; columns: displayedColumns"
          [routerLink]="['./', row.SchoolId]"
          class="tableLine"
        ></tr>
      </table>

      <mat-paginator
        [pageSize]="25"
        [pageSizeOptions]="[25, 50, 100]"
        [length]="totalRows"
        [pageIndex]="listfilters.PageIndex"
        (page)="pageChange($event)"
      ></mat-paginator>
    </div>
  </div>
</div>
