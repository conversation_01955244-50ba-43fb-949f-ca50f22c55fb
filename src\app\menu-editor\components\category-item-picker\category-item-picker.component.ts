import { Component, Input, OnInit, Output, EventEmitter, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MenuEditorModule } from '../../menu-editor.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CanteenState } from 'src/app/states';
import { Store, select } from '@ngrx/store';
import { MenuEditorApiService, SpinnerService } from 'src/app/sharedServices';
import { BaseComponent, CategoryEditor, EventItem, MenuItem } from 'src/app/sharedModels';
import { Subscription } from 'rxjs';
import { menuCategories } from 'src/app/states/canteen/canteen.selectors';

@Component({
  selector: 'category-item-picker',
  standalone: true,
  imports: [
    CommonModule,
    MenuEditorModule,
    MatFormFieldModule,
    MatOptionModule,
    MatSelectModule,
    ReactiveFormsModule,
  ],
  templateUrl: './category-item-picker.component.html',
  styleUrls: ['./category-item-picker.component.scss'],
})
//TODO: event management 1
export class CategoryItemPickerComponent extends BaseComponent implements OnInit, OnDestroy {
  @Input() merchantId: number;
  @Input() linkedItems: EventItem[] = [];
  @Output() itemPress = new EventEmitter<EventItem>();
  listCategories: CategoryEditor[];
  selectedCategory: CategoryEditor;
  form: FormGroup;
  canteenSubscription: Subscription;
  menuItems: MenuItem[];
  menuItemsToDisplay: MenuItem[];

  constructor(
    protected spinnerService: SpinnerService,
    protected menuEditorAPIService: MenuEditorApiService,
    protected store: Store<{ canteen: CanteenState }>
  ) {
    super();
  }

  ngOnInit(): void {
    this.canteenSubscription = this.store
      .pipe(select(menuCategories))
      .subscribe((menuCategories: CategoryEditor[]) => {
        this.listCategories = menuCategories;
        this.selectedCategory = this.listCategories[0];
        this.createForm();
      });

    this.getMenuItems(this.merchantId);
  }

  ngOnDestroy() {
    this.canteenSubscription?.unsubscribe();
  }

  getMenuItems(canteenId: number) {
    this.menuEditorAPIService.GetItemsByCanteenAPI(canteenId).subscribe({
      next: (response: MenuItem[]) => {
        this.menuItems = response;
        this.spinnerService.stop();
        this.filterItemsByCategory();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  filterItemsByCategory() {
    if (!this.menuItems) {
      this.menuItemsToDisplay = [];
      return;
    }

    this.menuItemsToDisplay = this.menuItems.filter(mi => {
      if (
        mi.CategoryId === this.selectedCategory.MenuCategoryId
      ) {
        return mi;
      }
    });
  }

  get category() {
    return this.form.get('category');
  }

  /** Create the filter form */
  createForm() {
    this.form = new FormGroup({
      category: new FormControl(this.selectedCategory.MenuCategoryId),
    });

    this.category.valueChanges.subscribe(val => {
      let index = this.listCategories.findIndex(i => i.MenuCategoryId == val);
      if (index >= 0) {
        this.selectedCategory = this.listCategories[index];
        this.filterItemsByCategory();
      }
    });
  }

  addItem(menuItem: MenuItem): void {

    var newitem : EventItem = {
      menuItemId: menuItem.MenuItemId,
      name: menuItem.Name,
      imageUrl: '',
      price: menuItem.Price,
      options: [],
      stock: 0
    }

    this.itemPress.emit(newitem);
  }
}
