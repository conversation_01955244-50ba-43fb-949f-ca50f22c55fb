import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import * as _ from 'lodash';

// Models
import { BaseComponent, LabelFormatV2, LabelV2 } from '../../../sharedModels';

// Services
import { PrintingApiService, SpinnerService } from '../../../sharedServices';

@Component({
  selector: 'app-label-print',
  templateUrl: './label-print.component.html',
  styleUrls: ['./label-print.component.scss'],
})
export class LabelPrintComponent extends BaseComponent implements OnInit {
  listLabels: LabelV2[] = [];

  constructor(
    private router: Router,
    private printingApiService: PrintingApiService,
    private spinnerService: SpinnerService
  ) {
    super();
  }

  ngOnInit() {
    // get the orders to print
    this.spinnerService.start();

    const guid = this.printingApiService.GetPrintingGuid();

    this.printingApiService.GetOrdersToPrintByGuidAPI(guid).subscribe({
      next: (response: LabelFormatV2) => {
        this.listLabels = response.Labels;

        if (!this.listLabels || this.listLabels.length == 0) {
          this.router.navigate(['./canteen']);
        }

        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      },
    });
  }
}
