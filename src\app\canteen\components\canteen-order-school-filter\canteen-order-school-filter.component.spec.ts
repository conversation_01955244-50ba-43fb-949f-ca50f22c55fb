import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { CanteenOrderSchoolFilterComponent } from './canteen-order-school-filter.component';

describe('CanteenOrderSchoolFilterComponent', () => {
  let component: CanteenOrderSchoolFilterComponent;
  let fixture: ComponentFixture<CanteenOrderSchoolFilterComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [CanteenOrderSchoolFilterComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CanteenOrderSchoolFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
