import { SelectionModel } from '@angular/cdk/collections';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { BasePaginatorComponent } from 'src/app/sharedModels';
import { OrderWithErrorDto } from 'src/app/sharedModels/order/order-status';

const _columns = [
  'checkbox',
  'id',
  'orderDate',
  'dateCreated',
  'parent',
  'school',
  'menu',
  'status',
  'message',
];

@Component({
  selector: 'order-error-table',
  templateUrl: './order-error-table.component.html',
  styleUrls: ['./order-error-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OrderErrorTableComponent extends BasePaginatorComponent<OrderWithErrorDto> implements OnInit {
  @Input() data: OrderWithErrorDto[] = [];
  selection = new SelectionModel<OrderWithErrorDto>(true, []);

  constructor(private cd: ChangeDetectorRef) {
    super(_columns);
  }

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges) {
    for (const propName in changes) {
      switch (propName) {
        case 'data':
          this.dataSource.data = this.data;
          this.cd.detectChanges;
          break;
        default:
          break;
      }
    }
  }

  isRowSelected(row): boolean {
    return this.selection.isSelected(row);
  }

  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    let numRows = 0;

    if (this.dataSource.data) {
      numRows = this.dataSource.data.length;
    }
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach(row => this.selection.select(row));
  }
}
