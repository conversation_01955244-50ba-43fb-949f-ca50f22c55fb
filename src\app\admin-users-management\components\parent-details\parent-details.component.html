<school-panel fullHeight="true" title="Parent Details">
  <ul class="infoList">
    <li><strong class="noTextHighlight">User id:</strong>{{ parent.UserId }}</li>
    <li><strong class="noTextHighlight">First name:</strong>{{ parent.FirstName }}</li>
    <li><strong class="noTextHighlight">Last name:</strong>{{ parent.Lastname }}</li>
    <li><strong class="noTextHighlight">Role:</strong>{{ role }}</li>
    <li>
      <strong class="noTextHighlight">Status:</strong>
      <span [ngClass]="{ greenText: parent.IsActive, redText: !parent.IsActive }">{{
        parent.IsActive | userIsActivePipe
      }}</span>
    </li>
    <li><strong class="noTextHighlight">External User ID:</strong>{{ parent.ExternalUserId }}</li>
    <li><strong class="noTextHighlight">Firebase User ID:</strong>{{ parent.FirebaseUserId }}</li>
    <li>
      <strong class="noTextHighlight">Spriggy Account ID:</strong>

      <span *ngIf="parent.SpriggyAccountId; else getAccountId">{{ parent.SpriggyAccountId }}</span>

      <ng-template #getAccountId>
        <a (click)="getSpriggyAccountId()">Get missing account ID</a>
      </ng-template>
    </li>
    <li><strong class="noTextHighlight">Stripe Customer ID:</strong>{{ parent.StripeCustomerId }}</li>
    <li *ngIf="parent.Role == roles.Parent">
      <strong class="noTextHighlight">Balance:</strong>{{ parent.SpriggyBalance | currency }}
    </li>
    <li><strong class="noTextHighlight">Mobile:</strong>{{ parent.Mobile }}</li>
    <li><strong class="noTextHighlight">Email:</strong>{{ parent.Email }}</li>
  </ul>

  <div class="pb-3 d-flex">
    <div class="pr-3" *ngIf="isParent()">
      <icon-button text="Edit Details" buttonStyle="secondary" (onPress)="editUser()">
        <img src="/assets/icons/black-pencil.svg" alt="pencil" />
      </icon-button>
    </div>
    <basic-button-v2
      class="pr-3"
      *ngIf="parent.Role == roles.Parent"
      (onPress)="reconcileUser()"
      text="Reconcile User Account"
      buttonStyle="secondary"
    ></basic-button-v2>
    <basic-button-v2
      *ngIf="canArchiveParent()"
      (onPress)="archiveClicked()"
      text="Archive User"
      buttonStyle="archive"
    ></basic-button-v2>
  </div>
</school-panel>
