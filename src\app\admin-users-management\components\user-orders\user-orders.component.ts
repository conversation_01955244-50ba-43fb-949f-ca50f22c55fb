import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import * as _ from 'lodash';

// Models
import { ArrayFilter, PaginatorChange, RefinedOrder, UserCashless } from 'src/app/sharedModels';

// Ngrx
import { select, Store } from '@ngrx/store';
import { UserManagementState } from 'src/app/states/user-management/user-management-state.interface';
import {
  OrdersResultsSelector,
  SelectedUserSelector,
  LoadingSelector,
} from 'src/app/states/user-management/user-management.selectors';
import { SearchOrders } from 'src/app/states/user-management/user-management.actions';
import { SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'user-orders',
  templateUrl: './user-orders.component.html',
  styleUrls: ['./user-orders.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserOrdersComponent implements OnInit, OnDestroy {
  ordersSubscription: Subscription;
  userSubscription: Subscription;
  loadingSubscription: Subscription;
  orders: RefinedOrder[];
  listFilters: ArrayFilter;
  user: UserCashless;
  isChild: boolean;
  requestData: boolean;

  constructor(
    private store: Store<{ userManagement: UserManagementState }>,
    private cd: ChangeDetectorRef,
    private spinnerService: SpinnerService
  ) {}

  ngOnInit(): void {
    this.userSubscription = this.store.pipe(select(SelectedUserSelector)).subscribe(userRes => {
      if (userRes) {
        this.user = userRes;
        this.cd.markForCheck();
      }
    });

    this.ordersSubscription = this.store
      .pipe(select(OrdersResultsSelector))
      .subscribe((results: RefinedOrder[]) => {
        this.orders = results;
        this.cd.markForCheck();
      });

    this.loadingSubscription = this.store.pipe(select(LoadingSelector)).subscribe((loading: boolean) => {
      if (loading) {
        this.spinnerService.start();
        return;
      }
      if (this.requestData) {
        this.requestData = false;
        this.spinnerService.stop();
      }
    });

    //init search filters
    this.listFilters = new ArrayFilter();
    this.listFilters.Filter = '';
    this.listFilters.NumberRows = 25;
    this.listFilters.PageIndex = 0;
  }

  ngOnDestroy(): void {
    this.ordersSubscription?.unsubscribe();
    this.userSubscription?.unsubscribe();
    this.loadingSubscription?.unsubscribe();
  }

  pageChanged(page: PaginatorChange): void {
    if (page !== null) {
      let tempListFilters = _.clone(this.listFilters);
      tempListFilters.PageIndex = page.pageIndex;

      if (tempListFilters.NumberRows != page.numberRows) {
        tempListFilters.PageIndex = 0;
      }
      tempListFilters.NumberRows = page.numberRows;
      this.listFilters = tempListFilters;
    }
    this.requestData = true;
    this._triggerSearch(this.listFilters);
  }

  private _triggerSearch(searchFilters: ArrayFilter): void {
    this.store.dispatch(SearchOrders({ listFilters: searchFilters, userId: this.user.UserId }));
  }

  sortChange(event): void {
    this.listFilters.PageIndex = 0;
    this.listFilters.SortBy = event.active;
    this.listFilters.SortDirection = event.direction;
  }
}
