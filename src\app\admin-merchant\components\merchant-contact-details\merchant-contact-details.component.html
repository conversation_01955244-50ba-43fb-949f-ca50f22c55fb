<div class="merchant-section">
  <div [ngClass]="{ disableCoverWhite: disableMode && !editDetailsMode }"></div>
  <div class="details-header">
    <h4>Contact Details</h4>
    <button *ngIf="!editDetailsMode" class="editBtn" (click)="triggerEdit()">
      <img class="editIcon" src="assets/icons/orange-pencil.svg" alt="edit symbol" />
    </button>
  </div>

  <hr class="details-divider" />

  <div class="top-margin">
    <div *ngIf="!editDetailsMode && contactDetails">
      <ul>
        <li><strong>Name: </strong>{{ contactDetails.ownerFirstName }} {{ contactDetails.ownerLastName }}</li>
        <li><strong>Email: </strong>{{ contactDetails.ownerEmail }}</li>
        <li><strong>Mobile: </strong>{{ contactDetails.ownerMobile }}</li>
      </ul>
    </div>

    <!-- Edit Section -->
    <div *ngIf="editDetailsMode" class="top-margin">
      <form class="cashlessForm" [formGroup]="formGroup">
        <div class="editInput">
          <input-text
            placeholder="First Name"
            formControlName="firstName"
            [error]="firstName.invalid ? this.invalidValueError : null"
          ></input-text>
          <input-text
            placeholder="Last Name"
            formControlName="lastName"
            [error]="lastName.invalid ? this.invalidValueError : null"
          ></input-text>
          <input-text
            placeholder="Email"
            formControlName="email"
            [error]="email.invalid ? this.invalidValueError : null"
          ></input-text>
          <input-text
            placeholder="Mobile"
            formControlName="phone"
            [error]="phone.invalid ? this.invalidValueError : null"
          ></input-text>
        </div>
      </form>

      <div class="editBtnContainer">
        <button class="saveBtn" (click)="saveContactDetails()" [disabled]="this.isFormDisabled()">
          Save
        </button>
        <button class="cancelBtn" (click)="cancelEditPopup()">Cancel</button>
      </div>
    </div>
  </div>
</div>
