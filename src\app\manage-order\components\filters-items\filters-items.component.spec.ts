import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { FiltersItemsComponent } from './filters-items.component';

describe('FiltersItemsComponent', () => {
  let component: FiltersItemsComponent;
  let fixture: ComponentFixture<FiltersItemsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [FiltersItemsComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FiltersItemsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
