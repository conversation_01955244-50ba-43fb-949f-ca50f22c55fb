@import '../../../../styles/cashless-theme.scss';

mat-expansion-panel-header {
  height: 45px !important;
}

mat-panel-title {
  font-size: 14px;
  font-family: Arial, Helvetica, sans-serif;
  align-items: center;
}

mat-panel-description {
  justify-content: right;

  & span {
    display: inline-block;
    font-family: Arial, Helvetica, sans-serif;
    background-color: $grey-6;
    border-radius: 8px;
    color: black;
    padding: 8px 5px 8px 5px;
    font-size: 12px;
    font-weight: 600;
  }
}

.blockAction {
  margin-top: 30px;

  & a {
    display: inline-block;
    margin-right: 14px;
    color: $grey-7;

    &.active {
      cursor: pointer;
      color: $orange-2;
    }
  }
}
