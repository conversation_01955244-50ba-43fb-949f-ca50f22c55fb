<div class="container-fluid sticky">
  <div class="row user-header">
    <div class="col-12 col-lg-6">
      <img
        class="listItemImage text-center"
        height="16"
        src="assets/icons/left-arrow.svg"
        (click)="goBack()"
      />

      <h2 *ngIf="user">
        {{ user.FirstName + ' ' + user.Lastname }}
        <span class="userid pr-1"
          >User ID:<span class="force-select">{{ user.UserId }}</span></span
        >
        <span class="role">({{ role }})</span>
      </h2>
    </div>
    <div class="col-12 col-lg-6">
      <div *ngIf="isParent" class="button-wrapper">
        <div class="balance">
          <img src="assets/icons/small-parent-wallet.svg" alt="wallet" />
          <span>${{ user.SpriggyBalance || 0 | number : '1.2-2' }}</span>
        </div>

        <icon-button text="Transfer Money" buttonStyle="primary" (onPress)="openTransferMoneyModal()">
          <img src="assets/icons/transfer-icon.svg" alt="transfer symbol" />
        </icon-button>
      </div>
      <div *ngIf="isChild" class="button-wrapper">
        <div class="school-info-wrapper">
          <span *ngIf="user.SchoolName">{{ user.SchoolName }}</span>
          <span *ngIf="user.SchoolName && user.ClassName"> : </span>
          <span *ngIf="user.ClassName">{{ user.ClassName }}</span>
        </div>
        <child-favourite-colour-bar
          *ngIf="user?.AllowCanteenToOrder"
          [colour]="user.FavouriteColour"
        ></child-favourite-colour-bar>

        <icon-button text="Create Order" buttonStyle="primary" (onPress)="createOrder()">
          <img src="assets/icons/white-plus.svg" alt="plus symbol" />
        </icon-button>
      </div>
    </div>
  </div>
</div>

<router-outlet></router-outlet>
