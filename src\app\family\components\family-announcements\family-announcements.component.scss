@import '../../../../styles/cashless-theme.scss';

.announcements {
  padding-top: 10px;
}

.announcementContainer {
  background-color: white;
  border: 1px solid #cbdffb;
  box-sizing: border-box;
  border-radius: 12px;
  padding: 16px 14px 16px 16px;
  margin-bottom: 8px;

  &.alert {
    margin-top: 20px;
    border: 1px solid $orange-3;
  }
}

.title {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.titleText {
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  line-height: 24px;
  color: #2f80ed;
  margin: 0;
  margin-left: 9px;

  &.alert {
    color: $orange-2;
  }
}

.description {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 7px;
}
.descriptionText {
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 28px;
  letter-spacing: -0.01em;
  color: #4f4f4f;
  margin: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.descriptionTextOpened {
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 28px;
  letter-spacing: -0.01em;
  color: #4f4f4f;
  margin: 0;
  white-space: pre-line;
}

.moreText {
  min-width: 80px;
  margin: 0;
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 28px;
  letter-spacing: -0.02em;
  color: rgba(47, 128, 237, 0.75);
  text-align: right;
}

.topLink {
  padding: 5px;
  margin-left: 7px;
}
