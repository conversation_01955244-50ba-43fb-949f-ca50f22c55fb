<div class="row transactions-wrapper" *ngIf="dataSource.data?.length > 0; else noTransactions">
  <div
    [ngClass]="{
      'col-12': !selectedTransactionId || selectedTransactionId == 0,
      'col-8 leftPanel': selectedTransactionId > 0
    }"
  >
    <table mat-table class="showRowHover" [dataSource]="dataSource">
      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef>ID</th>
        <td mat-cell *matCellDef="let element">{{ element.transactionId }}</td>
      </ng-container>

      <ng-container matColumnDef="type">
        <th mat-header-cell *matHeaderCellDef>Type</th>
        <td mat-cell *matCellDef="let element">{{ element.description }}</td>
      </ng-container>

      <ng-container matColumnDef="date">
        <th mat-header-cell *matHeaderCellDef>Date</th>
        <td mat-cell *matCellDef="let element">{{ element.dateCreatedUtc | date : 'MMM d, y, h:mm a' }}</td>
      </ng-container>

      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef>Amount</th>
        <td mat-cell *matCellDef="let element">
          <transaction-amount [isDebit]="element.debit" [amount]="element.amount"></transaction-amount>
        </td>
      </ng-container>

      <ng-container matColumnDef="updatedBalance">
        <th mat-header-cell *matHeaderCellDef>Updated Balance</th>
        <td mat-cell *matCellDef="let element">{{ element.currentBalance | currency }}</td>
      </ng-container>

      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let element">
          <span *ngIf="!element.declined">Approved</span>
          <span *ngIf="element.declined" class="declinedText">Declined</span>
        </td>
      </ng-container>

      <ng-container matColumnDef="error">
        <th mat-header-cell *matHeaderCellDef>Error</th>
        <td mat-cell *matCellDef="let element">
          <span *ngIf="element.exceptionThrown">
            <img src="assets/icons/black-error.svg" alt="error" />
          </span>
          <span *ngIf="!element.exceptionThrown"> - </span>
        </td>
      </ng-container>

      <ng-container matColumnDef="message">
        <th mat-header-cell *matHeaderCellDef>Message</th>
        <td mat-cell *matCellDef="let element" class="response">{{ element.response }}</td>
      </ng-container>

      <ng-container matColumnDef="options" stickyEnd>
        <th mat-header-cell *matHeaderCellDef class="option-column">
          <a *ngIf="data" class="download-link" (click)="downloadCsv()"
            >Download transactions ({{ data.length }})</a
          >
        </th>
        <td mat-cell *matCellDef="let element"></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr
        mat-row
        *matRowDef="let row; columns: displayedColumns"
        (click)="clickRow(row)"
        [ngClass]="{ rowSelected: selectedTransactionId == row.transactionId }"
      ></tr>
    </table>
  </div>
  <div *ngIf="selectedTransactionId > 0" class="col-4 rightPanel">
    <div class="detailsSelectedRow">
      <div class="row">
        <div class="col-12">
          <div class="header">
            <h3>Payment ID: {{ selectedTransaction.transactionId }}</h3>
            <img src="assets/icons/cross.svg" alt="cross" (click)="closeSideBar()" />
          </div>

          <h4>{{ selectedTransaction.description }}</h4>

          <ul class="infoList">
            <li>
              <strong>Amount:</strong>
              <transaction-amount
                [isDebit]="selectedTransaction.debit"
                [amount]="selectedTransaction.amount"
              ></transaction-amount>
            </li>
            <li>
              <strong>Status: </strong>
              <span *ngIf="selectedTransaction.declined">Declined</span>
              <span *ngIf="!selectedTransaction.declined">Approved</span>
              <span *ngIf="selectedTransaction.exceptionThrown" class="status-error">
                (An error occured)</span
              >
            </li>
            <li>
              <strong>Updated balance:</strong> ${{ selectedTransaction.currentBalance | number : '1.2-2' }}
            </li>
            <li><strong>Date:</strong> {{ selectedTransaction.dateCreatedUtc | date : 'MMM d, y' }}</li>
            <li><strong>Time:</strong> {{ selectedTransaction.dateCreatedUtc | date : 'h:mm a' }}</li>
            <li *ngIf="selectedTransaction.reason">
              <strong>Reason:</strong> {{ selectedTransaction.reason }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #noTransactions>
  <no-data-table-row text="No transactions"></no-data-table-row>
</ng-template>
