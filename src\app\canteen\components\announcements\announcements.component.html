<div class="col-12">
  <div class="col-12 col-md-8">
    <nav-back-button (navBack)="GoBackClick()" text="Go Back" class="backButton"></nav-back-button>
    <div class="col-12">
      <notice-header
        title="Announcement"
        (schoolChanged)="OnSchoolSelect($event)"
        (merchantChanged)="OnMerchantChanged($event)"
        (openForm)="AddNotice()"
      ></notice-header>
    </div>
  </div>

  <div class="col-12 row infoBlock">
    <div class="col-12 col-md-8">
      <notice-table
        [tableData]="noticeData"
        type="NoticeType.Announcement"
        (selectRow)="openEditForm($event)"
      ></notice-table>
    </div>

    <div class="col-12 col-md-4" [ngClass]="{ invisible: !showNoticeForm }">
      <div class="cardWrapper">
        <div class="crossIconWrapper">
          <a (click)="hideForm()" class="closeBtn">
            <img src="assets/icons/cross.svg" />
          </a>
        </div>

        <form [formGroup]="form" class="cashlessForm">
          <mat-form-field appearance="outline">
            <mat-label>Title</mat-label>
            <input
              maxlength="50"
              matInput
              placeholder="Enter title of announcement"
              formControlName="title"
              type="text"
            />
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Description (optional)</mat-label>
            <textarea
              maxlength="200"
              rows="1"
              matInput
              placeholder="Add a description"
              formControlName="description"
              type="text"
              class="description"
              #description
              rows="3"
            ></textarea>
            <mat-hint align="end">{{ description.value.length }} / 200</mat-hint>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>End date (optional)</mat-label>
            <input matInput [matDatepicker]="picker1" formControlName="endDate" [min]="date" readonly />
            <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
          </mat-form-field>

          <mat-checkbox formControlName="isActive">
            <p class="checkboxLabel">Active</p>
          </mat-checkbox>
          <div class="separator"></div>

          <!-- validation explain -->
          <div
            *ngIf="selectedNotice && selectedNotice.Status == NoticeStatusEnum.Refused"
            class="validationExplanation"
          >
            <h4>Validation explanation</h4>
            <p>{{ selectedNotice.ValidationDescription }}</p>
          </div>

          <form-buttons
            (saveEvent)="saveNotice()"
            (cancelEvent)="hideForm()"
            (deleteEvent)="deleteNoticeCheck()"
            [disableSaveButton]="IsSubmitButtonActive()"
            [showDeleteButton]="showDeleteButton()"
          ></form-buttons>
        </form>
      </div>
    </div>
  </div>
</div>
