import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';

import { SpinnerService, MerchantService } from '../../../sharedServices';

import { BasePaginatorComponent, Merchant } from '../../../sharedModels';

const _columns = ['id', 'firstName', 'lastName', 'phone', 'email', 'select'];

@Component({
  selector: 'app-create-merchant-search',
  templateUrl: './create-merchant-search.component.html',
  styleUrls: ['./create-merchant-search.component.scss'],
})
export class CreateMerchantSearchComponent
  extends BasePaginatorComponent<Merchant>
  implements OnInit, OnDestroy
{
  currentRoute: any;
  private routeSubscription: Subscription;

  constructor(
    private spinnerService: SpinnerService,
    private router: Router,
    private route: ActivatedRoute,
    private merchantService: MerchantService
  ) {
    super(_columns);

    //get current route
    this.routeSubscription = router.events.subscribe(route => (this.currentRoute = route));
  }

  ngOnInit() {
    // get current filters
    this.listfilters = this.merchantService.getMerchantSearchFilters();
    this.initFilters();

    if (this.listfilters?.Filter) {
      this._requestData();
    }
  }

  ngOnDestroy(): void {
    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }

    //clear search filter if navigating off search result pages
    if (!this.currentRoute.url.includes('createmerchant')) {
      this.clearFilter();
    }

    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }

  GoBackClick() {
    this.spinnerService.start();
    this.router.navigate(['./admin/merchants']);
  }

  clearFilter() {
    this.clearFiltersAndResults();
    this.merchantService.setMerchantSearchFilters(this.listfilters);
  }

  fetchData(searchInput: string) {
    this.listfilters.Filter = searchInput;
    this._requestData();
  }

  /** Call for data */
  private _requestData() {
    this.spinnerService.start();

    // save current filters
    this.merchantService.setMerchantSearchFilters(this.listfilters);

    this.merchantService.GetCreateMerchantSearchResults(this.listfilters.Filter).subscribe({
      next: (res: any) => {
        this._ProcessResponseMerchants(res);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  /** Process the list of users to be used in the search results */
  private _ProcessResponseMerchants(response: Merchant[]) {
    if (response) {
      this.listObjects = response;

      if (this.listObjects && this.listObjects.length > 0) {
        this.totalRows = this.listObjects[0].TotalRows;
        this.showResults();
      } else {
        this.totalRows = 0;
      }
    } else {
      this.noSearchResults(this.listfilters.Filter);
    }
    this.dataSource.data = this.listObjects;
    //Stop spinner
    this.spinnerService.stop();
  }
}
