import { Component, Input, Output, EventEmitter } from '@angular/core';
import { InvoiceDataForFiltering, InvoiceToExport, InvoiceType } from 'src/app/sharedModels';

@Component({
  selector: 'weekly-invoice-buttons',
  templateUrl: './weekly-invoice-buttons.component.html',
  styleUrls: ['./weekly-invoice-buttons.component.scss'],
})
export class WeeklyInvoiceButtonsComponent {
  @Input() generatedInvoiceList: InvoiceDataForFiltering[];
  @Input() selectedWeek: string;
  @Output() getWeeklyReport: EventEmitter<InvoiceToExport> = new EventEmitter();
  invoiceForSelectedWeek: InvoiceDataForFiltering;
  InvoiceType = InvoiceType;

  ngOnChanges() {
    this.invoiceForSelectedWeek = this.getSelectedWeekInvoice();
  }

  getSelectedWeekInvoice(): InvoiceDataForFiltering {
    let index: number = this.generatedInvoiceList?.findIndex(
      existingInvoice => existingInvoice.key === this.selectedWeek
    );

    return index >= 0 ? this.generatedInvoiceList[index] : null;
  }

  buttonPressed(invoiceType: InvoiceType): void {
    const data: InvoiceToExport = { invoiceType, invoiceId: this.getInvoiceId() };
    this.getWeeklyReport.emit(data);
  }

  getInvoiceId() {
    return this.invoiceForSelectedWeek ? this.invoiceForSelectedWeek.invoiceId : null;
  }
}
