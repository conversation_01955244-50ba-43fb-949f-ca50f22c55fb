@import '../../../../styles/cashless-theme.scss';

.row-container {
  background-color: white;
  border-radius: 20px;
  padding: 20px;
  border: 1px $grey-2 solid;

  h3,
  p {
    margin: 0;
  }

  h1{
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 28px;
  }

  .date-container {
    display: flex;
    align-items: center;
    justify-self: flex-start;
  }

  .button-container {
    text-align: right;
  }
}

h4{
  margin-bottom: 0;

  & span {
    // font-size: 14px;
    //color: #b20101;
    padding-left: 10px;
    color: #5B5E70;
  }
}

.description {
  color: #5B5E70;
}

.deleteLink {
  position: relative;
  padding: 6px 16px;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  line-height: 18px;
  color: #b20101;

  &.disabled {
    color: $grey-7;
    cursor: default;
  }

  &.active:hover {
    text-decoration: underline;
  }
}