import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { SPRIGGY_SCHOOLS_TERMS_LINK } from 'src/constants';

import { UserCashless, BaseComponent } from '../../../sharedModels';
import { UserService } from '../../../sharedServices';
import { PayService } from '../../../sharedServices';

import { trigger, transition, useAnimation } from '@angular/animations';
import { fadeIn } from 'ng-animate';

import { Subscription } from 'rxjs';
import { GetBalanceRequest } from 'src/app/sharedModels/pay/getBalanceRequest';
import { FamilyState } from 'src/app/states';
import { Store, select } from '@ngrx/store';
import { connectedUser } from 'src/app/states/user/user.selectors';

@Component({
  selector: 'app-account',
  templateUrl: './account.component.html',
  styleUrls: ['./account.component.scss'],
  animations: [
    trigger('bounce', [
      transition(
        '* => *',
        useAnimation(fadeIn, {
          // Set the duration to 5seconds and delay to 2seconds
          params: { timing: 0.5, delay: 0 },
        })
      ),
    ]),
  ],
})
export class AccountComponent extends BaseComponent implements OnInit, OnDestroy {
  private connectedUserSubscription: Subscription;
  accountForm: FormGroup;
  bounce: any;
  getBalanceRequest: GetBalanceRequest;
  accountBalance: number;
  subscriptionBalance: Subscription;
  deactivatedUser: boolean = false;
  isDesktop: boolean = false;
  termsLink: string = SPRIGGY_SCHOOLS_TERMS_LINK;

  constructor(
    private userService: UserService,
    private router: Router,
    private payService: PayService,
    private store: Store<{ family: FamilyState }>
  ) {
    super();
  }

  ngOnInit() {
    this.subscriptionBalance = this.payService
      .SubscribeBalanceUpdate()
      .subscribe(newBalance => (this.accountBalance = newBalance));

    this.connectedUserSubscription = this.store
      .pipe(select(connectedUser))
      .subscribe((user: UserCashless) => {
        this.deactivatedUser = !user.IsActive;
      });

    this.payService.UpdateBalance();
  }

  ngOnDestroy() {
    if (this.subscriptionBalance) {
      this.subscriptionBalance.unsubscribe();
    }

    if (this.connectedUserSubscription) {
      this.connectedUserSubscription.unsubscribe();
    }
  }

  SignOut(): void {
    this.userService.logout();
  }

  TopUp() {
    this.router.navigate(['family/account/account-topup']);
  }
}
