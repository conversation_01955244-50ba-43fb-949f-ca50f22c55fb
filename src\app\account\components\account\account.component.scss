@import '../../../../styles/cashless-breakpoints.scss';
@import '../../../../styles/cashless-font.scss';
@import '../../../../styles/cashless-theme.scss';

.wrapperContainer {
  margin-left: 0;
  margin-right: 0;
  @media (min-width: $breakpoint-md) {
    margin-left: 42px;
    margin-right: 32px;
  }
}

.accountTitle {
  font-size: 22px;
  font-family: 'bariol_bold';
  margin-left: 8px;
  margin-top: 42px;
  color: $charcoal-1;
  @media (min-width: $breakpoint-md) {
    margin-left: 0;
  }
}

.balance {
  background-color: white;
  border-radius: 12px;
}

.balanceAmount {
  font-style: 'bariol_regular';
  font-size: 44px;
  border-radius: 12px;
  color: #333b44;
  margin-top: auto;
  margin-bottom: auto;
}

.cardBalance {
  padding: 28px;
}

.PrimaryButton {
  max-width: 100%;
  margin-top: 24px;
  @media (min-width: $breakpoint-md) {
    max-width: 159px;
    margin-top: auto;
  }
}

.PrimaryButtonTest {
  max-width: 100%;
  margin-top: 24px;
  @media (min-width: $breakpoint-md) {
    max-width: 300px;
    margin-top: auto;
  }
}

.listItem {
  height: 72px;
  background: white;
  border-style: none;
  color: $charcoal-1;
  cursor: pointer;
  font-family: 'bariol_regular';
  font-size: 20px;
  margin-top: auto;
  margin-bottom: auto;
  height: 72px;
  width: 100%;
  text-align: start;
  border-width: 1px;
  border-color: #dddddd;
  display: flex;
  outline: none;
  align-items: center;
}

.listItemImage {
  margin-left: 16px;
  margin-right: 21px;

  &.billing {
    margin-left: 12px;
  }
}

.listItemText {
  margin-top: auto;
  margin-bottom: auto;
  vertical-align: middle;
}
.top {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-style: solid;
}

.bottom {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  // border-top-style: solid;
}

.single {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border-bottom-style: solid;
}

.hidden {
  display: none;
}

.appLinks {
  margin-top: 40px;

  & a {
    margin-left: 10px;
  }
}
