<div class="col-12">
  <nav-back-button
    smallText="true"
    (navBack)="goBack()"
    text="Go Back"
    class="backButton"
    smallFont="true"
    noPadding="true"
  ></nav-back-button>


  <mat-stepper linear #stepper class="stepper">
    <mat-step [stepControl]="formGroup">

      <ng-template matStepLabel>Event details</ng-template>

      <div class="row justify-content-center">
        <div class="col-12 col-md-9 col-xl-6" style="text-align: center;">
          <div class="form-container">
            <h3 class="pb-3">Event</h3>
            <select-event-template [eventTypes]="eventTypes" (templateChanged)="templateChanged($event)"></select-event-template>
            <event-form (saveEvent)="UpsertEvent($event, stepper)" [schoolClasses]="schoolClasses" [selectedTemplate]="selectedTemplate"></event-form>

            <div class="stepper-nav-button">
              <basic-button-v2 *ngIf="schoolEvent.SchoolEventId > 0"
                class="pr-2"
                (onPress)="stepper.next()"
                text="Next"
                buttonStyle="secondaryOrange"
              ></basic-button-v2>
            </div>
          </div>

        </div>
      </div>

  
    </mat-step>
    <mat-step >

        <ng-template matStepLabel>Image</ng-template>

        <div class="row justify-content-center">
          <div class="col-12 col-md-7 col-xl-4" style="text-align: center;">
            <div class="form-container">
              <h3 class="pb-3">Image</h3>
              <event-image [eventId]="schoolEvent.SchoolEventId" [imageUrl]="schoolEvent.ImageUrl"></event-image>

              <div class="stepper-nav-button">
                <!-- <basic-button-v2
                  class="pr-2"
                  (onPress)="stepper.previous()"
                  text="Back"
                  buttonStyle="third"
                ></basic-button-v2> -->
                <basic-button-v2
                  class="pr-2"
                  (onPress)="stepper.next()"
                  text="Next"
                  buttonStyle="secondaryOrange"
                ></basic-button-v2>
              </div>
            </div>
          </div>
        </div>

    </mat-step>
    <mat-step>
      <ng-template matStepLabel>Items</ng-template>

      <div class="row" *ngIf="selectedTemplate && selectedTemplate.items">
        <div class="col-12">
          <h4>We recommend to add or create the following items for the template {{selectedTemplate.title}}</h4>
        </div>
        <div class="col-12">

          <div class="cardItemTemplate" *ngFor="let item of selectedTemplate.items">
            <div class="image">
              <item-image url="" [isSmall]="true"></item-image>
            </div>
            <div>
              <h4>{{item.name}}</h4>
              <p>{{item.description}}</p>
            </div>
           </div>
        </div>
      </div>

      <div class="row justify-content-center">
        <div class="col-12">
          <div class="form-container">

              <event-items
              [items]="[]"
              [merchantId]="schoolEvent.MerchantId"
              [eventId]="schoolEvent.SchoolEventId"
              [eventIsPublish]="schoolEvent.IsActive"
              [isEditFlow]="false"
            ></event-items>

            <div class="stepper-nav-button">
              <basic-button-v2
                class="pr-2"
                (onPress)="stepper.previous()"
                text="Back"
                buttonStyle="third"
              ></basic-button-v2>
              <basic-button-v2
              class="pr-2"
              (onPress)="stepper.next()"
              text="Next"
              buttonStyle="secondaryOrange"
            ></basic-button-v2>
            </div>
          </div>
        </div>
      </div>

    </mat-step>
    <mat-step>
      <ng-template matStepLabel>Done</ng-template>

      <div class="row justify-content-center">
        <div class="col-12 col-md-6 col-xl-4">
          <div class="form-container pt-4">
             
            <app-green-check></app-green-check>

            <div style="text-align: center;" class="mb-4">
              <p>
                Event Created
              </p>
                <basic-button-v2
                class="pr-2"
                text="Go to Event"
                buttonStyle="primaryOrange"
                (onPress)="navToSelectedEvent()" 
              ></basic-button-v2>
            </div>

            <!-- <div class="stepper-nav-button">
                <basic-button-v2
                class="pr-2"
                (onPress)="stepper.reset()"
                text="Create another event"
                buttonStyle="secondary"
                  ></basic-button-v2>
                <basic-button-v2
                  class="pr-2"
                  text="Go to Event"
                  buttonStyle="primary"
                ></basic-button-v2>
            </div> -->
          </div>
        </div>
      </div>

    </mat-step>
  </mat-stepper>

</div>


