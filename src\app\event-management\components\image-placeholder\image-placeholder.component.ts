import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'event-image-placeholder',
  templateUrl: './image-placeholder.component.html',
  styleUrls: ['./image-placeholder.component.scss'],
  standalone: true,
  imports:[CommonModule]
})
export class ImagePlaceholderComponent implements OnInit {
  @Input() small: boolean = false;
  imageSize = 90;

  constructor(){

  }

  ngOnInit(): void {
    if(this.small){
      this.imageSize = 70;
    }
  }
}
