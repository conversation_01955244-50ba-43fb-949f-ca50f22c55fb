import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { Observable } from 'rxjs';

// models
import { AdminSchoolFeatures } from 'src/app/sharedModels';

// service
import { SchoolFeatureService } from 'src/app/sharedServices';

export const ListFeaturesResolver: ResolveFn<any> = (
  route: ActivatedRouteSnapshot
): Observable<AdminSchoolFeatures> => {
  const schoolFeatureService = inject(SchoolFeatureService);
  let id = route.params['id'];

  if (id == null || id == undefined) {
    id = route.parent.params['id'];
  }

  return schoolFeatureService.GetFeaturesBySchoolApi(id);
};
