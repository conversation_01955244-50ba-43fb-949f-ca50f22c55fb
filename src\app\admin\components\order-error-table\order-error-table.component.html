<div>
  <table mat-table class="showRowHover" [dataSource]="dataSource">
    <ng-container matColumnDef="top">
      <th mat-header-cell *matHeaderCellDef [attr.colspan]="9">
        <span>{{ dataSource.data.length || 0 | orderErrorTableHeading : selection.selected.length }}</span>
        <span *ngIf="selection?.selected?.length" class="ml-3"
          >
          <!-- <basic-button-v2
            (onPress)="processPayment()"
            text="Try payment again"
            buttonStyle="primary"
          ></basic-button-v2> -->
      </span>
      </th>
    </ng-container>

    <ng-container matColumnDef="checkbox">
      <th mat-header-cell *matHeaderCellDef class="error-table">
        <mat-checkbox
          (change)="$event ? masterToggle() : null"
          [checked]="selection.hasValue() && isAllSelected()"
          [indeterminate]="selection.hasValue() && !isAllSelected()"
        >
        </mat-checkbox>
      </th>
      <td mat-cell *matCellDef="let element" class="error-table">
        <mat-checkbox
          (change)="$event ? selection.toggle(element) : null"
          [checked]="selection.isSelected(element)"
        >
        </mat-checkbox>
      </td>
    </ng-container>

    <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef>ID</th>
      <td mat-cell *matCellDef="let element">{{ element.orderId }}</td>
    </ng-container>

    <ng-container matColumnDef="orderDate">
      <th mat-header-cell *matHeaderCellDef>Order date</th>
      <td mat-cell *matCellDef="let element">{{ element.orderDate | date : 'longDate' }}</td>
    </ng-container>

    <ng-container matColumnDef="dateCreated">
      <th mat-header-cell *matHeaderCellDef>Date created</th>
      <td mat-cell *matCellDef="let element">{{ element.dateCreatedUtc | date : 'medium' }}</td>
    </ng-container>

    <ng-container matColumnDef="parent">
      <th mat-header-cell *matHeaderCellDef>Parent</th>
      <td mat-cell *matCellDef="let element">
        <a class="link" routerLink="../users/{{ element.parentId }}/profile">{{ element.parentName }}</a>
      </td>
    </ng-container>

    <ng-container matColumnDef="school">
      <th mat-header-cell *matHeaderCellDef>School</th>
      <td mat-cell *matCellDef="let element">{{ element.school }}</td>
    </ng-container>

    <ng-container matColumnDef="cutOffTime">
      <th mat-header-cell *matHeaderCellDef>Cut off</th>
      <td mat-cell *matCellDef="let element">{{ element.cutOffTime }}</td>
    </ng-container>

    <ng-container matColumnDef="menu">
      <th mat-header-cell *matHeaderCellDef>Menu Type</th>
      <td mat-cell *matCellDef="let element">{{ element.menu }}</td>
    </ng-container>

    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>Status</th>
      <td mat-cell *matCellDef="let element">
        <error-status-bar *ngFor="let err of element.errors" [errorText]="err.errorType"></error-status-bar>
      </td>
    </ng-container>

    <ng-container matColumnDef="message">
      <th mat-header-cell *matHeaderCellDef>Message</th>
      <td mat-cell *matCellDef="let element" class="message-text">
        <p *ngFor="let err of element.errors">{{err.errorMessage}}</p>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="['top']"></tr>
    <div *ngIf="dataSource.data?.length > 0">
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr
        mat-row
        *matRowDef="let row; columns: displayedColumns"
        [ngClass]="{ selectedRow: isRowSelected(row) }"
      ></tr>
    </div>
  </table>
</div>
