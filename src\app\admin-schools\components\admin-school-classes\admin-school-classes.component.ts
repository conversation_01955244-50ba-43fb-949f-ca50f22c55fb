import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { BaseComponent, ListClasses } from '../../../sharedModels';
import { SpinnerService, AdminService, SchoolClassesService } from '../../../sharedServices';

@Component({
  selector: 'admin-school-classes',
  templateUrl: './admin-school-classes.component.html',
  styleUrls: ['./admin-school-classes.component.scss'],
})
export class AdminSchoolClassesComponent extends BaseComponent implements OnInit {
  isListLoaded: boolean;
  schoolId: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: SpinnerService,
    private adminService: AdminService,
    private schoolClassesService: SchoolClassesService
  ) {
    super();
  }

  ngOnInit() {
    let listTemp = this.route.snapshot.data['classes'];
    //get schoolid from url
    this.schoolId = this.route.snapshot.parent.params['id'];

    if (listTemp) {
      this.adminService.SetListClasses(listTemp.Classes);
    }

    this.isListLoaded = true;
    //Stop spinner
    this.spinnerService.stop();
  }

  /**
   * This function is called each time a class is updated via
   * the add-school-class component
   */
  loadSchoolClasses() {
    this.spinnerService.start();
    this.schoolClassesService.GetClassesBySchoolAPI(this.schoolId, true).subscribe({
      next: (response: ListClasses) => {
        this.adminService.SetListClasses(response.Classes);
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  GoBack() {
    this.spinnerService.start();
    this.router.navigate(['./admin/schools']);
  }
}
