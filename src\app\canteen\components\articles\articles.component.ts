import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { FormGroup, FormControl } from '@angular/forms';

// Models
import { NoticeBoardComponent, Notice, NoticeType } from 'src/app/sharedModels';

// Services
import { SpinnerService, NoticeService, UserService } from 'src/app/sharedServices';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-articles',
  templateUrl: './articles.component.html',
  styleUrls: ['./articles.component.scss'],
})
export class ArticlesComponent extends NoticeBoardComponent implements OnInit {
  displayedColumns: string[] = ['title', 'body', 'IsActive', 'Status'];

  constructor(
    private _location: Location,
    public spinnerService: SpinnerService,
    protected noticeService: NoticeService,
    protected userService: UserService,
    public dialog: MatDialog
  ) {
    super(noticeService, spinnerService, userService, dialog);
  }

  ngOnInit() {
    this.currentNoticeType = NoticeType.Article;
    this.CreateForm(new Notice());
  }

  GoBackClick() {
    this._location.back();
  }

  CreateForm(article: Notice) {
    this.form = new FormGroup({
      title: new FormControl(article.Title || ''),
      description: new FormControl(article.Description || ''),
      endDate: new FormControl(article.EndDate),
      isActive: new FormControl(article.IsActive === false ? false : true),
    });
  }

  AddNotice() {
    this.showNoticeForm = true;
    this.selectedNotice = null;
    this.CreateForm(new Notice());
  }

  IsSubmitButtonActive() {
    return !this.form.get('title').value || !this.form.get('description').value;
  }

  openEditForm(article: Notice) {
    this.selectedNotice = article;
    this.CreateForm(article);
    this.showNoticeForm = true;
  }

  get bodyFill() {
    return this.form.get('description').value ? true : false;
  }

  autoGrowTextZone(e) {
    if (!this.bodyFill) {
      e.target.style.height = '300px';
      return;
    }
    e.target.style.height = e.target.scrollHeight + 'px';
  }
}
