import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MerchantUserPermissionsTableComponent } from './merchant-user-permissions-table.component';

describe('MerchantUserPermissionsTableComponent', () => {
  let component: MerchantUserPermissionsTableComponent;
  let fixture: ComponentFixture<MerchantUserPermissionsTableComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [MerchantUserPermissionsTableComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MerchantUserPermissionsTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
