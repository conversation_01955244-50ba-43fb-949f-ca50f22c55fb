@import '../../../../styles/cashless-breakpoints.scss';
@import '../../../../styles/cashless-theme.scss';
@import '../../../../styles/cashless-font.scss';

.blocOrder {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  margin: auto;
  text-align: center;
  color: black;
  background-color: $grey-2;
  cursor: pointer;
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  justify-content: center;

  @media (min-width: $breakpoint-md) {
    width: 183px;
    height: 98px;
    border-radius: 8px;
    margin: 5px;
    text-align: center;
    color: black;
    cursor: pointer;
  }

  &.disabled {
    background-color: $grey-2;
    color: black;
    cursor: pointer;
    opacity: 0.5;
  }

  &.ordered {
    background: linear-gradient(120.56deg, $orange-1 4.57%, $orange-2 97.51%);
    color: white;
    cursor: pointer;
  }

  &.past {
    opacity: 0.5;
  }

  &.error {
    background-color: #fbe0e0;
    border: 1px solid red;
    color: red;
    cursor: pointer;
  }

  &.recess {
    background-color: lightgray;
    cursor: default;
  }

  &.supplies {
    height: 55px;
    width: 160px;
    padding-top: 10px;
    background-color: lightgray;
  }
}

.hidden {
  display: none;
}
