import { ChangeDetectionStrategy, Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';

// Models
import {
  BaseComponent,
  CANTEEN_CATEGORY_ICON_ARRAY,
  ConfirmModal,
  ImageUrlEnum,
  ItemCategory,
  ItemCategoryInsertRequest,
  ItemCategoryUpdateRequest,
  MerchantTypeEnum,
  PopupCategoryModel,
  UNIFORM_CATEGORY_ICON_ARRAY,
} from 'src/app/sharedModels';
import { environment } from 'src/environments/environment';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MenuEditorApiService } from 'src/app/sharedServices';

//ngrx
import { Store } from '@ngrx/store';
import { CanteenState } from 'src/app/states';
import { LoadMenuCategories } from 'src/app/states/canteen/canteen.actions';
import { DialogConfirmComponent } from 'src/app/shared/components';
import { CategoryIconComponent } from 'src/app/manage-order/components/category-icon/category-icon.component';

@Component({
  selector: 'popup-category-form',
  templateUrl: './popup-category-form.component.html',
  styleUrls: ['./manage-categories.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PopupCategoryFormComponent extends BaseComponent implements OnInit {
  form: FormGroup;
  listImages: string[] = [];
  selectedImage: string = null;
  title: string = 'New Category';
  buttonLoading: boolean = false;
  selectedCategory: ItemCategory;
  categoryIcon: CategoryIconComponent;

  constructor(
    public dialogRef: MatDialogRef<PopupCategoryFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PopupCategoryModel,
    private menuEditorService: MenuEditorApiService,
    private store: Store<{ canteen: CanteenState }>,
    public dialog: MatDialog
  ) {
    super();
    this.categoryIcon = new CategoryIconComponent();
  }

  ngOnInit(): void {
    //create deep copy of selected category to stop data being updated in parent component
    this.selectedCategory = { ...this.data.SelectedCategory };
    this.listImages = this.getCategoryIconsForMerchantType();

    if (this.selectedCategory?.MenuCategoryId > 0) {
      this.title = 'Edit Category';
      this.imageSelection(this.selectedCategory.CategoryUrl);
    }

    this.createForm();
  }

  getCategoryIconsForMerchantType(): string[] {
    const isUniformMerchant = this.data.Canteen.CanteenType === MerchantTypeEnum.Uniform;
    return isUniformMerchant ? UNIFORM_CATEGORY_ICON_ARRAY : CANTEEN_CATEGORY_ICON_ARRAY;
  }

  getUrlCategory(imageName: string): string {
    return environment.blobStorage + ImageUrlEnum.MenusSM + imageName;
  }

  closeDialog(reload: boolean): void {
    this.dialogRef.close(reload);
  }

  disableSave(): boolean {
    return this.form.invalid || !Boolean(this.selectedImage);
  }

  createForm(): void {
    this.form = new FormGroup({
      name: new FormControl(this.selectedCategory.CategoryName, Validators.required),
      sortOrder: new FormControl(this.selectedCategory.SortOrder, [Validators.required, Validators.min(1)]),
    });
  }

  get name() {
    return this.form.get('name');
  }

  get sortOrder() {
    return this.form.get('sortOrder');
  }

  isSelected(image: string): boolean {
    return image == this.selectedImage;
  }

  imageSelection(image: string): void {
    this.selectedImage = image ? this.categoryIcon.getImageNameFromFileName(image) : null;
  }

  onSubmit(): void {
    this.buttonLoading = true;
    if (this.selectedCategory.MenuCategoryId > 0) {
      this.updateCategory();
      return;
    }
    this.insertCategory();
  }

  getImageFileName(): string {
    return `${this.selectedImage}.jpg`;
  }

  updateCategory(): void {
    const request: ItemCategoryUpdateRequest = {
      CategoryName: this.name.value,
      SortOrder: this.sortOrder.value,
      CategoryUrl: this.getImageFileName(),
      CategoryId: this.selectedCategory.MenuCategoryId,
    };

    this.menuEditorService.UpdateCategoryAPI(request).subscribe({
      next: (response: ItemCategory) => {
        this.apiSuccessAction();
      },
      error: error => {
        this.apiErrorAction(error, 'Editing');
      },
    });
  }

  insertCategory(): void {
    const request: ItemCategoryInsertRequest = {
      CategoryName: this.name.value,
      SortOrder: this.sortOrder.value,
      CategoryUrl: this.getImageFileName(),
      CanteenId: this.data.Canteen.CanteenId,
    };

    this.menuEditorService.InsertCategoryAPI(request).subscribe({
      next: (response: ItemCategory) => {
        this.apiSuccessAction();
      },
      error: error => {
        this.apiErrorAction(error, 'Creating');
      },
    });
  }

  apiSuccessAction(): void {
    this.buttonLoading = false;
    this.closeDialog(true);
    this.store.dispatch(LoadMenuCategories());
  }

  apiErrorAction(error: any, keyword: string): void {
    this.buttonLoading = false;
    this.showErrorDialog(keyword);
    this.handleErrorFromService(error);
  }

  showErrorDialog(keyWord: string): void {
    let data = new ConfirmModal();
    data.Title = `Something went wrong`;
    data.Text = `${keyWord} this category was unsuccessful. Please try again.`;
    data.ConfirmButton = 'Ok';

    this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });
  }
}
