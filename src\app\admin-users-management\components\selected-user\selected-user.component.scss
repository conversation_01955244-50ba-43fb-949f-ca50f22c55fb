// import theme
@import 'src/styles/schools-theme';

.sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 65px;
  z-index: 999;
}
.user-header {
  background-color: $text-light-strong;
  box-shadow: inset 0px -1px 0px $neutral-grey-2;

  & img {
    display: inline-block;
    cursor: pointer;
  }

  & h2 {
    color: $text-dark-strong;
    font-weight: 700;
    font-size: 28px;
    line-height: 34px;
    margin: 19px 0 19px 10px;
    display: inline-block;

    & .role {
      font-weight: 500;
      color: $text-dark-soft;
      font-size: 20px;
    }

    & .userid {
      font-weight: 700;
      color: $text-dark-soft;
      font-size: 20px;

      & .force-select {
        -webkit-user-select: text; /* Chrome 49+ */
        -moz-user-select: text; /* Firefox 43+ */
        -ms-user-select: text; /* No support yet */
        user-select: text; /* Likely future */
      }
    }
  }
}

.button-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-direction: row;
  height: 100%;
  gap: 20px;
}

.balance {
  border-radius: 14px;
  padding: 6px 16px;
  border: 1px solid $neutral-grey-2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;

  & span {
    display: inline-block;
    font-size: 18px;
    line-height: 24px;
    font-weight: bolder;
    color: $text-dark-strong;
  }

  & .school-info-wrapper {
    font-size: 14px;
    line-height: 18px;
  }
}
