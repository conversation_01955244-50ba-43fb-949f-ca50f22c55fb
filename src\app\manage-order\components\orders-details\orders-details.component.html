<!-- Edit Order -->
<ng-container *ngIf="editOrderSummary; else newOrder">
  <edit-order-details [editOrderSummary]="editOrderSummary"></edit-order-details>
</ng-container>

<!-- Create Order -->
<ng-template #newOrder>
  <new-order-details
    [createOrderSummary]="createOrderSummary"
    [totalFees]="totalFees"
    [feesGroupedByStudent]="feesGroupedByStudent"
  ></new-order-details>
</ng-template>

<!-- creditExists -->
<div *ngIf="!isRefund()">
  <p class="walletBalance">
    Wallet Balance:
    <span class="subHeader">{{ accountBalance | currency }}</span>
  </p>
  <p class="chargeDesc">
    We'll deduct the
    <span *ngIf="createOrderSummary">total orders amount </span>
    <span *ngIf="editOrderSummary" class="subHeader">
      {{ editOrderSummary.priceDiff | absoluteMoneyValue }}</span
    >
    from your wallet balance.
  </p>
</div>

<!-- refund -->
<div *ngIf="editOrderSummary && isRefund()">
  <p>
    We'll refund your wallet with
    <span class="subHeader">{{ editOrderSummary.priceDiff | absoluteMoneyValue }}</span>
  </p>
</div>
