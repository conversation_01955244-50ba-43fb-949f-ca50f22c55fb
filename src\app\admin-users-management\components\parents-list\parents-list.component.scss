@import 'src/styles/schools-list';
@import 'src/styles/schools-text';

.parent-wrapper {
  height: auto;
  overflow: auto;
  display: flex;
  flex-direction: column;

  & * {
    // authorize text selection
    -webkit-user-select: text; /* Chrome 49+ */
    -moz-user-select: text; /* Firefox 43+ */
    -ms-user-select: text; /* No support yet */
    user-select: text; /* Likely future */
  }

  & .mat-mdc-table {
    width: 100%;
  }

  & table {
    width: 100%;

    & tr {
      height: min-content;
    }

    & .mat-mdc-header-row .mat-mdc-header-cell {
      border-bottom: 1px solid $neutral-grey-1;
      border-top: none;
      padding: 8px 0 8px 0;
    }

    & .mat-mdc-row .mat-mdc-cell {
      border-bottom: 1px solid $neutral-grey-1;
      border-top: none;
      padding: 8px 0 8px 0;
    }
  }
}
