import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { LinkSchoolToMerchantPageComponent } from './link-school-to-merchant-page.component';

describe('LinkSchoolToMerchantPageComponent', () => {
  let component: LinkSchoolToMerchantPageComponent;
  let fixture: ComponentFixture<LinkSchoolToMerchantPageComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [LinkSchoolToMerchantPageComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(LinkSchoolToMerchantPageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
