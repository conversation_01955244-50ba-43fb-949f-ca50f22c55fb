import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

// ngrx
import { DatePipe, KeyValue } from '@angular/common';
import { SelectListCustomMenu } from 'src/app/sharedModels/selectList';
import { ActivatedRoute } from '@angular/router';
import { OrderDashboardDto, OrderWithErrorDto } from 'src/app/sharedModels/order/order-status';
import { OrderStatusApiService, SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'app-admin-dashboard',
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdminDashboardComponent implements OnInit {
  form: FormGroup;
  private dashboarData: OrderDashboardDto = null;
  dashboarOrders: OrderWithErrorDto[] = [];
  orderDateMenuValues: KeyValue<SelectListCustomMenu, number>[] = [];
  schoolMenuValues: KeyValue<SelectListCustomMenu, number>[] = [];

  constructor(
    private activatedRoute: ActivatedRoute,
    private cd: ChangeDetectorRef,
    private datePipe: DatePipe,
    private orderStatusApiService: OrderStatusApiService,
    private spinnerService: SpinnerService
  ) {}

  ngOnInit(): void {
    this.activatedRoute.data.subscribe((data: { dashboard: OrderDashboardDto }) => {
      this.dashboarData = data.dashboard;
      this.dashboarOrders = this.dashboarData.orders;

      this.createForm();
      this.setupData();
    });
  }

  private setupData() {
    this.addDataToSchoolsDropDownMenu();
    this.addDataToOrderDateDropDownMenu();
  }

  private createForm(): void {
    this.form = new FormGroup({
      selectedDate: new FormControl(null),
      selectedSchoolId: new FormControl(null),
    });

    this.selectedDate.valueChanges.subscribe(dayOfYear => {
      //filter data by date
      if (dayOfYear && dayOfYear > 0) {
        let orderDateKey = this.orderDateMenuValues.find(f => f.value == dayOfYear);
        this.dashboarOrders = this.dashboarData.orders.filter(
          s => this.datePipe.transform(s.orderDate, 'longDate') == orderDateKey.key.LeftMenuData
        );
      } else {
        this.dashboarOrders = this.dashboarData.orders;
      }
    });

    this.selectedSchoolId.valueChanges.subscribe(schoolId => {
      //filter data by school id
      if (schoolId && schoolId > 0) {
        this.dashboarOrders = this.dashboarData.orders.filter(s => s.schoolId == schoolId);
      } else {
        this.dashboarOrders = this.dashboarData.orders;
      }
    });
  }

  private addDataToSchoolsDropDownMenu() {
    this.schoolMenuValues = [];

    if (this.dashboarData && this.dashboarData.schoolsFilter && this.dashboarData.schoolsFilter.length > 0) {
      this.dashboarData.schoolsFilter.forEach(l => {
        let menuData: SelectListCustomMenu = {
          LeftMenuData: l.label,
          RightMenuData: this.getErrorTextFroDropDown(l.additionalNumber),
        };
        this.schoolMenuValues.push({ key: menuData, value: l.id });
      });
    }
  }

  private addDataToOrderDateDropDownMenu() {
    this.orderDateMenuValues = [];

    if (
      this.dashboarData &&
      this.dashboarData.orderDatesFilter &&
      this.dashboarData.orderDatesFilter.length > 0
    ) {
      this.dashboarData.orderDatesFilter.forEach(l => {
        let menuData: SelectListCustomMenu = {
          LeftMenuData: this.datePipe.transform(l.label, 'longDate'),
          RightMenuData: this.getErrorTextFroDropDown(l.additionalNumber),
        };
        this.orderDateMenuValues.push({ key: menuData, value: l.id });
      });
    }
  }

  private getErrorTextFroDropDown(errors: number): string {
    let res = errors > 1 ? 'Errors ' : 'Error ';
    res += errors;
    return res;
  }

  RefreshDashboard(): void {
    this.spinnerService.start();

    this.orderStatusApiService.GetDashboardErrorsAPI().subscribe({
      next: (res: OrderDashboardDto) => {
        this.dashboarData = res;
        this.dashboarOrders = this.dashboarData.orders;

        this.setupData();
        this.spinnerService.stop();
        this.cd.markForCheck();
      },
      error: error => {
        this.spinnerService.stop();
        console.log('Error when refreshing the data', error);
      },
    });
  }

  schoolSelectedValueChange(schoolId: number): void {
    this.selectedSchoolId.setValue(schoolId);
  }

  dateSelectedValueChange(date): void {
    this.selectedDate.setValue(date);
  }

  get selectedDate() {
    return this.form.get('selectedDate');
  }
  get selectedSchoolId() {
    return this.form.get('selectedSchoolId');
  }
}
