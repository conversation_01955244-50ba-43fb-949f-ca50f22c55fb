<div class="col-12">
  <h3 class="optionTitle">Food Break Settings</h3>
  <br />
  <div *ngIf="hasMenu()">
    <div class="picker-container">
      <menu-list [menuList]="menuList" (menuSelectionChanged)="setSelectedMenu($event)"></menu-list>
    </div>
    <div *ngIf="selectedMenu">
      <form [formGroup]="form">
        <div *ngIf="showCustomMenuPicker" class="picker-container">
          <input-select-list
            formControlName="customMenuName"
            placeholder="Select menu name"
            [values]="customMenuNameList"
          ></input-select-list>
        </div>

        <div class="row mt-3">
          <div class="col-4">
            <h3 class="optionTitle">Cut off time</h3>
            <div class="mt-2 mb-2">Set the cut-off time that applies to each food break.</div>
            <mat-form-field appearance="outline">
              <mat-label>Cut off time</mat-label>
              <input matInput maxlength="40" formControlName="cutOffTime" type="time" />
            </mat-form-field>
          </div>
        </div>

        <div class="row mt-3">
          <div class="col-4">
            <h3 class="optionTitle">Break time</h3>
            <div class="mt-2 mb-2">Set the time that each food break starts.</div>
            <mat-form-field appearance="outline">
              <mat-label>Break time</mat-label>
              <input matInput maxlength="40" formControlName="breakTime" type="time" />
            </mat-form-field>
          </div>
        </div>

        <div class="row mt-3">
          <div class="col-4">
            <mat-checkbox
              formControlName="menuIsDisabled"
              class="checkbox"
              [checked]="!this.selectedMenu.IsActive"
              >Disable</mat-checkbox
            >
          </div>
        </div>
      </form>
      <div class="row mt-3">
        <div class="col-4">
          <basic-button text="Save" (onPress)="saveFoodBreakSettings()"></basic-button>
        </div>
      </div>
    </div>
  </div>
</div>
