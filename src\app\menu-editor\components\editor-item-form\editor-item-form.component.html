<form *ngIf="form" [formGroup]="form" class="menuItemForm" style="width: 100%;">
    <div class="row">

            <div class="col-6 col-md-4">
              <div class="formInputs">
                <mat-form-field appearance="outline">
                  <mat-label>Name</mat-label>
                  <input matInput placeholder="Name" formControlName="name" type="text" required />
                  <mat-error *ngIf="name.invalid">{{ getErrorMessage() }}</mat-error>
                </mat-form-field>
    
                <input-select-list
                  formControlName="categoryData"
                  placeholder="Category"
                  [values]="merchantCategories"
                  [error]="categoryData.invalid && getErrorMessage()"
                ></input-select-list>
    
                <mat-form-field appearance="outline">
                  <mat-label>Description</mat-label>
                  <textarea
                    matInput
                    #description
                    placeholder="Description (optional)"
                    formControlName="description"
                    maxlength="200"
                  ></textarea>
                  <mat-hint align="end">{{ description.value.length }} / 200</mat-hint>
                </mat-form-field>
    
                <div class="mt-3">
                  <mat-form-field appearance="outline">
                    <mat-label>Price</mat-label>
                    <span matPrefix>&nbsp; $ &nbsp;</span>
                    <!-- type 'text' needed to use twoDecimal directive -->
                    <input matInput placeholder="Price" type="text" formControlName="price" required twoDecimal />
                    <mat-error *ngIf="price.invalid">{{ getErrorMessage() }}</mat-error>
                  </mat-form-field>
                </div>
              </div>

              <input-checkbox formControlName="hasGst" placeholder="GST applies to this item"></input-checkbox>
            </div>

            <div class="col-1">

            </div>

              <ng-container *ngIf="!IsUniformCanteen()">

                  <div class="col-3 col-md-2">
                    <editor-checkbox-list
                      title="Dietary Labels"
                      [values]="dietaryCheckBoxValues"
                      [formGroup]="form"
                      [newTheme]="true"
                    ></editor-checkbox-list>
                  </div>
      
                  <!-- Nutritional value -->
                  <div *ngIf="nutritionalValues" class="col-3 col-md-2">
                    <h3>Nutritional value</h3>
                    <div *ngFor="let item of nutritionalValues; let i = index">
                      <mat-checkbox
                        [checked]="selectedNutritional.Name == item.Name"
                        (change)="onNutritionalSelect(item)"
                        >{{ item.Name }}</mat-checkbox
                      >
                    </div>
                  </div>

                  <div class="col-4 col-md-3">
                    <item-cut-off-time-picker
                      [form]="form"
                      [cutOffTimeVal]="item.CutOffTime"
                      [cutOffTimeTypeVal]="item.CutOffTimeType"
                      [newTheme]="true"
                    ></item-cut-off-time-picker>
                </div>

            </ng-container>
  
    </div>
    <div class="row">
      <div class="col-12 mt-2 buttons">

          <basic-button-v2
            (onPress)="onSubmit()"
            text="Create"
            buttonStyle="primaryOrange"
            [disabled]="form.invalid"
          ></basic-button-v2>
        
      </div>
    </div>
  </form>