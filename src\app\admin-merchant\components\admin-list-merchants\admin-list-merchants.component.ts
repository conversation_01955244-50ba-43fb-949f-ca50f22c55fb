import { Compo<PERSON>, OnInit, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

import {
  BasePaginatorComponent,
  Merchant,
  MerchantDetails,
  MerchantContactDetails,
  MerchantTypeEnum,
} from '../../../sharedModels';

import { SpinnerService, MerchantService } from '../../../sharedServices';

//dialog
import { MatDialog } from '@angular/material/dialog';

// ngrx
import { Subscription } from 'rxjs';

const _columns = ['name'];

@Component({
  selector: 'app-admin-list-merchants',
  templateUrl: './admin-list-merchants.component.html',
  styleUrls: ['./admin-list-merchants.component.scss'],
})
export class AdminListMerchantsComponent
  extends BasePaginatorComponent<Merchant>
  implements OnInit, OnD<PERSON>roy
{
  @ViewChild('userPermissions') userPermissionsSection: ElementRef;
  private editSubscription: Subscription;
  private routeSubscription: Subscription;
  private merchantDataSubscription: Subscription;
  form: FormGroup;
  selectedMerchant: Merchant;
  disableMode: boolean = false;
  currentRoute: any;
  merchantDetails: MerchantDetails;
  contactDetails: MerchantContactDetails;
  merchantType: MerchantTypeEnum;
  loadCount: number = 0;

  constructor(
    private spinnerService: SpinnerService,
    private route: ActivatedRoute,
    private router: Router,
    private merchantService: MerchantService,
    public dialog: MatDialog
  ) {
    super(_columns);

    //get current route
    this.routeSubscription = router.events.subscribe(route => (this.currentRoute = route));
  }

  ngOnInit() {
    this.merchantDataSubscription = this.merchantService.merchantListUpdatedEvent$.subscribe({
      next: (res: Merchant[]) => {
        this._ProcessResponseMerchants(res);

        //find newly created merchant
        let newMerchantId = this.merchantService.getNewMerchantId();
        if (newMerchantId) {
          let findNewMerchant = res.findIndex(el => el.canteenId === this.merchantService.getNewMerchantId());
          this.merchantService.setSelectedMerchant(res[findNewMerchant]);
          this.merchantService.setNewMerchantId(null);
        }

        this.selectedMerchant = this.merchantService.getSelectedMerchant();
      },
    });

    // get data from resolver
    this.route.data.subscribe(data => {
      let tempRes = data['merchants'];
      this.merchantService.setMerchantList(tempRes);
    });

    this.editSubscription = this.merchantService.editMode$.subscribe(status => {
      this.disableMode = status;
    });

    if (this.selectedMerchant) {
      this.spinnerService.start();
      this.LoadMerchantData(this.selectedMerchant.canteenId);
    }
  }

  ngOnDestroy(): void {
    if (this.editSubscription) {
      this.merchantService.setDisableMode(false);
      this.editSubscription.unsubscribe();
    }

    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }

    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }

    if (this.merchantDataSubscription) {
      this.merchantDataSubscription.unsubscribe();
    }
  }

  /** Process the list of users to be used in the component */
  private _ProcessResponseMerchants(response: Merchant[]) {
    if (response) {
      this.listObjects = response;

      if (this.listObjects && this.listObjects.length > 0) {
        this.totalRows = this.listObjects[0].TotalRows;
      } else {
        this.totalRows = 0;
      }
    } else {
      this.listObjects = [];
      this.totalRows = 0;
    }
    this.dataSource.data = this.listObjects;
    this.selectedMerchant = null;

    //Stop spinner
    this.spinnerService.stop();
  }

  selectMerchant(merchant: Merchant) {
    if (!this.selectedMerchant || merchant.canteenId != this.selectedMerchant.canteenId) {
      this.loadCount = 0;
      this.selectedMerchant = merchant;
      this.merchantService.setSelectedMerchant(merchant);
      this.LoadMerchantData(this.selectedMerchant.canteenId);
    }
  }

  //function to track the loading status of all child components
  loadCheck() {
    this.loadCount++;
    //once the 3 child components have finsihed loading - the global loading will be stopped
    if (this.loadCount >= 3) {
      if (this.selectedMerchant && this.merchantService.getUpdateMerchantUserPermissions()) {
        //scroll to User permissions table
        this.userPermissionsSection.nativeElement.scrollIntoView({ block: 'start' });
        window.scroll(0, 0);
        this.merchantService.setUpdateMerchantUserPermissions(false);
      }
      this.spinnerService.stop();
    }
  }

  LoadMerchantData(merchantId: number) {
    this.spinnerService.start();

    //Load merchant details
    this.merchantService.GetMerchant(merchantId).subscribe({
      next: (res: any) => {
        this.merchantDetails = new MerchantDetails();
        this.merchantDetails = res.merchantDetails;
        this.merchantType = res.merchantDetails.type;

        this.contactDetails = new MerchantContactDetails();
        this.contactDetails = res.contactDetails;

        //stop loading
        this.loadCheck();
      },
      error: error => {
        //stop loading
        this.loadCheck();
        this.handleErrorFromService(error);
      },
    });
  }

  createMerchantClick() {
    this.router.navigate(['./admin/merchants/createmerchant']);
  }

  financeReportClick() {
    this.router.navigate(['./admin/merchants/financeReport']);
  }
}
