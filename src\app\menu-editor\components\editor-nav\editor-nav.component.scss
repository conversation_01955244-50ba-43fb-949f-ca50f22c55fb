// @import '../../../../styles/cashless-theme.scss';

// .editorNav{
//     font-size: 20px;

//     li {
//         list-style-type: none;
//         display: inline-block;
//         padding: 5px;
//         margin-left: 10px;
//         cursor: pointer;

//         a{
//             text-decoration: none;
//             color: $orange-2;
//             border: none;

//             &.active{
//                 border-bottom: 1px solid $orange-2;
//             }
//         }
//     }
// }

@import '../../../../styles/cashless-font.scss';
@import '../../../../styles/cashless-theme.scss';

.tabEditor {
  width: 550px;
  padding: 0;
  padding-top: 30px;
  padding-bottom: 15px;
  margin: auto;
  font-size: 18px;
  font-weight: bold;

  & li {
    display: inline-block;
    margin-left: 30px;
    padding-bottom: 5px;
    text-align: center;
    color: $orange-3;
    cursor: pointer;

    &.active {
      border-bottom: 2px solid $orange-3;
    }
  }
}
