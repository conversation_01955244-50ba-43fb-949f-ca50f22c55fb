import { Component, Input, OnInit } from '@angular/core';
import { CreateOrdersSummary, StudentFee } from 'src/app/sharedModels';

@Component({
  selector: 'new-order-details',
  templateUrl: './new-order-details.component.html',
  styleUrls: ['./new-order-details.component.scss'],
})
export class NewOrderDetailsComponent implements OnInit {
  @Input() createOrderSummary: CreateOrdersSummary;
  @Input() totalFees: number;
  @Input() feesGroupedByStudent: StudentFee[];

  ngOnInit(): void {}
}
