import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// module
import { AdminMerchantRoutingModule } from './admin-merchant-routing.module';
import { AccountModule } from '../account/account.module';
import { SharedModule } from '../shared/shared.module';
import { SharedToolsModule } from '../shared-tools/shared-tools.module';
import { SchoolsButtonModule } from '../schools-button/schools-button.module';
import { SchoolsFormModule } from '../schools-form/schools-form.module';
import { SchoolsCommonModule } from '../schools-common/schools-common.module';

// material
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';

import {
  AdminListMerchantsComponent,
  MerchantSchoolSearchComponent,
  MerchantLinkedSchoolsTableComponent,
  LinkSchoolToMerchantPageComponent,
  MerchantUserFormComponent,
  MerchantUserPermissionsTableComponent,
  AddMerchantUserSearchComponent,
  CreateMerchantFormComponent,
  CreateMerchantSearchComponent,
  MerchantContactDetailsComponent,
  MerchantDetailsComponent,
  MerchantLinkedSchoolsDetailsComponent,
  SchoolHeaderComponent,
  FinanceReportComponent,
  FeeCalculatorFormComponent,
  FeeCalculatorSelectListComponent,
  FinanceReportWithHistoryComponent,
  WeeklyInvoiceButtonsComponent,
  WeeklyReportComponent,
} from './components';

@NgModule({
  declarations: [
    AdminListMerchantsComponent,
    MerchantLinkedSchoolsTableComponent,
    MerchantSchoolSearchComponent,
    LinkSchoolToMerchantPageComponent,
    MerchantUserFormComponent,
    MerchantUserPermissionsTableComponent,
    AddMerchantUserSearchComponent,
    CreateMerchantFormComponent,
    CreateMerchantSearchComponent,
    MerchantDetailsComponent,
    MerchantContactDetailsComponent,
    MerchantLinkedSchoolsDetailsComponent,
    SchoolHeaderComponent,
    FinanceReportComponent,
    FeeCalculatorFormComponent,
    FeeCalculatorFormComponent,
    FeeCalculatorSelectListComponent,
    FinanceReportWithHistoryComponent,
    WeeklyInvoiceButtonsComponent,
    WeeklyReportComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    SchoolsFormModule,
    AdminMerchantRoutingModule,
    AccountModule,
    SharedModule,
    SharedToolsModule,
    SchoolsButtonModule,
    SchoolsCommonModule,

    // material
    MatIconModule,
    MatCheckboxModule,
    MatSortModule,
    MatInputModule,
    MatCardModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatRadioModule,
    MatSelectModule,
    MatTableModule,
    MatButtonModule,
  ],
})
export class AdminMerchantModule {}
