import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import * as _ from 'lodash';

//Models
import {
  BaseComponent,
  ListClasses,
  ListSchools,
  School,
  SchoolClass,
  UserCashless,
  UpdateChildStateRequest,
} from 'src/app/sharedModels';

//Services
import {
  PhoneNumberService,
  SchoolClassesService,
  SchoolService,
  SpinnerService,
  UserService,
} from 'src/app/sharedServices';

//dialog
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeyValue } from '@angular/common';

//ngrx
import { UserState } from 'src/app/states';
import { Store } from '@ngrx/store';
import * as userActions from '../../../states/user-management/user-management.actions';

@Component({
  selector: 'app-dialog-user-details-form',
  templateUrl: './dialog-user-details-form.component.html',
  styleUrls: ['./dialog-user-details-form.component.scss'],
})
export class DialogUserDetailsFormComponent extends BaseComponent implements OnInit {
  form: FormGroup;
  invalidValueError: string = 'Invalid value entered';
  title: string;
  isParent: boolean;
  listSchools: KeyValue<string, string>[] = [];
  listClasses: KeyValue<string, string>[] = [];

  constructor(
    public dialogRef: MatDialogRef<DialogUserDetailsFormComponent>,
    private userStore: Store<{ userState: UserState }>,
    @Inject(MAT_DIALOG_DATA) public data,
    private userService: UserService,
    private spinnerService: SpinnerService,
    private schoolClassesService: SchoolClassesService,
    private schoolService: SchoolService,
    private phoneNumberService: PhoneNumberService,
    private cd: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit() {
    this.isParent = this.data.isParent;
    this.title = this.isParent ? 'Edit parent details' : 'Edit child details';

    if (this.isParent) {
      this._createParentForm();
      return;
    }
    this._createChildForm();
    this._GetListSchools();
    this._GetListClasses(this.data.SchoolId, false);
  }

  private _createParentForm() {
    let formattedMobile = this.phoneNumberService.formatToPhone(this.data.Mobile);

    this.form = new FormGroup({
      firstName: new FormControl(this.data.FirstName, [Validators.required]),
      lastName: new FormControl(this.data.Lastname, [Validators.required]),
      mobile: new FormControl(formattedMobile, [Validators.required, Validators.minLength(12)]),
    });
  }

  private _createChildForm() {
    this.form = new FormGroup({
      firstName: new FormControl(this.data.FirstName, [Validators.required]),
      lastName: new FormControl(this.data.Lastname, [Validators.required]),
      schoolId: new FormControl(this.data.SchoolId, [Validators.required]),
      classId: new FormControl(this.data.ClassId, [Validators.required]),
    });

    this.schoolId.valueChanges.subscribe(schoolId => {
      this._GetListClasses(schoolId, true);
      this.classId.setValue(null);
    });
  }

  formatMobile() {
    let res = this.phoneNumberService.aussieMobileBranded(this.mobile.value);
    this.mobile.patchValue(res);
  }

  closeModal() {
    this.dialogRef.close();
  }

  onSubmit() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }
    this.spinnerService.start();
    let updatedUser = _.cloneDeep(this.data);
    let request = this.isParent ? this.getParentData(updatedUser) : this.getChildData(updatedUser);

    this.userService.UpsertUser(request).subscribe({
      next: res => {
        this.spinnerService.stop();
        this.updateUserInfo(request);
        this.closeModal();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  updateUserInfo(request: UserCashless) {
    if (this.isParent) {
      this.userStore.dispatch(
        userActions.UpdateParentValues({
          firstName: request.FirstName,
          lastName: request.Lastname,
          mobile: request.Mobile,
        })
      );
      return;
    }
    this.updateChildInfo(request);
  }

  updateChildInfo(request: UserCashless) {
    const childUpdatedValues: UpdateChildStateRequest = {
      FirstName: request.FirstName,
      LastName: request.Lastname,
      SchoolId: request.SchoolId,
      SchoolName: request.SchoolName,
      ClassId: request.ClassId,
      ClassName: request.ClassName,
    };
    this.userStore.dispatch(
      userActions.UpdateChildValues({
        updatedChild: childUpdatedValues,
      })
    );
  }

  getParentData(user: UserCashless) {
    user.FirstName = this.firstName.value;
    user.Lastname = this.lastName.value;
    user.Mobile = this.mobile.value;
    return user;
  }

  getChildData(user: UserCashless) {
    user.FirstName = this.firstName.value;
    user.Lastname = this.lastName.value;
    user.SchoolId = this.schoolId.value;
    user.SchoolName = this.getNameFromId(parseInt(this.schoolId.value), true);
    user.ClassId = this.classId.value;
    user.ClassName = this.getNameFromId(parseInt(this.classId.value), false);
    return user;
  }

  getNameFromId(id: number, school: boolean) {
    let data = school ? this.listSchools : this.listClasses;
    let selectedData = data.find(x => parseInt(x.key) === id);
    if (selectedData) {
      return selectedData.value;
    }
  }

  private _GetListSchools() {
    this.schoolService.GetSchoolsAPI().subscribe({
      next: (response: ListSchools) => {
        if (!response?.schools) {
          return;
        }
        this.listSchools = this.processListData(response.schools, 'SchoolId');
      },
      error: error => {
        this.handleErrorFromService(error);
      },
    });
  }

  private _GetListClasses(schoolId: number, updateClassValue: boolean) {
    this.schoolClassesService.GetClassesBySchoolAPI(schoolId, true).subscribe({
      next: (response: ListClasses) => {
        if (!response?.Classes) {
          this.listClasses = [];
          return;
        }
        this.listClasses = this.processListData(response.Classes, 'ClassId');
      },
      error: error => {
        this.handleErrorFromService(error);
      },
    });
  }

  processListData(datalist: SchoolClass[] | School[], idField: string) {
    let list = [];
    datalist.forEach(x => list.push({ key: x[idField].toString(), value: x.Name }));
    return list;
  }

  get firstName() {
    return this.form.get('firstName');
  }
  get lastName() {
    return this.form.get('lastName');
  }
  get mobile() {
    return this.form.get('mobile');
  }
  get schoolId() {
    return this.form.get('schoolId');
  }
  get classId() {
    return this.form.get('classId');
  }
}
