import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
  EventEmitter,
  Output,
} from '@angular/core';

// state
import { select, Store } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { Roles, UserCashless } from 'src/app/sharedModels';
import { UserManagementState } from 'src/app/states/user-management/user-management-state.interface';
import { SelectedUserSelector } from 'src/app/states/user-management/user-management.selectors';

@Component({
  selector: 'user-details',
  templateUrl: './user-details.component.html',
  styleUrls: ['./user-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserDetailsComponent implements OnInit {
  private userSubscription: Subscription;
  user: UserCashless;
  isChild: boolean = false;

  constructor(private cd: ChangeDetectorRef, private store: Store<{ userManagement: UserManagementState }>) {}

  ngOnInit(): void {
    this.userSubscription = this.store.pipe(select(SelectedUserSelector)).subscribe(userRes => {
      if (userRes) {
        this.user = userRes;
        this.isChild = this.user.Role == Roles.Child;
        this.cd.markForCheck();
      }
    });
  }

  ngOnDestroy(): void {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }
}
