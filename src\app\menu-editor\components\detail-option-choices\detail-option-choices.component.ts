import { Component, Input, Output, EventEmitter } from '@angular/core';
import * as _ from 'lodash';

// Services
import { SpinnerService, MenuEditorApiService } from 'src/app/sharedServices';

// Models
import { BaseComponent, SubOption, Option } from 'src/app/sharedModels';
import { MatDialog } from '@angular/material/dialog';
import { DialogDeleteMenuItemComponent } from '../dialog-delete-menu-item/dialog-delete-menu-item.component';

@Component({
  selector: 'editor-detail-option-choices',
  templateUrl: './detail-option-choices.component.html',
  styleUrls: ['./detail-option-choices.component.scss'],
})
export class DetailOptionChoicesComponent extends BaseComponent {
  @Input() option: Option;
  @Input() inDialog: boolean = false;
  @Output() updateOption = new EventEmitter();
  subOptionForm: SubOption;

  constructor(
    private spinnerService: SpinnerService,
    private menuEditorAPIService: MenuEditorApiService,
    private dialog: MatDialog
  ) {
    super();
  }

  GetTextButtonChoice(): string {
    return this.subOptionForm && this.subOptionForm.MenuItemOptionId > 0 ? 'Save' : 'Add';
  }

  ClickAddEdit(optiontoEdit: SubOption): void {
    if (!optiontoEdit) {
      this.setUpDefaultSubOption();
    } else {
      this.subOptionForm = _.cloneDeep(optiontoEdit);
    }
  }

  setUpDefaultSubOption(): void {
    this.subOptionForm = new SubOption();
    this.subOptionForm.IsActive = true;
    this.subOptionForm.MenuItemOptionsCategoryId = this.option.MenuItemOptionsCategoryId;
    this.subOptionForm.OptionType = this.option.Name;
    this.subOptionForm.CanteenId = this.option.CanteenId;
    this.subOptionForm.OptionCost = 0;
    this.subOptionForm.OptionOrder = this.option.SubOptions ? this.option.SubOptions.length + 1 : 1;
  }

  SubmitChoiceForm(): void {
    this.spinnerService.start();

    if (this.editingSubOption()) {
      this.updateSubOption();
    } else {
      this.addSubOption();
    }
  }

  updateSubOption(): void {
    this.menuEditorAPIService.UpdateSubOptionAPI(this.subOptionForm).subscribe({
      next: (response: boolean) => {
        this.reloadOptionData(response);
        this.updateCurrentSubOptionData();
        this.subOptionForm = null;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  updateCurrentSubOptionData(): void {
    const index = this.option.SubOptions.findIndex(
      i => i.MenuItemOptionId == this.subOptionForm.MenuItemOptionId
    );
    this.option.SubOptions[index] = this.subOptionForm;
  }

  addSubOption(): void {
    this.menuEditorAPIService.AddSubOptionAPI(this.subOptionForm).subscribe({
      next: (response: SubOption) => {
        this.addNewSubOption(response);
        this.subOptionForm = null;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }
  addNewSubOption(newSubOption: SubOption): void {
    if (!this.option.SubOptions) {
      this.option.SubOptions = [];
    }
    this.option.SubOptions.push(newSubOption);
  }

  deleteSubOption(optionId: number): void {
    this.spinnerService.start();
    this.menuEditorAPIService.DeleteSubOptionAPI(optionId).subscribe({
      next: (response: boolean) => {
        this.reloadOptionData(response);
        this.removeSubOptionFromList();
        this.subOptionForm = null;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  removeSubOptionFromList() {
    this.option.SubOptions = this.option.SubOptions.filter(
      element => element.MenuItemOptionId !== this.subOptionForm.MenuItemOptionId
    );
  }

  reloadOptionData(reload: boolean) {
    if (reload) {
      this.updateOption.emit();
    }
  }

  ArchiveChoice(): void {
    this.dialog.open(DialogDeleteMenuItemComponent, {
      data: {
        displayOption: 'choice',
        onDeletePress: () => {
          this.deleteSubOption(this.subOptionForm.MenuItemOptionId);
        },
      },
    });
  }

  editingSubOption() {
    return this.subOptionForm.MenuItemOptionId > 0;
  }
}
