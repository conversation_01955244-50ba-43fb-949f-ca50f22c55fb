{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "../../out-tsc/lib", "declarationMap": true, "module": "es2015", "moduleResolution": "node", "declaration": true, "sourceMap": true, "inlineSources": true, "experimentalDecorators": true, "importHelpers": true, "types": [], "lib": ["dom", "es2018"]}, "angularCompilerOptions": {"skipTemplateCodegen": true, "strictMetadataEmit": true, "fullTemplateTypeCheck": true, "strictInjectionParameters": true, "enableResourceInlining": true}, "exclude": ["src/test.ts", "**/*.spec.ts"]}