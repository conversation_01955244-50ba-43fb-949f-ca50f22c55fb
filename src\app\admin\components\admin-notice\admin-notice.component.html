<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h3>Noticeboard</h3>
    </div>
  </div>

  <div class="row">
    <div class="col-12 schoolsArray">
      <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 tableau accountTable">
        <ng-container matColumnDef="noticeId">
          <th mat-header-cell *matHeaderCellDef>Notice Id</th>
          <td mat-cell *matCellDef="let element">{{ element.NoticeId }}</td>
        </ng-container>

        <ng-container matColumnDef="title">
          <th mat-header-cell *matHeaderCellDef>Title</th>
          <td mat-cell *matCellDef="let element">{{ element.Title }}</td>
        </ng-container>

        <ng-container matColumnDef="description">
          <th mat-header-cell *matHeaderCellDef>Description</th>
          <td mat-cell *matCellDef="let element">{{ element.Description }}</td>
        </ng-container>

        <ng-container matColumnDef="schoolId">
          <th mat-header-cell *matHeaderCellDef>School Name</th>
          <td mat-cell *matCellDef="let element">{{ element.SchoolName }}</td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let element">{{ element.Status }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns" (click)="openDialog(row)"></tr>
      </table>
    </div>
  </div>
</div>
