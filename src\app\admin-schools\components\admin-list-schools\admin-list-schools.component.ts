import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { PageEvent } from '@angular/material/paginator';

//models
import { School, BasePaginatorComponent, ArrayFilter } from '../../../sharedModels';

//services
import { AdminService, SpinnerService, SchoolService } from '../../../sharedServices';

const _columns = ['id', 'name', 'deactivatedFilters', 'options'];

@Component({
  selector: 'app-admin-list-schools',
  templateUrl: './admin-list-schools.component.html',
  styleUrls: ['./admin-list-schools.component.scss'],
})
export class AdminListSchoolsComponent extends BasePaginatorComponent<School> implements OnInit {
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: SpinnerService,
    private adminService: AdminService,
    private schoolService: SchoolService
  ) {
    super(_columns);
  }

  ngOnInit() {
    this.route.data.subscribe(data => {
      // get data from resolver
      let tempRes = data['schools'];
      this._ProcessResponseSchools(tempRes.schoolList);

      // get current filters
      this.listfilters = this.adminService.GetSchoolFilters();

      if (!this.listfilters) {
        this.listfilters = new ArrayFilter();
      }
    });
  }

  pageChange(event: PageEvent) {
    // Update filter
    this.basePageChange(event);

    // send request
    this._requestSchools();
  }

  clearFilter() {
    this.clearFiltersAndResults();
    // send request
    this._requestSchools();
  }

  fetchData(searchInput: string) {
    this.listfilters.Filter = searchInput;
    this._requestSchools();
  }

  /** Call the school service to get the school data  */
  private _requestSchools() {
    // start spinner
    this.spinnerService.start();

    // save current filters
    this.adminService.SetSchoolFilters(this.listfilters);

    this.schoolService.GetSchoolsWithFilterAPI(this.listfilters).subscribe({
      next: (res: any) => {
        this._ProcessResponseSchools(res.schoolList);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  /** Process the list of users to be used in the component */
  private _ProcessResponseSchools(response: School[]) {
    if (response) {
      this.listObjects = response;

      if (this.listObjects && this.listObjects.length > 0) {
        this.totalRows = this.listObjects[0].TotalRows;
      } else {
        this.totalRows = 0;
      }
    } else {
      this.listObjects = [];
      this.totalRows = 0;
    }
    this.dataSource.data = this.listObjects;

    //Stop spinner
    this.spinnerService.stop();
  }

  ShowCutOffTime(time: Date) {
    let compare = new Date('0001-01-01');
    return new Date(time) > compare;
  }
}
