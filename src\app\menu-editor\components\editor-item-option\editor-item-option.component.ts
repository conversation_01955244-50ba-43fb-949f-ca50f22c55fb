import { Component, OnInit, Input, OnDestroy } from '@angular/core';

// Models
import {
  MenuItem,
  Option,
  ListOptionsResponse,
  BaseComponent,
  AddRemoveOptionRequest,
  Canteen,
} from 'src/app/sharedModels';

// Services
import { SpinnerService, MenuEditorApiService } from 'src/app/sharedServices';

// ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { CanteenState } from '../../../states';
import { selectedCanteen } from '../../../states/canteen/canteen.selectors';

@Component({
  selector: 'editor-item-option',
  templateUrl: './editor-item-option.component.html',
  styleUrls: ['./editor-item-option.component.scss'],
})
export class EditorItemOptionComponent extends BaseComponent implements OnInit, OnDestroy {
  @Input() item: MenuItem;
  itemOptions: Option[] = [];
  listOptionsToShow: Option[] = [];
  private listOptions: Option[] = [];
  private subscription: Subscription;

  constructor(
    private spinnerService: SpinnerService,
    private menuEditorAPIService: MenuEditorApiService,
    private store: Store<{ canteen: CanteenState }>
  ) {
    super();
  }

  ngOnInit() {
    this.subscription = this.store.pipe(select(selectedCanteen)).subscribe((canteen: Canteen) => {
      if (canteen) {
        this._LoadCanteenOptions(canteen.CanteenId);
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  private _LoadItemOptions() {
    this.spinnerService.start();
    this.menuEditorAPIService.GetOptionsByMenuItemAPI(this.item.MenuItemId).subscribe({
      next: (response: Option[]) => {
        if (response) {
          this.itemOptions = response;
        } else {
          this.itemOptions = [];
        }
        this._FilterAddOptions();
        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      },
    });
  }

  private _FilterAddOptions() {
    this.listOptionsToShow = [];

    this.listOptions.forEach(i => {
      if (!i.IsActive) {
        return;
      }
      if (this.itemOptions && this.itemOptions.length > 0) {
        let index = this.itemOptions.findIndex(
          x => x.MenuItemOptionsCategoryId == i.MenuItemOptionsCategoryId
        );
        if (index < 0) {
          i.SubOptions = i.SubOptions && i.SubOptions.filter(subOption => subOption.IsActive);
          this.listOptionsToShow.push(i);
        }
      } else {
        i.SubOptions = i.SubOptions && i.SubOptions.filter(subOption => subOption.IsActive);
        this.listOptionsToShow.push(i);
      }
    });
  }

  private _LoadCanteenOptions(canteenId: number) {
    this.spinnerService.start();
    this.menuEditorAPIService.GetOptionsAPI(canteenId).subscribe({
      next: (response: ListOptionsResponse) => {
        if (response && response.Options) {
          this.listOptions = response.Options;
          this._LoadItemOptions();
        } else {
          this.listOptions = [];
          this.spinnerService.stop();
        }
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      },
    });
  }

  AddOption(option: Option) {
    this.spinnerService.start();

    let request: AddRemoveOptionRequest = new AddRemoveOptionRequest();
    request.ItemId = this.item.MenuItemId;
    request.OptionId = option.MenuItemOptionsCategoryId;
    request.CanteenId = this.item.CanteenId;
    request.MenuId = this.item.MenuId;

    this.menuEditorAPIService.AddOptionToItemAPI(request).subscribe({
      next: (response: any) => {
        // success
        this._LoadItemOptions();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  RemoveOption(option: Option) {
    this.spinnerService.start();

    let request: AddRemoveOptionRequest = new AddRemoveOptionRequest();
    request.ItemId = this.item.MenuItemId;
    request.OptionId = option.MenuItemOptionsCategoryId;
    request.CanteenId = this.item.CanteenId;
    request.MenuId = this.item.MenuId;

    this.menuEditorAPIService.RemoveOptionFromItemAPI(request).subscribe({
      next: (response: any) => {
        // success
        this._LoadItemOptions();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }
}
