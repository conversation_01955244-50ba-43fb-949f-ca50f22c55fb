<div class="container-fluid">
  <div class="row">
    <div class="col-9">
      <nav-back-button
        (navBack)="GoBackClick()"
        [enableClick]="!disableOrderFilters()"
        (clicked)="ShowMenusFilters()"
        [text]="titlePageMobile()"
        class="d-block d-md-none"
      ></nav-back-button>
      <nav-back-button (navBack)="GoBackClick()" class="d-none d-md-block"></nav-back-button>
    </div>
    <div class="col-3 d-block d-md-none mobileFilterCol">
      <filters-items
        *ngIf="selectedMenuType !== menuTypeEnum.Uniform"
        [deactivatedFilters]="deactivatedFilters"
      ></filters-items>
    </div>
  </div>
  <div class="row">
    <div class="d-none d-md-block col-md-8 col-lg-8 col-xl-7 offset-xl-1">
      <order-filters [title]="titlePage()" [isEdit]="orderToEdit"></order-filters>
    </div>
    <div class="d-none d-md-block col-md-2">
      <filters-items
        *ngIf="selectedMenuType !== menuTypeEnum.Uniform"
        [deactivatedFilters]="deactivatedFilters"
      ></filters-items>
    </div>
  </div>

  <div *ngIf="menuLoading(); else result" class="col-12 d-flex justify-content-center pt-4">
    <app-spinner [manual]="true"></app-spinner>
  </div>

  <ng-template #result>
    <div *ngIf="noMenuMessage; else menu">
      <div class="row">
        <div class="col-12 col-md-8 col-lg-8 col-xl-7 offset-xl-1">
          <p class="noMenuAvailable">{{ noMenuMessage }}</p>
        </div>
      </div>
    </div>

    <ng-template #menu>
      <div *ngIf="currentMenu?.MenuJSON" class="row">
        <div class="col-12 col-md-8 col-lg-8 col-xl-7 offset-xl-1 itemsContainers">
          <ul class="listMenu scrolling-horizontal-wrapper">
            <li
              *ngFor="let cat of currentMenu.MenuJSON; index as i"
              id="menu-category-{{ i }}"
              class="mr-2"
              trackBy:cat.CategoryId
            >
              <category-tile
                (click)="SetCategory(cat)"
                [name]="cat.CatName"
                [iconName]="cat.CatUrl"
                [isSelected]="IsCurrentCategory(cat)"
              ></category-tile>
            </li>
          </ul>
        </div>
      </div>

      <ng-container *ngIf="currentCategoryToDisplay">
        <div class="row noMarginRight">
          <div class="col-12 col-md-8 col-lg-8 col-xl-7 offset-xl-1 categoryContainers">
            <h4 class="title">{{ currentCategoryToDisplay.CatName }}</h4>
            <div class="row">
              <div
                class="col-12 col-lg-6 item"
                *ngFor="let item of currentCategoryToDisplay.item; index as i"
              >
                <mat-card appearance="outlined" class="card">
                  <product-item
                    [category]="currentCategoryToDisplay"
                    [item]="item"
                    [dateOrder]="selectedOrderDate"
                    [currentMenuType]="selectedMenuType"
                    [schoolCutOffTime]="menuCutOffTime"
                    (clickItem)="AddToCart($event)"
                    id="product-item-{{ i }}"
                  >
                  </product-item>
                </mat-card>
              </div>
              <div
                *ngIf="currentCategoryToDisplay.item && currentCategoryToDisplay.item.length == 0"
                class="col-12"
              >
                <div class="noItemAfterFilter">
                  <h4>No items for this category</h4>
                  <p>Try changing your filters</p>
                </div>
              </div>
            </div>
          </div>
          <div class="d-none d-md-block col-md-4 col-lg-4">
            <shopping-cart></shopping-cart>
          </div>
        </div>
      </ng-container>
    </ng-template>
  </ng-template>
</div>

<div class="d-block d-md-none">
  <div *ngIf="showMobilePlaceOrder">
    <div class="row noMarginRight">
      <div class="col-12">
        <a class="placeOrderMobileLink" routerLink="../shoppingCart">
          Go to Cart ({{ priceCart() | currency }})
        </a>
      </div>
    </div>
  </div>
</div>
