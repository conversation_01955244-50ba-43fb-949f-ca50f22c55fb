<form *ngIf="form" [formGroup]="form" class="form">
  <div class="row">
    <div class="col-12 col-lg-7">
      <mat-form-field appearance="outline">
        <mat-label>Event Title</mat-label>
        <input
          matInput
          maxlength="40"
          placeholder="Enter title of event"
          formControlName="title"
          type="text"
          id="title-input"
        />
        <mat-error *ngIf="title.invalid">You must enter a value</mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Event Description</mat-label>
        <textarea
          maxlength="500"
          matInput
          placeholder="Add a description"
          formControlName="description"
          id="description-input"
          type="text"
          rows="8"
          #description
        ></textarea>
        <mat-hint align="end">{{ description.value.length }} / 500</mat-hint>
        <mat-error *ngIf="!descriptionFill">You must enter a value</mat-error>
      </mat-form-field>
    </div>
    <div class="col-12 col-lg-5">
      <div class="time-date-container">
        <mat-form-field appearance="outline">
          <mat-label>Event date</mat-label>
          <input
            matInput
            [matDatepicker]="picker1"
            formControlName="eventDate"
            [min]="todaysOrEventDate"
            readonly
          />
          <mat-error>You must enter a value</mat-error>
          <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
          <mat-datepicker #picker1></mat-datepicker>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Event time</mat-label>
          <input matInput maxlength="40" formControlName="eventTime" type="time" />
        </mat-form-field>
      </div>
      <div class="time-date-container">
        <mat-form-field appearance="outline">
          <mat-label>Cut off date</mat-label>
          <mat-error>You must enter a value</mat-error>
          <input
            matInput
            [matDatepicker]="picker2"
            formControlName="cutOffDate"
            [min]="todaysOrEventDate"
            readonly
          />
          <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
          <mat-datepicker #picker2></mat-datepicker>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Cut off time</mat-label>
          <input matInput formControlName="cutOffTime" type="time" />
        </mat-form-field>
      </div>

      <div *ngIf="eventDateHasPassed && !isAdmin && isEdit" class="info-container">
        <mat-icon class="info-icon">info_outline</mat-icon>
        <p>Event dates cannot be edited once the event date has passed</p>
      </div>

      <mat-form-field floatLabel="never" appearance="outline">
        <mat-label>Classes (optional)</mat-label>
        <mat-select
          matNativeControl
          formControlName="selectedClasses"
          placeholder="Select classes"
          multiple
          id="class-selector"
        >
          <mat-option *ngFor="let option of schoolClasses" [value]="option.ClassId || ''">
            {{ option.Name || '' }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <div *ngIf="isAdmin && isEdit" style="text-align: left">
        <mat-checkbox formControlName="waiveEventOrderFee" id="active-checkbox">
          <p>Waive Order Fee</p>
        </mat-checkbox>
      </div>
    </div>
  </div>

  <p *ngIf="showWarning" class="warning">
    This event is currently active. Changing the event date will update all orders already placed.
  </p>
  <div class="button-container">
    <basic-button-v2
      (onPress)="formatEvent()"
      [text]="buttonText"
      buttonStyle="primaryOrange"
      [disabled]="disableSaveButton()"
    ></basic-button-v2>
  </div>
</form>
