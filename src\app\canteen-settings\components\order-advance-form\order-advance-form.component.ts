import { Component, OnInit, Output, EventEmitter, Input, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'order-advance-form',
  templateUrl: './order-advance-form.component.html',
  styleUrls: ['./order-advance-form.component.scss'],
})
export class OrderAdvanceFormComponent implements OnInit {
  @Output() updateValue: EventEmitter<number> = new EventEmitter();
  @Input() weekValue: number;
  form: FormGroup;
  weeksPreOrderOptions: number[] = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

  constructor() {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.weekValue?.currentValue || changes?.weekValue?.currentValue === 0) {
      this._createForm();
    }
  }

  private _createForm() {
    this.form = new FormGroup({
      weeksAdvanceValue: new FormControl(this.weekValue, Validators.required),
    });
  }

  submitForm() {
    this.updateValue.emit(this.weeksAdvanceValue.value);
  }

  get weeksAdvanceValue() {
    return this.form.get('weeksAdvanceValue');
  }
}
