import { Component, EventEmitter, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import * as moment from 'moment';

//ngrx
import { select, Store } from '@ngrx/store';
import { menuCategories } from 'src/app/states/canteen/canteen.selectors';
import { Subscription } from 'rxjs';
import { CanteenState } from 'src/app/states';

// models
import {
  BaseComponent,
  Canteen,
  CanteenFilters,
  CanteenStatusEnum,
  MerchantTypeEnum,
  CategoryEditor,
  LabelPrintChoiceEnum,
  MenuTypeEnum,
  CanteenOrderRequest,
  SettingChangedEvent,
} from 'src/app/sharedModels';

// services
import { CanteenService, DebounceService } from 'src/app/sharedServices';

@Component({
  selector: 'canteen-order-filter',
  templateUrl: './canteen-order-filter.component.html',
  styleUrls: ['./canteen-order-filter.component.scss'],
})
export class CanteenOrderFilterComponent extends BaseComponent implements OnInit, OnDestroy {
  @Output() filtersChanged: EventEmitter<CanteenOrderRequest> = new EventEmitter();
  @Output() settingsChanged: EventEmitter<SettingChangedEvent> = new EventEmitter();
  selectedCanteen: Canteen;
  isUniformCanteen: boolean = false;
  isEventCanteen: boolean = false;
  filterForm: FormGroup;
  canteenFilters: CanteenFilters;
  labelPrintChoice: string;
  listCategories: CategoryEditor[];
  selectedSchools: number[];
  selectedCategories: number[];
  showCategoriesList: boolean = false;
  canteenSubscription: Subscription;

  constructor(
    private canteenService: CanteenService,
    private store: Store<{ canteen: CanteenState }>,
    private debounceService: DebounceService
  ) {
    super();
  }

  ngOnInit() {
    this.canteenSubscription = this.store
      .pipe(select(menuCategories))
      .subscribe((menuCategories: CategoryEditor[]) => {
        this.listCategories = menuCategories;
      });
    // get saved filters
    this.canteenFilters = this.canteenService.GetFilters();

    // create form
    this._createForm();
  }

  ngOnDestroy() {
    if (this.canteenSubscription) {
      this.canteenSubscription.unsubscribe();
    }
  }

  get date() {
    return this.filterForm.get('date');
  }

  get recess() {
    return this.filterForm.get('recess');
  }

  get lunch() {
    return this.filterForm.get('lunch');
  }

  get event() {
    return this.filterForm.get('event');
  }

  get printed() {
    return this.filterForm.get('printed');
  }

  get uniNew() {
    return this.filterForm.get('uniNew');
  }

  get uniProcessing() {
    return this.filterForm.get('uniProcessing');
  }

  get uniReady() {
    return this.filterForm.get('uniReady');
  }

  get uniCompleted() {
    return this.filterForm.get('uniCompleted');
  }

  get search() {
    return this.filterForm.get('search');
  }

  /**
   * Setup the filters form and listen to events
   */
  private _createForm() {
    this.filterForm = new FormGroup({
      search: new FormControl(''),
      recess: new FormControl(this.canteenFilters.Recess),
      lunch: new FormControl(this.canteenFilters.Lunch),
      event: new FormControl(this.canteenFilters.Event),
      printed: new FormControl(this.canteenFilters.Printed),
      date: new FormControl(this.canteenFilters.Date, [Validators.required]),
      uniNew: new FormControl(this.canteenFilters.UniNew),
      uniProcessing: new FormControl(this.canteenFilters.UniProcessing),
      uniReady: new FormControl(this.canteenFilters.UniReady),
      uniCompleted: new FormControl(this.canteenFilters.UniCompleted),
    });

    this.date.valueChanges.subscribe(val => {
      this.triggerFiltersChanged();
    });

    this.recess.valueChanges.subscribe(val => {
      this.canteenFilters.Recess = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });

    this.lunch.valueChanges.subscribe(val => {
      this.canteenFilters.Lunch = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });

    this.event.valueChanges.subscribe(val => {
      this.canteenFilters.Event = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });

    this.printed.valueChanges.subscribe(val => {
      this.canteenFilters.Printed = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });

    this.uniNew.valueChanges.subscribe(val => {
      this.canteenFilters.UniNew = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });

    this.uniProcessing.valueChanges.subscribe(val => {
      this.canteenFilters.UniProcessing = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });

    this.uniReady.valueChanges.subscribe(val => {
      this.canteenFilters.UniReady = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });

    this.uniCompleted.valueChanges.subscribe(val => {
      this.canteenFilters.UniCompleted = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });
  }

  /**
   * Manage the change of the selected canteen
   * @param canteen selected canteen
   */
  CanteenChanged(canteen: Canteen) {
    this.selectedCanteen = canteen;
    this.isUniformCanteen = this.selectedCanteen.CanteenType === MerchantTypeEnum.Uniform;
    this.isEventCanteen = this.selectedCanteen.CanteenType === MerchantTypeEnum.Event;
    this.labelPrintChoice = this.selectedCanteen.Schools[0].LabelPrintChoice; // TODO => change the logic to take the First selected school
    this.showCategoriesList = this.labelPrintChoice == LabelPrintChoiceEnum.Item;

    // export settings to the main screen
    const settings: SettingChangedEvent = {
      LabelPrintChoice: this.labelPrintChoice,
      UsePrintingApp: this.selectedCanteen.Schools[0].UsePrintingApp,
      IsUniformCanteen: this.isUniformCanteen,
    };
    this.settingsChanged.emit(settings);
  }

  /**
   * Search value
   */
  SearchClicked() {
    this.triggerFiltersChanged();
  }

  /**
   * Handled event when schools selection change
   * @param schools list of schools
   */
  SchoolSelectionChanged(schools: number[]) {
    this.selectedSchools = schools;

    if (this.selectedCanteen.CanteenType == MerchantTypeEnum.Event) {
      this.event.setValue(true);

      this.canteenFilters.Event = true;
      this.canteenService.SetFilters(this.canteenFilters);
    }

    if (
      this.labelPrintChoice == LabelPrintChoiceEnum.Order ||
      (this.labelPrintChoice == LabelPrintChoiceEnum.Item && this.listCategories.length > 0)
    ) {
      this.triggerFiltersChanged();
    }
  }

  /**
   * Handled event when categories selection change
   * @param categories list of categories
   */
  CategoriesSelectionChanged(categories: number[]) {
    this.selectedCategories = categories;
    this.triggerFiltersChanged();
  }

  triggerFiltersChanged = this.debounceService.callDebounce(this._prepareRequest, 100, false, true);

  private _prepareRequest() {
    // prepare request
    let request: CanteenOrderRequest = new CanteenOrderRequest();

    this.selectedSchools.forEach(n => {
      if (!request.SchoolIds) {
        request.SchoolIds = n.toString();
      } else {
        request.SchoolIds += ',' + n;
      }
    });

    // filter per selected categories (print 1 item per label only)
    if (this.selectedCategories) {
      this.selectedCategories.forEach(n => {
        if (!request.CategoryIds) {
          request.CategoryIds = n.toString();
        } else {
          request.CategoryIds += ',' + n;
        }
      });
    }

    request.Date = moment(this.date.value).format('YYYY-MM-DD');

    request.CanteenStatus = '';
    request.OrderType = '';
    request.LabelPrintChoice = this.labelPrintChoice;
    request.Filter = this.search.value;
    request.MerchantId = this.selectedCanteen.CanteenId;

    if (this.isUniformCanteen) {
      request.OrderType += MenuTypeEnum.Uniform + ',';

      if (this.uniNew.value) {
        request.CanteenStatus += CanteenStatusEnum.New + ',';
      }

      if (this.uniProcessing.value) {
        request.CanteenStatus += CanteenStatusEnum.Processing + ',';
      }

      if (this.uniReady.value) {
        request.CanteenStatus += CanteenStatusEnum.Ready + ',';
      }

      if (this.uniCompleted.value) {
        request.CanteenStatus += CanteenStatusEnum.Completed + ',';
      }
    } else {
      if (this.recess.value && !this.isEventCanteen) {
        request.OrderType += MenuTypeEnum.Recess + ',';
      }

      if (this.lunch.value && !this.isEventCanteen) {
        request.OrderType += MenuTypeEnum.Lunch + ',';
      }

      if (this.event.value) {
        request.OrderType += MenuTypeEnum.Event + ',';
      }

      if (this.printed.value) {
        if (this.printed.value == 'all') {
          request.Printed == null;
        } else if (this.printed.value == 'printed') {
          request.Printed = true;
        } else {
          request.Printed = false;
        }
      }
    }

    // trigger event
    this.filtersChanged.emit(request);
  }
}
