<table>
  <thead>
    <tr>
      <th>Id</th>
      <th>Item name</th>
      <th>Is Active</th>
      <th></th>
    </tr>
  </thead>
  <tbody *ngIf="selectedCategory">
    <tr *ngFor="let item of tableData" class="line">
      <td>{{ item.MenuItemId }}</td>
      <td>{{ item.Name }}</td>
      <td>
        <mat-checkbox [checked]="item.IsActive" [disabled]="true"></mat-checkbox>
      </td>
      <td>
        <mat-icon *ngIf="ItemIsSelected(item.MenuItemId); else addIcon" matTooltip="Add" class="selected-icon" (click)="clickRow(item)">check</mat-icon>
        
        <ng-template #addIcon>
          <mat-icon matTooltip="Add" class="actionTableau" (click)="clickRow(item)">add</mat-icon>
        </ng-template>
           
      </td>
    </tr>
  </tbody>
</table>
