<div class="row">
  <div class="col-12">
    <!-- <ul class="tabLabels">
        <li [ngClass]="{'active': ShowTab(menuType.Recess)}" (click)="ChangeTab(menuType.Recess)">Recess</li>
        <li [ngClass]="{'active': ShowTab(menuType.Lunch)}" (click)="ChangeTab(menuType.Lunch)">Lunch</li>
        <li [ngClass]="{'active': ShowTab(menuType.Event)}" (click)="ChangeTab(menuType.Event)">Event</li>
      </ul> -->
  </div>
</div>

<ng-container *ngIf="listLabels && listLabels.length > 0">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <h3>Labels</h3>
      </div>
    </div>
  </div>
  <block-print [listLabels]="listLabels"></block-print>
</ng-container>

<ng-container *ngIf="!listLabels || listLabels.length == 0">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <h3>No Labels</h3>
      </div>
    </div>
  </div>
</ng-container>
