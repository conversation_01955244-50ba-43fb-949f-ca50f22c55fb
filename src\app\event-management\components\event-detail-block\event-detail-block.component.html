<ng-container *ngIf="schoolEvent">
  <div class="row-container">
    <div class="row">
      <div class="col-10">
        <h1>{{ schoolEvent.Name }}</h1>
      </div>
      <div class="col-2 button-container">
        <basic-button-v2
        (onPress)="editEventPress()"
        text="Edit event"
        buttonStyle="basic"
        >
          <img src="/assets/icons/black-pencil.svg" alt="transfer symbol" style="margin-right: 6px;" />
        </basic-button-v2>
      </div>
    </div>

    <div class="row">
      <div class="col-10">
        <h4 class="mb-2">Description</h4>
        <p class="description">{{ schoolEvent.Description }}</p>
      </div>
    </div>
    <div class="row">
      <div class="col-12">
        <h4>Event date <span>{{ schoolEvent.EventDate | date : 'mediumDate'}}{{ schoolEvent.EventDate | eventTimeFormat }}</span></h4>
      </div>
      <div class="col-12">
        <h4>Cut off date <span>{{ schoolEvent.CutOffDate | date : 'mediumDate'}}{{ schoolEvent.CutOffDate | eventTimeFormat }}</span></h4>
      </div>
    </div>

    <div class="row">
      <div class="col-12 button-container">
        <a
          class="deleteLink"
          (click)="deleteEventPress()"
          [ngClass]="hideDeleteButton() ? 'disabled' : 'active'"
        >Delete event</a>
      </div>
    </div>

  </div>
</ng-container>
