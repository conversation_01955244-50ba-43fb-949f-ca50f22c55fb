import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { DialogDeleteMenuItemComponent } from './dialog-delete-menu-item.component';

describe('DialogDeleteMenuItemComponent', () => {
  let component: DialogDeleteMenuItemComponent;
  let fixture: ComponentFixture<DialogDeleteMenuItemComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [DialogDeleteMenuItemComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogDeleteMenuItemComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
