<form *ngIf="filterForm" [formGroup]="filterForm">
  <div class="row">
    <div class="col-12 col-md-3 col-lg-2">
      <canteen-select-list (selectedChanged)="CanteenChanged($event)"></canteen-select-list>
    </div>
  </div>

  <div class="row">
    <div *ngIf="!isUniformCanteen" class="col-12 col-md-3 col-lg-2">
      <input-date placeholder="Date" formControlName="date"></input-date>
    </div>
    <div *ngIf="!isUniformCanteen" class="col-12 col-md-3 col-lg-2">
      <mat-form-field appearance="outline">
        <mat-label>Print Status</mat-label>
        <mat-select formControlName="printed">
          <mat-option value="all">All</mat-option>
          <mat-option value="printed">Printed Only</mat-option>
          <mat-option value="unprinted">Unprinted Only</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div class="col-12 col-md-3 col-lg-2">
      <mat-form-field appearance="outline" class="searchBox">
        <input matInput formControlName="search" placeholder="Search" autocomplete="off" />
        <mat-icon matSuffix (click)="SearchClicked()" aria-hidden="false">search</mat-icon>
      </mat-form-field>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <h4>Filters</h4>
    </div>
  </div>

  <!-- Schools filters -->
  <div *ngIf="selectedCanteen" class="row">
    <div class="col-12">
      <canteen-order-school-filter
        [schools]="selectedCanteen.Schools"
        (schoolsSelected)="SchoolSelectionChanged($event)"
      ></canteen-order-school-filter>
    </div>
  </div>

  <!-- Order type Filters-->
  <div class="row mt-3">
    <div class="col-12">
      <canteen-order-type-filter
        *ngIf="selectedCanteen"
        [merchantType]="selectedCanteen.CanteenType"
        [(formGroup)]="filterForm"
      >
      </canteen-order-type-filter>
    </div>
  </div>

  <!-- Item categories Filters-->
  <div *ngIf="showCategoriesList && listCategories" class="row mt-3">
    <div class="col-12">
      <canteen-order-category-filter
        [categories]="listCategories"
        (categoriesSelected)="CategoriesSelectionChanged($event)"
      ></canteen-order-category-filter>
    </div>
  </div>
</form>
