<mat-sidenav-container>
  <div class="desktop">
    <mat-toolbar color="primary" role="heading" class="cashless-toolbar">
      <ul>
        <li *ngFor="let data of navData">
          <a [routerLink]="data.Link" routerLinkActive="activeLink">{{ data.Name }}</a>
          <span class="tab-selected"></span>
        </li>
        <li class="accountBalance">
          <img src="assets/icons/wallet.svg" class="accountBalanceImage" />
          <a class="accountBalanceText" routerLink="/family/account"
            >Balance: {{ accountBalance | currency }}</a
          >
        </li>
      </ul>

      <!-- This fills the remaining space of the current row -->
      <span class="d-none d-md-inline-block"> </span>
    </mat-toolbar>
  </div>

  <mat-sidenav class="mobile" #sidenav [(opened)]="opened" [mode]="'over'" position="end">
    <mat-nav-list>
      <a mat-list-item *ngFor="let data of navData" [routerLink]="data.Link" routerLinkActive="activeLink">
        <span class="nav-tab">{{ data.Name }}</span>
      </a>

      <a mat-list-item (click)="SignOut()">
        <span class="nav-tab">Log Out</span>
      </a>
    </mat-nav-list>
  </mat-sidenav>

  <div class="mobile">
    <div class="container-fluid mobileNav">
      <div class="row">
        <div class="col-9 childrenCol">
          <children-list [noShadow]="true"></children-list>
        </div>
        <div class="col-3">
          <mat-icon class="IconMenuMobile" aria-hidden="false" aria-label="Menu" (click)="sidenav.open()"
            >account_circle</mat-icon
          >
        </div>
      </div>
    </div>
  </div>

  <div class="mobileContainer">
    <router-outlet></router-outlet>
  </div>
</mat-sidenav-container>
