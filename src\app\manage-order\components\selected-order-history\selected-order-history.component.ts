import { Component, OnInit, OnDestroy } from '@angular/core';
import { Location } from '@angular/common';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { MatDialog } from '@angular/material/dialog';
import { DeviceDetectorService } from 'ngx-device-detector';
import * as moment from 'moment';
import * as _ from 'lodash';
import {
  FeatureFlags,
  ORDER_AGAIN_CUT_OFF_DAY,
  ORDER_AGAIN_CUT_OFF_MONTH,
  ORDER_AGAIN_CUT_OFF_YEAR,
} from 'src/constants';

// Ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { familyState } from 'src/app/states/family/family.selectors';
import { childrenState } from 'src/app/states/children/children.selectors';
import { FamilyState, ChildrenState } from '../../../states';

// Models
import {
  MenuItem,
  FamilyDayOrders,
  Order,
  BaseComponent,
  OrderStatusEnum,
  UserCashless,
  MenuTypeEnum,
  CanteenStatusEnum,
  SchoolEvent,
  PlaceOrderDialogData,
  OrderWithCutOffTimes,
  CutOffTimes,
  checkoutErrorModal,
  RefinedOrder,
  RefinedOrderItem,
  ConfirmModal,
  OrderAgainSheetData,
  ReOrdersSummaryRequest,
  ReOrderSummary,
  CartItem,
} from 'src/app/sharedModels';

// Services
import { CashlessAppInsightsService, FeatureFlagService } from 'src/app/sharedServices';

// components
import {
  DialogCancelOrderComponent,
  DialogConfirmComponent,
  DialogCutOffTimeComponent,
} from '../../../shared/components';
import { DialogPlaceOrderComponent } from '../dialog-place-order/dialog-place-order.component';
import { ReorderFilterComponent } from '../reorder-filter/reorder-filter.component';
import { DateHasPassed } from 'src/app/utility';
import { ActivatedRoute } from '@angular/router';
import { CreateOrderService } from 'src/app/sharedServices/order/create-order.service';
import { ConvertOrderItemToCartType, GetGroupedShoppingCart } from '../../functions/convert-to-cart-items';
import { GetCustomMenuName } from 'src/app/sharedServices/menu/menu-custom-name';
import { convertSchoolDateTimeToLocalDateTime } from 'src/app/utility/timezone-helper';

@Component({
  selector: 'selected-order-history',
  templateUrl: './selected-order-history.component.html',
  styleUrls: ['./selected-order-history.component.scss'],
})
export class SelectedOrderHistoryComponent extends BaseComponent implements OnInit, OnDestroy {
  private subscriptionFamilyState$: Subscription;
  private subscriptionStudentsState$: Subscription;
  titlePage: string;
  listProducts: RefinedOrderItem[] = [];
  hasError: boolean;
  isProcessing: boolean;
  isUniformOrder: boolean;
  errorMessage: string;
  nextWeekDate: Date;
  orderStatusDisplay: string;
  orderFee: number;
  currentEvent: SchoolEvent[];
  orderToReorder: Order;
  orderData: OrderWithCutOffTimes;
  day: FamilyDayOrders;
  disableOrderAgainButton: boolean = true;
  selectedUser: UserCashless;
  orderPrice: number;

  constructor(
    private familyStore: Store<{ family: FamilyState }>,
    private location: Location,
    public dialog: MatDialog,
    private deviceService: DeviceDetectorService,
    private _bottomSheet: MatBottomSheet,
    private appInsightsService: CashlessAppInsightsService,
    private featureFlagService: FeatureFlagService,
    private route: ActivatedRoute,
    private createOrderService: CreateOrderService
  ) {
    super();
  }

  ngOnInit(): void {
    this.featureFlagService.getFlag(FeatureFlags.disableOrderAgainButton, false).then(res => {
      this.disableOrderAgainButton = res;
    });

    this.subscriptionStudentsState$ = this.familyStore
      .pipe(select(childrenState))
      .subscribe((studentState: ChildrenState) => {
        this.selectedUser = studentState.selected;
      });

    this.subscriptionFamilyState$ = this.familyStore
      .pipe(select(familyState))
      .subscribe((familyState: FamilyState) => {
        this.day = Object.assign(new FamilyDayOrders(), familyState.dayDetail);
      });

    // get order data from resolver
    this.route.data.subscribe(data => {
      if (data?.orderWithCutOffTimes) {
        this.orderData = {
          Order: data.orderWithCutOffTimes.Order,
          CutOffTimes: this.convertCutOffTimeToLocalTime(data.orderWithCutOffTimes.CutOffTimes),
        };
        this.processOrderData(this.orderData.Order);
      }
    });
  }

  convertCutOffTimeToLocalTime(cutOffTimes: CutOffTimes): CutOffTimes {
    const utcOffSet: number = this.selectedUser.SchoolTimeZoneOffSetHours;
    return {
      EarlyCutOffTime: convertSchoolDateTimeToLocalDateTime(cutOffTimes.EarlyCutOffTime, utcOffSet),
      LateCutOffTime: convertSchoolDateTimeToLocalDateTime(cutOffTimes.LateCutOffTime, utcOffSet),
      MenuCutOffTime: convertSchoolDateTimeToLocalDateTime(cutOffTimes.MenuCutOffTime, utcOffSet),
    };
  }

  getPageTitle(): string {
    const menuName = GetCustomMenuName(this.orderData.Order.MenuType, this.orderData.Order.MenuName);
    return `${moment(this.orderData.Order.OrderDate).format('dddd DD/MM')} ${menuName} Order`;
  }

  ngOnDestroy(): void {
    if (this.subscriptionFamilyState$) {
      this.subscriptionFamilyState$.unsubscribe();
    }
    if (this.subscriptionStudentsState$) {
      this.subscriptionStudentsState$.unsubscribe();
    }
  }

  processOrderData(order: RefinedOrder): void {
    if (!order || order?.Items?.length <= 0) {
      this.GoBackClick();
      return;
    }
    this.isProcessing = this.orderData.Order.OrderStatusId == OrderStatusEnum.Draft;
    this.isUniformOrder = this.orderData.Order.MenuType === MenuTypeEnum.Uniform;
    this.hasError = this.orderData.Order.OrderStatusId === OrderStatusEnum.Error;
    this.orderStatusDisplay = this.getOrderStatusToDisplay();
    this.titlePage = this.getPageTitle();
    this.listProducts = order.Items;
    this.orderFee = order.OrderFee;
    this.orderPrice = this.getTotalPrice();
  }

  getOrderStatusToDisplay(): string {
    if (this.hasError) {
      this.errorMessage = this.orderData.Order.ErrorMessage;
      return 'Error Occurred';
    }

    return this.isProcessing
      ? 'Processing'
      : this.orderHasBeenCancelled()
      ? 'Cancelled'
      : this.getOrderStatusText();
  }

  getOrderStatusText(): string {
    return this.isUniformOrder
      ? this.getUniformStatusToDisplay(this.orderData.Order.CanteenStatus)
      : 'Completed';
  }

  orderHasBeenCancelled(): boolean {
    return this.orderData.Order.OrderStatusId === OrderStatusEnum.Cancelled;
  }

  getUniformStatusToDisplay(status: string): string {
    return status === CanteenStatusEnum.New ? 'Ordered' : status;
  }

  getTotalPrice(): number {
    const orderPrice = this.orderData.Order?.Price || 0;
    const orderFee = this.orderData.Order?.OrderFee || 0;
    return orderPrice + orderFee;
  }

  GetPriceItem(item: MenuItem): number {
    let temp = Object.assign(new MenuItem(), item);
    return temp.GetPriceItemWithOption();
  }

  GoBackClick(): void {
    this.location.back();
  }

  ClickEditOrder(): void {
    if (this.cutOffTimePassed()) {
      return;
    }
    this.createOrderService.getAndSetDayDetail(
      this.day.MenuType,
      this.day.MenuName,
      this.day.MenuId,
      this.day.Date,
      this.selectedUser,
      moment(this.orderData.CutOffTimes.MenuCutOffTime).toDate(),
      this.orderData.Order
    );
    this.createOrderService.parentCreateOrder();
  }

  cutOffTimePassed(): boolean {
    const showCutOffWarning =
      !this.isUniformOrder && this.hasOrderEarlyCutOffTimePassed(this.orderData.CutOffTimes);
    if (showCutOffWarning) {
      this.showCutOffTimeDialog();
    }
    return showCutOffWarning;
  }

  ClickCancel(): void {
    if (this.cutOffTimePassed()) {
      return;
    }

    this.dialog.open(DialogCancelOrderComponent, {
      width: '500px',
      disableClose: true,
      data: this.orderData.Order,
    });
  }

  orderAgainSheetRequest(): OrderAgainSheetData {
    const order = this.orderData.Order;
    const earlyCutOffTime = this.orderData.CutOffTimes.EarlyCutOffTime;
    const orderAaginData: ReOrderSummary = {
      StudentId: order.StudentId,
      MenuId: order.MenuId,
    };
    const request: ReOrdersSummaryRequest = { Orders: [orderAaginData] };

    return {
      menuName: this.day.MenuName,
      dateRequest: request,
      OrderItems: order.Items,
      EarlyCutOffTime: earlyCutOffTime,
    };
  }

  ClickOrderAgain(): void {
    const sheetData = this.orderAgainSheetRequest();

    const dateSelectionSheetRef = this._bottomSheet.open(ReorderFilterComponent, { data: sheetData });

    dateSelectionSheetRef.afterDismissed().subscribe((selectedDates: string[]) => {
      if (selectedDates) {
        this.goToOrderAgainCheckout(selectedDates);
      }
    });
  }

  goToOrderAgainCheckout(dateList: string[]): void {
    const cartItems = this.getAllCartsData(dateList);
    const groupedCarts = GetGroupedShoppingCart(cartItems);
    const data: PlaceOrderDialogData = {
      editOrderId: null,
      groupedCarts,
    };
    this.openCheckoutDialog(data);
  }

  openCheckoutDialog(placeOrderData: PlaceOrderDialogData): void {
    let dialogRef;

    if (this.deviceService.isMobile()) {
      dialogRef = this.showFullScreenCheckoutDialog(placeOrderData);
    } else {
      dialogRef = this.dialog.open(DialogPlaceOrderComponent, {
        width: '500px',
        disableClose: true,
        data: placeOrderData,
      });
    }

    dialogRef.afterClosed().subscribe((errorResult: boolean) => {
      if (errorResult) {
        this.showErrorDialog();
        return;
      }
    });
  }

  showFullScreenCheckoutDialog(placeOrderData: PlaceOrderDialogData) {
    return this.dialog.open(DialogPlaceOrderComponent, {
      maxWidth: '100vw',
      maxHeight: '100vh',
      height: '100%',
      width: '100%',
      panelClass: 'custom-dialog-container',
      disableClose: true,
      data: placeOrderData,
    });
  }

  getAllCartsData(dateList: string[]): CartItem[] {
    const cutOffTime = this.orderData.CutOffTimes.MenuCutOffTime;
    const order = this.orderData.Order;
    const cartItemsWithOrderAgainDates = order.Items.map((item: RefinedOrderItem, index: number) =>
      dateList.map(date =>
        ConvertOrderItemToCartType(cutOffTime, order, index, this.selectedUser.FirstName, date)
      )
    );

    return cartItemsWithOrderAgainDates.flat();
  }

  showErrorDialog(): void {
    let data = new ConfirmModal();
    data.Title = `Something went wrong`;
    data.Text = `Try again in a minute. If it’s still not working give Member Help a shout.`;
    data.ConfirmButton = 'Ok';

    this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });
  }

  canCancelOrder(): boolean {
    if (this.orderHasBeenCancelled()) return false;

    return this.isUniformOrder ? this.canCancelUniformOrder() : this.canCancelEventOrCanteenOrder();
  }

  canCancelEventOrCanteenOrder(): boolean {
    return !this.isProcessing && !this.hasOrderEarlyCutOffTimePassed(this.orderData.CutOffTimes);
  }

  canCancelUniformOrder(): boolean {
    return (
      this.orderData.Order.CanteenStatus === CanteenStatusEnum.New ||
      this.orderData.Order.OrderStatusId === OrderStatusEnum.Error
    );
  }

  canTryAgainOrder() {
    return this.hasError && !this.hasOrderEarlyCutOffTimePassed(this.orderData.CutOffTimes);
  }

  canEditOrder(): boolean {
    if (this.orderCannotBeChanged()) return false;
    if (this.isUniformOrder) return this.orderStatusDisplay === 'Ordered';
    return !this.hasOrderEarlyCutOffTimePassed(this.orderData.CutOffTimes);
  }

  hasOrderEarlyCutOffTimePassed(cutOffTimes: CutOffTimes | undefined): boolean {
    if (!cutOffTimes?.EarlyCutOffTime) {
      return false;
    }
    return DateHasPassed(cutOffTimes.EarlyCutOffTime);
  }

  isBeforeOrderAgainCutOff(): boolean {
    const orderDate = moment(this.orderData.Order.OrderDate);
    const cutOffDate = moment().set({
      year: ORDER_AGAIN_CUT_OFF_YEAR,
      month: ORDER_AGAIN_CUT_OFF_MONTH,
      date: ORDER_AGAIN_CUT_OFF_DAY,
      hour: 0,
      minute: 0,
      second: 0,
    });
    return orderDate.isBefore(cutOffDate);
  }

  canOrderAgain(): boolean {
    if (this.orderCannotBeChanged()) return false;
    const hideOrderAgain = this.disableOrderAgainButton || this.isBeforeOrderAgainCutOff();
    return this.isCanteenOrder() && !hideOrderAgain;
  }

  orderCannotBeChanged(): boolean {
    return this.hasError || this.isProcessing || this.orderHasBeenCancelled();
  }

  isCanteenOrder(): boolean {
    return (
      this.orderData.Order.MenuType === MenuTypeEnum.Recess ||
      this.orderData.Order.MenuType === MenuTypeEnum.Lunch
    );
  }

  showCutOffTimeDialog(): void {
    const data = new checkoutErrorModal();
    data.Title = 'Order cannot be changed';
    data.Text = 'Changes cannot be made to an order after the cut off time.';

    this.dialog.open(DialogCutOffTimeComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });
  }
}
