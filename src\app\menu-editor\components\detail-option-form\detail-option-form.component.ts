import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Location } from '@angular/common';

// Services
import { SpinnerService, MenuEditorApiService } from 'src/app/sharedServices';

// Models
import { BaseComponent, Option, OptionsDisplayEnum } from 'src/app/sharedModels';
import { MatDialog } from '@angular/material/dialog';
import { MatRadioChange } from '@angular/material/radio';
import { DialogDeleteMenuItemComponent } from '../dialog-delete-menu-item/dialog-delete-menu-item.component';

@Component({
  selector: 'editor-detail-option-form',
  templateUrl: './detail-option-form.component.html',
  styleUrls: ['./detail-option-form.component.scss'],
})
export class DetailOptionFormComponent extends BaseComponent implements OnInit {
  @Input() option: Option;
  @Output() optionChange = new EventEmitter();
  OptionsDisplayEnum = OptionsDisplayEnum;
  displayMode: string;
  displayOption: string;

  selectionRules = [
    {
      title: 'Required - 1 selection',
      description: 'Must select 1 option',
    },
    {
      title: 'Required - Multiple selections ',
      description: 'Must select 1 or more options',
    },
    {
      title: 'Optional - 1 selection',
      description: 'Can only select 1 option',
    },
    {
      title: 'Optional - Multiple selections',
      description: 'Can select 1 or more options',
    },
  ];
  selectedRuleIndex: number = null;

  constructor(
    private spinnerService: SpinnerService,
    private menuEditorAPIService: MenuEditorApiService,
    public dialog: MatDialog,
    private _location: Location
  ) {
    super();
  }

  onRuleChange(index) {
    if (this.selectedRuleIndex === index) {
      this.selectedRuleIndex = null;
    } else {
      this.selectedRuleIndex = index;
    }
  }

  ngOnInit() {
    if (this.option.DisplayMode) {
      this.displayMode = this.option.DisplayMode;
    } else {
      this.displayMode = OptionsDisplayEnum.Chips;
    }
    if (this.option.IsRequired == true && this.option.IsOnlyOne == true) {
      this.selectedRuleIndex = 0;
    }
    if (this.option.IsRequired == true && this.option.IsOnlyOne == false) {
      this.selectedRuleIndex = 1;
    }
    if (this.option.IsRequired == false && this.option.IsOnlyOne == true) {
      this.selectedRuleIndex = 2;
    }
    if (this.option.IsRequired == false && this.option.IsOnlyOne == false) {
      this.selectedRuleIndex = 3;
    }
  }

  GetTextButton(): string {
    return this.option.MenuItemOptionsCategoryId > 0 ? 'Save' : 'Add';
  }

  ModeChanged(event: MatRadioChange) {
    this.displayMode = event.value;
  }

  SubmitForm() {
    this.spinnerService.start();
    this.option.DisplayMode = this.displayMode;
    this.option.IsRequired = this.selectedRuleIndex === 0 || this.selectedRuleIndex === 1 ? true : false;
    this.option.IsOnlyOne = this.selectedRuleIndex === 0 || this.selectedRuleIndex === 2 ? true : false;

    this.menuEditorAPIService.UpsertOptionAPI(this.option).subscribe({
      next: (response: Option) => {
        // success
        this.option = response;
        this.optionChange.emit(this.option);
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  ArchiveMenuItem(event) {
    this.dialog.open(DialogDeleteMenuItemComponent, {
      data: {
        displayOption: 'option',
        onDeletePress: () => {
          const deletedItemData = Object.assign(new Option(), { ...this.option, IsArchived: true });
          this.spinnerService.start();
          this.menuEditorAPIService.ArchiveMenuOptionAPI(deletedItemData).subscribe({
            next: (response: Option) => {
              this.option = response;
              this.spinnerService.stop();
              this.GoBackClick();
            },
            error: error => {
              this.spinnerService.stop();
              this.handleErrorFromService(error);
            },
          });
        },
      },
    });
  }

  CanDeleteItem() {
    return this.option.MenuItemOptionsCategoryId > 0;
  }

  GoBackClick() {
    this._location.back();
  }
}
