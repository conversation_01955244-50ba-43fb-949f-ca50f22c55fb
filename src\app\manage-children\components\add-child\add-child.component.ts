import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';

import { AllergiesModel, AllergyCheckBoxData, allergiesData } from '../../../sharedModels';

// Ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { FamilyState } from '../../../states';
import { selectedChild } from '../../../states/children/children.selectors';
import { familyLoadChildren } from '../../../states/children/children.actions';

import {
  BaseComponent,
  UserCashless,
  Address,
  Roles,
  School,
  SchoolClass,
  ListClasses,
  SchoolFeatureEnum,
  ConfirmModal,
} from '../../../sharedModels';
import { SpinnerService, UserService, SchoolClassesService, StudentService } from '../../../sharedServices';
import { DialogConfirmComponent } from 'src/app/shared/components';

@Component({
  selector: 'manage-children-add-child',
  templateUrl: './add-child.component.html',
  styleUrls: ['./add-child.component.scss'],
})
export class AddChildComponent extends BaseComponent implements OnInit, OnDestroy {
  listSchools: School[] = [];
  listClass: SchoolClass[];
  errorAPI: any;
  form: FormGroup;
  textSubmit: string;
  showButtonCancel: boolean;
  IsEdit: boolean = false;
  private _schoolId: number;
  private _studentToEdit: UserCashless;
  private subscriptionStudent$: Subscription;
  ALLERGY_DATA: AllergiesModel[] = allergiesData;
  allergyForm: FormGroup;

  constructor(
    private store: Store<{ family: FamilyState }>,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private spinnerService: SpinnerService,
    private userService: UserService,
    private classesService: SchoolClassesService,
    private studentService: StudentService,
    public dialog: MatDialog,
    private formBuilder: FormBuilder
  ) {
    super();
  }

  ngOnInit() {
    let temp = this.activatedRoute.snapshot.data['schools'];
    this.listSchools = temp.schools;
    this._studentToEdit = new UserCashless();

    // If Edit
    if (this.router.url.indexOf('/edit') > -1) {
      this.subscriptionStudent$ = this.store
        .pipe(select(selectedChild))
        .subscribe((student: UserCashless) => {
          this._studentToEdit = student;
          this.IsEdit = true;

          if (!this._studentToEdit) {
            this._studentToEdit = new UserCashless();
            this.GoBack();
          }
          this.CreateForm();
        });
    } else {
      this.CreateForm();
    }
  }

  ngOnDestroy(): void {
    this.subscriptionStudent$?.unsubscribe();
  }

  GoBack() {
    this.spinnerService.start();
    this.router.navigate(['/family/children'], { relativeTo: this.activatedRoute });
  }

  ////////////////////////////////////////
  // Form
  ////////////////////////////////////////
  get firstName() {
    return this.form.get('firstName');
  }

  get lastName() {
    return this.form.get('lastName');
  }

  get school() {
    return this.form.get('school');
  }

  get class() {
    return this.form.get('class');
  }

  get allowCanteen() {
    return this.form.get('allowCanteen');
  }

  get favouriteColour() {
    return this.form.get('favouriteColour');
  }

  get allergyFormArray() {
    return this.allergyForm.get('allergies') as FormArray;
  }

  getErrorMessageFirstName() {
    return this.firstName.hasError('required') ? 'You must enter a value' : '';
  }

  getErrorMessageLastName() {
    return this.lastName.hasError('required') ? 'You must enter a value' : '';
  }

  getErrorMessageClass() {
    return this.class.hasError('required') ? 'You must enter a value' : '';
  }

  CreateForm() {
    this._schoolId = this._studentToEdit.SchoolId;

    if (!this._studentToEdit.FavouriteColour || this._studentToEdit.FavouriteColour == '') {
      let studentCopy = Object.assign(new UserCashless(), this._studentToEdit);
      studentCopy.FavouriteColour = 'blue';
      this._studentToEdit = studentCopy;
    }

    this.form = new FormGroup({
      firstName: new FormControl(this._studentToEdit.FirstName, [Validators.required]),
      lastName: new FormControl(this._studentToEdit.Lastname, [Validators.required]),
      school: new FormControl(this._studentToEdit.SchoolId),
      class: new FormControl(this._studentToEdit.ClassId, [Validators.required]),
      allowCanteen: new FormControl(this._studentToEdit.AllowCanteenToOrder),
      favouriteColour: new FormControl(this._studentToEdit.FavouriteColour),
    });

    this.generateAllergyForm();

    // on value change
    this.school.valueChanges.subscribe(newValue => {
      this._schoolId = newValue;
      this.LoadClass(newValue);
    });

    // If edit we need to load the class from the start
    if (this.IsEdit && this._studentToEdit) {
      this.LoadClass(this._studentToEdit.SchoolId);
    }
  }

  generateAllergyForm() {
    //generate dynamic allergy form
    this.allergyForm = this.formBuilder.group({
      allergies: new FormArray([]),
    });

    // Create form control for each allergy checkbox value
    this.ALLERGY_DATA.forEach(allergy => {
      this.allergyFormArray.push(new FormControl(this._studentToEdit?.Allergies?.includes(allergy.code)));
    });
  }

  /** Load Class */
  private LoadClass(schoolId: number) {
    if (schoolId) {
      this.spinnerService.start();
      this.classesService.GetClassesBySchoolAPI(schoolId, true).subscribe({
        next: (response: ListClasses) => {
          this.listClass = response.Classes;
          this.spinnerService.stop();
        },
        error: error => {
          this.handleErrorFromService(error);
          this.spinnerService.stop();
        },
      });
    }
  }

  ////////////////////////////////////////
  // Add User
  ////////////////////////////////////////
  onSubmit() {
    this.spinnerService.start();

    let connectedUser = this.userService.GetUserConnected();

    this.userService.UpsertUser(this.convertObject()).subscribe({
      next: (user: UserCashless) => {
        // init form
        this.CreateForm();

        // refreshStore
        this.store.dispatch(familyLoadChildren({ parentId: connectedUser.UserId }));

        this.GoBack();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      },
    });
  }

  private convertObject(): UserCashless {
    // Get parent Id
    let connectedUser = this.userService.GetUserConnected();

    if (connectedUser != null) {
      let initUser = new UserCashless();
      initUser.Address = new Address();

      if (this.IsEdit) {
        initUser = this._studentToEdit;
      }

      let user = Object.assign(new UserCashless(), initUser);

      user.FirstName = this.firstName.value;
      user.Lastname = this.lastName.value;
      user.ClassId = this.class.value;
      user.SchoolId = this.school.value;
      user.FirebaseUserId = '';
      user.AllowCanteenToOrder = this.allowCanteen.value;
      user.FavouriteColour = this.favouriteColour.value;
      user.Allergies = this.getAllergyString();

      if (!this.IsEdit) {
        user.ParentId = connectedUser.UserId;
        user.Role = Roles.Child;
      }

      return user;
    }
  }

  getAllergyString(): string {
    const processedAllergyValues: AllergyCheckBoxData[] = this.allergyFormArray.value.map(
      (value: boolean, index: number) => ({
        allergyCode: this.ALLERGY_DATA[index].code,
        selected: value,
      })
    );

    let selectedAllergyString = processedAllergyValues
      .filter(allergy => allergy.selected)
      .map(allergy => allergy.allergyCode)
      .join(',');

    return selectedAllergyString;
  }

  CancelForm() {
    this.CreateForm();
    this.GoBack();
  }

  GetBackgroundColor() {
    return 'background-color:' + this.favouriteColour.value + ';';
  }

  HasPayAtCanteenFeature(): boolean {
    let res = false;

    if (this._schoolId) {
      let school = this.listSchools.find(x => x.SchoolId == this._schoolId);

      if (school.SchoolFeatures) {
        let hasFeature = school.SchoolFeatures.findIndex(x => x.OptionName == SchoolFeatureEnum.PayAtCanteen);
        res = hasFeature >= 0;
      }
    }
    return res;
  }

  HasAllergyAlertFeature(): boolean {
    let res = false;

    if (this._schoolId) {
      let school = this.listSchools.find(x => x.SchoolId == this._schoolId);

      if (school.SchoolFeatures) {
        let hasFeature = school.SchoolFeatures.findIndex(x => x.OptionName == SchoolFeatureEnum.AllergyAlert);
        res = hasFeature >= 0;
      }
    }
    return res;
  }

  ////////////////////////// Archive child
  ArchiveClicked() {
    let data = new ConfirmModal();
    data.Title = 'Delete Child';
    data.Text =
      'Deleting will inactivate the child profile permanently and cannot be undone. Do you want to proceed?';
    data.CancelButton = 'No';
    data.ConfirmButton = 'Yes';

    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.ArchiveClickConfirmed();
      }
    });
  }

  ArchiveClickConfirmed() {
    this.spinnerService.start();

    this.studentService.ArchiveStudentAPI(this._studentToEdit.UserId).subscribe({
      next: (response: any) => {
        this.GoBack();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      },
    });
  }

  CanArchive() {
    return this._studentToEdit && this._studentToEdit.UserId > 0;
  }
}
