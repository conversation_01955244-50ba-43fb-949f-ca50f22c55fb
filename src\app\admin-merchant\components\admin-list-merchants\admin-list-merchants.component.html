<div class="container-fluid">
  <!-- Header -->
  <div class="row pt-5 pb-4">
    <div class="col-6">
      <div [ngClass]="{ disableCoverGrey: disableMode }"></div>
      <school-header title="Merchants"></school-header>
    </div>
    <div class="col-6 d-flex justify-content-end">
      <div [ngClass]="{ disableCoverGrey: disableMode }"></div>
      <basic-button
        text="Create Merchant"
        (onPress)="createMerchantClick()"
        [buttonStyle]="1"
        class="mr-3"
      ></basic-button>
      <basic-button text="Finance Reports" (onPress)="financeReportClick()" [buttonStyle]="1"></basic-button>
    </div>
  </div>

  <!-- Content -->
  <div class="row">
    <div class="col-12 searchResults">
      <!-- search result tabs -->
      <div [ngClass]="{ disableCoverTab: disableMode }"></div>
      <div class="tabContainer" [ngClass]="{ tabContainerOpen: selectedMerchant, stopScroll: disableMode }">
        <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 tableau accountTable">
          <ng-container matColumnDef="name">
            <td
              mat-cell
              *matCellDef="let element"
              class="result-tab"
              [ngClass]="{ selectedTab: selectedMerchant && selectedMerchant.canteenId == element.canteenId }"
              (click)="selectMerchant(element)"
            >
              <div class="result-tab-inner">
                <h5
                  [ngClass]="{
                    selectedTabText: selectedMerchant && selectedMerchant.canteenId == element.canteenId
                  }"
                  class="result-title"
                >
                  {{ element.merchantName }}
                </h5>
                <p class="result-subtitle">{{ element.ownerName }}</p>
              </div>
              <img class="chevron" src="assets/icons/orange-arrow-right.svg" alt="chevron" />
            </td>
          </ng-container>

          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      </div>

      <!-- Merchant details Section -->
      <div *ngIf="selectedMerchant" class="result-details">
        <merchant-contact-details [contactDetails]="contactDetails"></merchant-contact-details>

        <merchant-details [merchantDetails]="merchantDetails"></merchant-details>

        <merchant-linked-schools-table
          (stopLoad)="loadCheck()"
          [merchantType]="merchantType"
        ></merchant-linked-schools-table>

        <div #userPermissions>
          <merchant-user-permissions-table (stopLoad)="loadCheck()"></merchant-user-permissions-table>
        </div>
      </div>
    </div>
  </div>
</div>
