import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

// components
import { MerchantNavbarComponent } from '../shared/components/merchant-navbar/merchant-navbar.component';
import {
  CanteenComponent,
  LabelPrintComponent,
  IosLabelsPrintingComponent,
  NoticeBoardComponent,
  AnnouncementsComponent,
  ArticlesComponent,
} from './components';

// Shared tools components
import { ListAccountComponent, UserDetailsPageComponent } from '../shared-tools/components';
import { MenuEditorGuardService } from '../sharedServices/canteen/menu-editor-guard.service';

// Services
import { ListCanteensResolver, UserDetailsResolver } from '../sharedServices';
import { SalesReportGuardService } from '../sharedServices/canteen/sales-report-guard.service';
import { eventManagementRoutes } from '../event-management/event-management.routes';

const routes: Routes = [
  {
    path: '',
    component: MerchantNavbarComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'home',
      },
      {
        path: 'home',
        component: CanteenComponent,
        resolve: { canteens: ListCanteensResolver },
      },
      {
        path: 'order',
        loadChildren: () => import('../manage-order/manage-order.module').then(m => m.ManageOrderModule),
      },
      {
        path: 'settings',
        loadChildren: () =>
          import('../canteen-settings/canteen-settings.module').then(m => m.CanteenSettingsModule),
      },
      {
        path: 'reports',
        loadChildren: () => import('../reports/reports.module').then(m => m.ReportsModule),
      },
      {
        path: 'students',
        component: ListAccountComponent,
        resolve: {
          canteens: ListCanteensResolver,
        },
      },
      {
        path: 'students/details/:id',
        component: UserDetailsPageComponent,
        resolve: { user: UserDetailsResolver },
      },
      {
        path: 'labels',
        component: LabelPrintComponent,
      },
      {
        path: 'notice',
        component: NoticeBoardComponent,
      },
      {
        path: 'notice/announcements',
        component: AnnouncementsComponent,
      },
      {
        path: 'notice/articles',
        component: ArticlesComponent,
      },
      {
        path: 'editor',
        loadChildren: () => import('../menu-editor/menu-editor.module').then(m => m.MenuEditorModule),
        canActivateChild: [MenuEditorGuardService],
      },
      {
        path: 'events',
        loadChildren: () => eventManagementRoutes,
        resolve: { merchants: ListCanteensResolver },
      },
      {
        path: 'pos',
        loadChildren: () => import('../pos/pos.module').then(m => m.PosModule),
      },
    ],
  },
  { path: 'iosPrint/:displaySchoolName', component: IosLabelsPrintingComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [MenuEditorGuardService, SalesReportGuardService],
})
export class CanteenRoutingModule {}
