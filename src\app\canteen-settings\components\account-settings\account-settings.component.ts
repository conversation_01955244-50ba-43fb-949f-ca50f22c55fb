import { Component, OnInit } from '@angular/core';
import { Store, select } from '@ngrx/store';
import { CanteenState } from 'src/app/states';
import { selectedCanteen } from 'src/app/states/canteen/canteen.selectors';
import { Canteen } from 'src/app/sharedModels';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-account-settings',
  templateUrl: './account-settings.component.html',
  styleUrls: ['./account-settings.component.scss'],
})
export class AccountSettingsComponent implements OnInit {

  isAdmin: boolean;
  private subscription: Subscription;

  constructor(private store: Store<{ canteen: CanteenState }>) {}

  ngOnInit() {
    this.subscription = this.store.pipe(select(selectedCanteen)).subscribe((state: Canteen) => {
      this.isAdmin = Boolean(state.IsAdmin);
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
