<school-panel fullHeight="true" title="Parent">
  <div class="parent-wrapper">
    <table mat-table [dataSource]="dataSource" class="children-table">
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef>Name:</th>
        <td mat-cell *matCellDef="let element">{{ element.FirstName + ' ' + element.Lastname }}</td>
      </ng-container>

      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef>User ID:</th>
        <td mat-cell *matCellDef="let element">
          <a [routerLink]="['../../', element.UserId]">{{ element.UserId }}</a>
        </td>
      </ng-container>

      <ng-container matColumnDef="mobile">
        <th mat-header-cell *matHeaderCellDef>Mobile:</th>
        <td mat-cell *matCellDef="let element">{{ element.Mobile }}</td>
      </ng-container>

      <ng-container matColumnDef="email">
        <th mat-header-cell *matHeaderCellDef>Email:</th>
        <td mat-cell *matCellDef="let element">{{ element.Email }}</td>
      </ng-container>

      <ng-container matColumnDef="balance">
        <th mat-header-cell *matHeaderCellDef>Balance:</th>
        <td mat-cell *matCellDef="let element">{{ element.SpriggyBalance | currency }}</td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
  </div>
</school-panel>
