import { inject } from '@angular/core';

import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';

import { Observable } from 'rxjs';
import { SchoolClassesService } from '../../sharedServices';

export const SchoolClassResolver: ResolveFn<any> = (route: ActivatedRouteSnapshot): Observable<any> => {
  const classesService = inject(SchoolClassesService);
  const schoolId = route.queryParams['schoolId'];

  return schoolId ? classesService.GetClassesBySchoolAPI(schoolId, true) : null;
};
