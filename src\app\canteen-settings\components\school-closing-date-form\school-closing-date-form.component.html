<div class="col-md-6 col-lg-4" *ngIf="form">
  <h3 class="optionTitle">School Closing Dates</h3>

  <div [formGroup]="form" class="pt-3 pb-3">
    <input-date placeholder="Start Date" formControlName="closeStartDate"></input-date>
    <input-date placeholder="End Date" formControlName="closeEndDate"></input-date>
  </div>
</div>

<basic-button text="Save" (onPress)="submitForm()"></basic-button>

<div class="col-12">
  <div *ngIf="schoolClosingDates?.length; else noDateMessage">
    <school-close-date-table
      [dates]="schoolClosingDates"
      (archive)="archiveClicked($event)"
    ></school-close-date-table>
  </div>

  <ng-template #noDateMessage>
    <p>Your school currently doesn't have any closing dates</p>
  </ng-template>
</div>
