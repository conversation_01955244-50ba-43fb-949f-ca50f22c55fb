@import 'src/styles/schools-table';
@import 'src/styles/schools-theme';

::ng-deep .mat-mdc-dialog-container {
  border-radius: 12px !important;
}

/* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
.radio-button-wrapper ::ng-deep .mat-mdc-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element {
  background-color: $link-default;
}

/* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
.radio-button-wrapper ::ng-deep .mat-mdc-radio-button.mat-accent .mat-radio-inner-circle {
  background-color: $link-default;
}

/* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
.radio-button-wrapper ::ng-deep.mat-mdc-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border-color: $link-default;
}
