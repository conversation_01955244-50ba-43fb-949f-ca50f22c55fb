import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

// Services
import { MenuEditorApiService, SpinnerService } from 'src/app/sharedServices';

@Component({
  selector: 'editor-image-upload',
  templateUrl: './image-upload.component.html',
  styleUrls: ['./image-upload.component.scss'],
})
export class ImageUploadComponent implements OnInit {
  @Input() menuItemId: number;
  @Input() schoolCode: string;
  @Input() showError: boolean = false;
  @Output() newImage: EventEmitter<string> = new EventEmitter<string>();

  fileToUpload: File = null;
  files: Array<any> = new Array<any>();
  url: string = '';

  constructor(private menuEditorAPIService: MenuEditorApiService, private spinnerService: SpinnerService) {}

  ngOnInit() {}

  onSelectFile(files: FileList) {
    if (files.length === 0) return;

    this.fileToUpload = files.item(0);
    let file = new File([this.fileToUpload], this.fileToUpload.name.slice());
    this.fileToUpload = file;
    const fileReader: FileReader = new FileReader();
    fileReader.readAsDataURL(this.fileToUpload);

    fileReader.onload = (event: any) => {
      this.url = event.target.result;
    };
  }

  Upload() {
    this.showError = false;
    this.spinnerService.start();
    this.files.push({ data: this.fileToUpload, fileName: this.fileToUpload.name });

    // not using schoolcode anymore, use canteenid now
    this.menuEditorAPIService
      .uploadItemImage(this.files[0], this.menuItemId, this.schoolCode)
      .subscribe(
        response => {
          if (response && response.body != '') {
            this.newImage.emit(response.body);
            this.url = null;
            this.files = new Array<any>();
          } else {
            this.showError = true;
          }
          this.spinnerService.stop();
        },
        error => {
          this.showError = true;
          this.spinnerService.stop();
        }
      );
  }
}
