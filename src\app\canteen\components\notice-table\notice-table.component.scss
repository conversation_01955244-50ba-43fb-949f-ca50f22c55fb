@import '../../../../styles/cashless-theme.scss';

.mat-elevation-z8 {
  box-shadow: 0 0 #000000;
}

.title {
  font-size: 24px;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 15px;
  color: $grey-11;
  width: 30%;
}

.table {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 30px;
}

.tableElement {
  padding-top: 10px;
  padding-bottom: 10px;
  padding-right: 30px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 0px;
}

.titleCenter {
  font-size: 24px;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 15px;
  color: $grey-11;
  width: 65%;
}

.rowElement {
  height: 61px;
}

.rowElement:hover {
  background-color: $orange-9;
  cursor: pointer;
}

.checkBox {
  color: $orange-3;
}
